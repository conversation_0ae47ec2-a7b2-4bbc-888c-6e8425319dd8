#!/usr/bin/env python3
"""
简单的状态检查脚本
"""

import sys
import os
import pymysql

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config


def check_current_status():
    """检查当前状态"""
    print("=" * 60)
    print("当前数据库状态检查")
    print("=" * 60)
    
    try:
        db_config = config.get_db_config()
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        table_config = config.get_table_config()
        case_relation_table = table_config['case_relation_table']
        
        # 1. 总体状态分布
        print("1. 总体状态分布:")
        cursor.execute(f'SELECT status, COUNT(*) as count FROM `{case_relation_table}` GROUP BY status ORDER BY status')
        results = cursor.fetchall()
        
        status_desc = {
            0: '初始状态',
            1: 'PDF合并完成',
            2: 'OCR识别完成', 
            3: '要素提取完成',
            4: '处理失败'
        }
        
        total_cases = 0
        for status, count in results:
            desc = status_desc.get(status, f'状态{status}')
            print(f"  STATUS={status} ({desc}): {count} 个案件")
            total_cases += count
        
        print(f"  总案件数: {total_cases}")
        
        # 2. 最新批次信息
        print(f"\n2. 最新批次信息:")
        cursor.execute(f'''
            SELECT batchid, status, COUNT(*) as count, MAX(updatetime) as last_update
            FROM `{case_relation_table}` 
            WHERE batchid IS NOT NULL
            GROUP BY batchid, status 
            ORDER BY last_update DESC 
            LIMIT 10
        ''')
        
        batch_results = cursor.fetchall()
        
        if batch_results:
            current_batch = None
            for row in batch_results:
                batchid, status, count, last_update = row
                if current_batch != batchid:
                    if current_batch is not None:
                        print()  # 空行分隔不同批次
                    current_batch = batchid
                    print(f"  批次 {batchid}:")
                
                desc = status_desc.get(status, f'状态{status}')
                print(f"    STATUS={status} ({desc}): {count} 个案件, 更新时间: {last_update}")
        
        # 3. STATUS=2的详细信息
        print(f"\n3. STATUS=2 (OCR完成) 案件详情:")
        cursor.execute(f'''
            SELECT ajbh, batchid, updatetime, 
                   CASE WHEN ajnr IS NULL THEN 0 ELSE CHAR_LENGTH(ajnr) END as content_length
            FROM `{case_relation_table}` 
            WHERE status = '2' 
            ORDER BY updatetime DESC 
            LIMIT 5
        ''')
        
        status2_results = cursor.fetchall()
        
        if status2_results:
            print(f"  找到 {len(status2_results)} 个待要素提取案件（显示前5个）:")
            for ajbh, batchid, updatetime, content_length in status2_results:
                print(f"    {ajbh}: 批次={batchid}, 内容长度={content_length}, 时间={updatetime}")
        else:
            print(f"  ✅ 没有STATUS=2的案件")
        
        # 4. STATUS=3的详细信息
        print(f"\n4. STATUS=3 (要素提取完成) 案件详情:")
        cursor.execute(f'''
            SELECT ajbh, batchid, updatetime
            FROM `{case_relation_table}` 
            WHERE status = '3' 
            ORDER BY updatetime DESC 
            LIMIT 5
        ''')
        
        status3_results = cursor.fetchall()
        
        if status3_results:
            print(f"  找到 {len(status3_results)} 个已完成案件（显示前5个）:")
            for ajbh, batchid, updatetime in status3_results:
                print(f"    {ajbh}: 批次={batchid}, 时间={updatetime}")
        else:
            print(f"  ❌ 没有STATUS=3的案件")
        
        cursor.close()
        connection.close()
        
        # 5. 分析和建议
        print(f"\n5. 分析和建议:")
        
        status2_count = len(status2_results) if status2_results else 0
        status3_count = len(status3_results) if status3_results else 0
        
        if status2_count > 0 and status3_count == 0:
            print(f"  📊 当前状态: OCR已完成，要素提取待处理")
            print(f"  💡 建议: 运行要素提取脚本完成最后一步")
            print(f"  🚀 命令: python test_fixed_extraction.py")
        elif status3_count > 0:
            print(f"  🎉 部分案件已完成要素提取!")
            if status2_count > 0:
                print(f"  📝 还有 {status2_count} 个案件待处理")
            else:
                print(f"  ✅ 所有案件都已完成!")
        else:
            print(f"  ⚠️  没有找到已处理的案件，请检查系统状态")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    check_current_status()
