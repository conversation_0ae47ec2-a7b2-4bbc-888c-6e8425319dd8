#!/usr/bin/env python3
"""
验证Excel导入SQL结果的脚本
"""

import pymysql
import sys
from datetime import datetime

def get_connection():
    """获取数据库连接"""
    db_config = {
        'host': '***********',
        'user': 'root',
        'password': '123456',
        'database': 'djzs_db',
        'charset': 'utf8mb4'
    }
    return pymysql.connect(**db_config)

def verify_import_data(case_number=None):
    """验证导入的数据"""
    
    print("🔍 验证Excel导入SQL结果")
    print("="*50)
    
    try:
        with get_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                
                # 构建查询条件
                if case_number:
                    where_clause = "WHERE ajbh = %s"
                    params = [case_number]
                    print(f"📋 查询指定案件: {case_number}")
                else:
                    where_clause = "WHERE ajlx = 'AUTO_IMPORT' ORDER BY tbrksj DESC LIMIT 10"
                    params = []
                    print(f"📋 查询最近导入的10条记录")
                
                # 查询数据
                sql = f"""
                SELECT ajbh, xxzjbh, ajmc, flwslldz, flwsxzdz, 
                       ajlx, tbrksj, tbgxsj, llsj,
                       CHAR_LENGTH(flwsnr) as content_length
                FROM ds_case_instrument_his_ai 
                {where_clause}
                """
                
                cursor.execute(sql, params)
                results = cursor.fetchall()
                
                if not results:
                    print("⚠️  未找到匹配的记录")
                    return False
                
                print(f"✅ 找到 {len(results)} 条记录")
                print()
                
                # 显示记录详情
                for i, record in enumerate(results, 1):
                    print(f"📄 记录 {i}:")
                    print(f"   案件编号: {record['ajbh']}")
                    print(f"   数据版本号: {record['xxzjbh']}")
                    print(f"   案件名称: {record['ajmc']}")
                    print(f"   内容长度: {record['content_length']} 字符")
                    print(f"   PDF链路地址: {record['flwslldz']}")
                    print(f"   PDF下载地址: {record['flwsxzdz']}")
                    print(f"   案件类型: {record['ajlx']}")
                    print(f"   入库时间: {record['tbrksj']}")
                    print(f"   更新时间: {record['tbgxsj']}")
                    print(f"   录入时间: {record['llsj']}")
                    print()
                
                return True
                
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def get_import_statistics():
    """获取导入统计信息"""
    
    print("📊 导入统计信息")
    print("="*30)
    
    try:
        with get_connection() as connection:
            with connection.cursor() as cursor:
                
                # 总记录数
                cursor.execute("SELECT COUNT(*) as total FROM ds_case_instrument_his_ai")
                total = cursor.fetchone()[0]
                print(f"总记录数: {total}")
                
                # 自动导入记录数
                cursor.execute("SELECT COUNT(*) as auto_import FROM ds_case_instrument_his_ai WHERE ajlx = 'AUTO_IMPORT'")
                auto_import = cursor.fetchone()[0]
                print(f"自动导入记录数: {auto_import}")
                
                # 今天导入的记录数
                cursor.execute("""
                    SELECT COUNT(*) as today_import 
                    FROM ds_case_instrument_his_ai 
                    WHERE ajlx = 'AUTO_IMPORT' AND DATE(tbrksj) = CURDATE()
                """)
                today_import = cursor.fetchone()[0]
                print(f"今天导入记录数: {today_import}")
                
                # 最近导入时间
                cursor.execute("""
                    SELECT MAX(tbrksj) as last_import 
                    FROM ds_case_instrument_his_ai 
                    WHERE ajlx = 'AUTO_IMPORT'
                """)
                last_import = cursor.fetchone()[0]
                if last_import:
                    print(f"最近导入时间: {last_import}")
                
                # 内容长度统计
                cursor.execute("""
                    SELECT 
                        AVG(CHAR_LENGTH(flwsnr)) as avg_length,
                        MIN(CHAR_LENGTH(flwsnr)) as min_length,
                        MAX(CHAR_LENGTH(flwsnr)) as max_length
                    FROM ds_case_instrument_his_ai 
                    WHERE ajlx = 'AUTO_IMPORT' AND flwsnr IS NOT NULL
                """)
                length_stats = cursor.fetchone()
                if length_stats[0]:
                    print(f"内容长度统计:")
                    print(f"   平均长度: {int(length_stats[0])} 字符")
                    print(f"   最小长度: {length_stats[1]} 字符")
                    print(f"   最大长度: {length_stats[2]} 字符")
                
                return True
                
    except Exception as e:
        print(f"❌ 统计信息获取失败: {e}")
        return False

def check_pdf_links():
    """检查PDF链接格式"""
    
    print("🔗 检查PDF链接格式")
    print("="*30)
    
    try:
        with get_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                
                # 检查PDF链接格式
                cursor.execute("""
                    SELECT ajbh, xxzjbh, flwslldz, flwsxzdz
                    FROM ds_case_instrument_his_ai 
                    WHERE ajlx = 'AUTO_IMPORT' 
                    ORDER BY tbrksj DESC 
                    LIMIT 5
                """)
                
                results = cursor.fetchall()
                
                if not results:
                    print("⚠️  未找到记录")
                    return False
                
                print("📋 PDF链接检查:")
                
                for record in results:
                    case_number = record['ajbh']
                    version_number = record['xxzjbh']
                    expected_filename = f"{case_number}_{version_number}.pdf"
                    
                    flwslldz = record['flwslldz']
                    flwsxzdz = record['flwsxzdz']
                    
                    print(f"\n案件: {case_number}_{version_number}")
                    print(f"   期望文件名: {expected_filename}")
                    
                    # 检查链路地址
                    if expected_filename in flwslldz:
                        print(f"   ✅ 链路地址正确: {flwslldz}")
                    else:
                        print(f"   ❌ 链路地址错误: {flwslldz}")
                    
                    # 检查下载地址
                    if expected_filename in flwsxzdz:
                        print(f"   ✅ 下载地址正确: {flwsxzdz}")
                    else:
                        print(f"   ❌ 下载地址错误: {flwsxzdz}")
                
                return True
                
    except Exception as e:
        print(f"❌ PDF链接检查失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔍 Excel导入SQL验证工具")
    print("="*60)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取参数
    case_number = None
    if len(sys.argv) >= 2:
        case_number = sys.argv[1]
        print(f"指定案件编号: {case_number}")
    
    print()
    
    # 执行验证
    tests = [
        ("导入统计", get_import_statistics),
        ("数据验证", lambda: verify_import_data(case_number)),
        ("PDF链接检查", check_pdf_links)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"验证项目: {test_name}")
        print("="*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 验证异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("验证结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 个验证通过")
    
    if passed == total:
        print(f"\n🎉 所有验证通过！数据导入正确")
    else:
        print(f"\n⚠️  部分验证失败，请检查数据")
    
    return passed == total

if __name__ == "__main__":
    main()
