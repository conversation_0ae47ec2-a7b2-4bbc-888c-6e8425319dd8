#!/bin/bash
# 快速检查后台任务状态

echo "📊 快速状态检查"
echo "================"

echo "🔍 检查进程 65408:"
ps aux | grep 65408 | grep -v grep

echo ""
echo "🐍 查找Python相关进程:"
ps aux | grep python | grep -E "(start\.py|main_controller\.py)" | grep -v grep

echo ""
echo "📁 检查日志目录:"
if [ -d "logs" ]; then
    echo "当前目录logs/:"
    ls -la logs/
else
    echo "当前目录无logs文件夹"
fi

if [ -d "/bigai/ai/AJagent-main/logs" ]; then
    echo ""
    echo "/bigai/ai/AJagent-main/logs/:"
    ls -la /bigai/ai/AJagent-main/logs/
fi

echo ""
echo "📄 查找PID文件:"
find . -name "*.pid" 2>/dev/null
find /bigai/ai/AJagent-main -name "*.pid" 2>/dev/null

echo ""
echo "📝 查找日志文件:"
find . -name "*.log" 2>/dev/null | head -5
find /bigai/ai/AJagent-main -name "*.log" 2>/dev/null | head -5

echo ""
echo "🔧 管理命令:"
echo "停止任务: kill 65408"
echo "查看进程: ps aux | grep 65408"
echo "查看日志: tail -f /bigai/ai/AJagent-main/logs/hourly_task_*.log"
