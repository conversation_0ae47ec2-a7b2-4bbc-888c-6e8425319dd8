#!/usr/bin/env python3
"""
Excel转PDF脚本 - 修复版
专门处理Linux环境下的字体问题
"""

import pandas as pd
import os
import sys
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_to_pdf.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ExcelToPDFConverter:
    """Excel转PDF转换器 - 修复版"""
    
    def __init__(self, excel_path, output_dir):
        """
        初始化转换器
        
        Args:
            excel_path: Excel文件路径
            output_dir: PDF输出目录
        """
        self.excel_path = excel_path
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        
        # 字段配置
        self.pdf_name_fields = ['案件编号', '数据版本号']
        self.content_fields = ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明']
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 字体状态
        self.font_registered = False
        self.font_name = 'Helvetica'  # 默认字体
        
        # 尝试注册中文字体
        self._register_fonts()
    
    def _register_fonts(self):
        """注册中文字体"""
        try:
            # Linux环境下的中文字体路径
            font_paths = [
                '/usr/share/fonts/wqy-microhei/wqy-microhei.ttc',
                '/usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc',
                '/usr/share/fonts/chinese/simsun.ttf',
                '/usr/share/fonts/chinese/simhei.ttf',
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                # 用户字体目录
                os.path.expanduser('~/.fonts/wqy-microhei.ttc'),
                os.path.expanduser('~/.fonts/SourceHanSansCN-Regular.otf'),
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        self.font_registered = True
                        self.font_name = 'ChineseFont'
                        self.logger.info(f"成功注册字体: {font_path}")
                        break
                    except Exception as e:
                        self.logger.warning(f"注册字体失败 {font_path}: {e}")
                        continue
            
            if not self.font_registered:
                self.logger.warning("未找到中文字体，将使用默认字体（可能显示乱码）")
                self.font_name = 'Helvetica'
                
        except Exception as e:
            self.logger.error(f"字体注册异常: {e}")
            self.font_registered = False
            self.font_name = 'Helvetica'
    
    def read_excel(self):
        """读取Excel文件"""
        try:
            self.logger.info(f"开始读取Excel文件: {self.excel_path}")
            
            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            
            self.logger.info(f"成功读取Excel，共 {len(df)} 行数据")
            self.logger.info(f"Excel列名: {list(df.columns)}")
            
            # 检查必需字段是否存在
            missing_fields = []
            all_required_fields = self.pdf_name_fields + self.content_fields
            
            for field in all_required_fields:
                if field not in df.columns:
                    missing_fields.append(field)
            
            if missing_fields:
                self.logger.error(f"Excel中缺少必需字段: {missing_fields}")
                return None
            
            return df
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            return None
    
    def create_pdf_styles(self):
        """创建PDF样式"""
        styles = getSampleStyleSheet()
        
        try:
            # 标题样式 - 使用简单配置避免字体问题
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Normal'],  # 使用Normal而不是Title
                fontName=self.font_name,
                fontSize=16,
                spaceAfter=20,
                alignment=1,  # 居中
                leading=20
            )
            
            # 正文样式
            body_style = ParagraphStyle(
                'CustomBody',
                parent=styles['Normal'],
                fontName=self.font_name,
                fontSize=12,
                spaceAfter=12,
                leftIndent=0,
                rightIndent=0,
                leading=15
            )
            
            # 章节标题样式
            section_style = ParagraphStyle(
                'CustomSection',
                parent=styles['Normal'],
                fontName=self.font_name,
                fontSize=14,
                spaceAfter=10,
                spaceBefore=15,
                leading=18
            )
            
        except Exception as e:
            self.logger.warning(f"创建自定义样式失败，使用默认样式: {e}")
            # 使用最基本的样式
            title_style = styles['Normal']
            body_style = styles['Normal']
            section_style = styles['Normal']
        
        return {
            'title': title_style,
            'body': body_style,
            'section': section_style
        }
    
    def _clean_text(self, text):
        """清理文本，避免XML解析错误"""
        if not text:
            return ""
        
        text = str(text)
        # 替换可能导致XML解析错误的字符
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('"', '&quot;')
        text = text.replace("'", '&apos;')
        # 处理换行符
        text = text.replace('\n', '<br/>')
        text = text.replace('\r', '')
        
        return text
    
    def generate_pdf(self, row_data, pdf_path):
        """生成单个PDF文件"""
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(
                pdf_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # 获取样式
            styles = self.create_pdf_styles()
            
            # 构建PDF内容
            story = []
            
            # 添加标题
            case_number = str(row_data.get('案件编号', ''))
            version_number = str(row_data.get('数据版本号', ''))
            
            # 清理标题文本
            case_number = self._clean_text(case_number)
            version_number = self._clean_text(version_number)
            
            title = f"Case Number: {case_number} | Version: {version_number}"
            story.append(Paragraph(title, styles['title']))
            story.append(Spacer(1, 20))
            
            # 添加内容字段
            for field in self.content_fields:
                content = row_data.get(field, '')
                
                if pd.notna(content) and str(content).strip():
                    try:
                        # 添加章节标题（使用英文避免字体问题）
                        field_mapping = {
                            '正文内容': 'Main Content',
                            '到案情况': 'Case Status',
                            '依法侦查查明': 'Investigation Results',
                            '犯罪证据': 'Evidence',
                            '综上所述': 'Summary',
                            '其他说明': 'Additional Notes'
                        }
                        
                        section_title = field_mapping.get(field, field)
                        story.append(Paragraph(f"<b>{section_title}</b>", styles['section']))
                        
                        # 添加内容
                        content_text = self._clean_text(content)
                        story.append(Paragraph(content_text, styles['body']))
                        story.append(Spacer(1, 10))
                        
                    except Exception as e:
                        self.logger.warning(f"处理字段 {field} 时出错: {e}")
                        # 尝试添加简化版本
                        try:
                            simple_content = str(content)[:500] + "..." if len(str(content)) > 500 else str(content)
                            story.append(Paragraph(f"Content: {simple_content}", styles['body']))
                            story.append(Spacer(1, 10))
                        except:
                            continue
            
            # 添加生成时间
            story.append(Spacer(1, 20))
            generate_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            story.append(Paragraph(f"Generated: {generate_time}", styles['body']))
            
            # 生成PDF
            doc.build(story)
            
            return True
            
        except Exception as e:
            self.logger.error(f"生成PDF失败 {pdf_path}: {e}")
            return False
    
    def convert_all(self):
        """转换所有数据为PDF"""
        try:
            # 读取Excel数据
            df = self.read_excel()
            if df is None:
                return False
            
            success_count = 0
            error_count = 0
            
            self.logger.info(f"开始转换 {len(df)} 条记录为PDF")
            
            for index, row in df.iterrows():
                try:
                    # 获取PDF文件名
                    case_number = str(row.get('案件编号', '')).strip()
                    version_number = str(row.get('数据版本号', '')).strip()
                    
                    if not case_number or not version_number:
                        self.logger.warning(f"第 {index+1} 行数据缺少案件编号或数据版本号，跳过")
                        error_count += 1
                        continue
                    
                    # 清理文件名中的非法字符
                    case_number = self._clean_filename(case_number)
                    version_number = self._clean_filename(version_number)
                    
                    pdf_filename = f"{case_number}_{version_number}.pdf"
                    pdf_path = os.path.join(self.output_dir, pdf_filename)
                    
                    # 生成PDF
                    if self.generate_pdf(row, pdf_path):
                        success_count += 1
                        self.logger.info(f"成功生成PDF: {pdf_filename}")
                    else:
                        error_count += 1
                        
                except Exception as e:
                    self.logger.error(f"处理第 {index+1} 行数据失败: {e}")
                    error_count += 1
            
            self.logger.info(f"转换完成！成功: {success_count}, 失败: {error_count}")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"批量转换失败: {e}")
            return False
    
    def _clean_filename(self, filename):
        """清理文件名中的非法字符"""
        # 替换Windows文件名中的非法字符
        illegal_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        for char in illegal_chars:
            filename = filename.replace(char, '_')
        return filename.strip()

def main():
    """主函数"""
    
    print("📄 Excel转PDF工具 - 修复版")
    print("="*50)
    
    # 配置参数
    if len(sys.argv) >= 3:
        excel_path = sys.argv[1]
        output_dir = sys.argv[2]
    else:
        # 默认配置
        excel_path = input("请输入Excel文件路径: ").strip()
        output_dir = input("请输入PDF输出目录 (默认: ./pdf_output): ").strip()
        
        if not output_dir:
            output_dir = "./pdf_output"
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        return False
    
    print(f"📁 Excel文件: {excel_path}")
    print(f"📁 输出目录: {output_dir}")
    
    # 创建转换器并执行转换
    converter = ExcelToPDFConverter(excel_path, output_dir)
    
    print(f"\n🔄 开始转换...")
    success = converter.convert_all()
    
    if success:
        print(f"\n🎉 转换完成！PDF文件已保存到: {output_dir}")
    else:
        print(f"\n❌ 转换失败，请检查日志文件")
    
    return success

if __name__ == "__main__":
    main()
