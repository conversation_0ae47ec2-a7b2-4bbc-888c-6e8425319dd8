#!/usr/bin/env python3
"""
检查分片批处理状态脚本
分析当前批次的处理状态和进度
"""

import sys
import os
import pymysql
from datetime import datetime
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config


def get_connection():
    """获取数据库连接"""
    try:
        db_config = config.get_db_config()
        connection = pymysql.connect(**db_config)
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        raise


def check_batch_status():
    """检查批次处理状态"""
    print("=" * 80)
    print("分片批处理状态检查")
    print("=" * 80)
    
    try:
        connection = get_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        table_config = config.get_table_config()
        case_relation_table = table_config['case_relation_table']
        
        # 1. 查看最近的批次状态分布
        print("1. 最近批次状态分布:")
        print("-" * 50)
        
        status_query = f"""
        SELECT batchid, status, COUNT(*) as count, 
               MIN(updatetime) as start_time,
               MAX(updatetime) as end_time
        FROM `{case_relation_table}`
        WHERE batchid IS NOT NULL 
        GROUP BY batchid, status
        ORDER BY batchid DESC, status
        LIMIT 50
        """
        
        cursor.execute(status_query)
        results = cursor.fetchall()
        
        if not results:
            print("  没有找到批次数据")
            return
        
        # 按批次分组显示
        batch_stats = defaultdict(dict)
        for row in results:
            batchid = row['batchid']
            status = row['status']
            count = row['count']
            batch_stats[batchid][status] = {
                'count': count,
                'start_time': row['start_time'],
                'end_time': row['end_time']
            }
        
        # 状态说明
        status_desc = {
            '0': '初始状态',
            '1': 'PDF合并完成',
            '2': 'OCR识别完成',
            '3': '要素提取完成',
            '4': '处理失败'
        }
        
        for batchid in list(batch_stats.keys())[:10]:  # 显示最近10个批次
            print(f"\n批次: {batchid}")
            batch_data = batch_stats[batchid]
            
            total_cases = sum(data['count'] for data in batch_data.values())
            print(f"  总案件数: {total_cases}")
            
            for status in ['0', '1', '2', '3', '4']:
                if status in batch_data:
                    data = batch_data[status]
                    count = data['count']
                    percentage = (count / total_cases) * 100
                    print(f"  状态{status} ({status_desc[status]}): {count} 个 ({percentage:.1f}%)")
                    
                    if status == '2':
                        print(f"    ✅ OCR识别完成! 时间: {data['end_time']}")
                    elif status == '3':
                        print(f"    🎉 要素提取完成! 时间: {data['end_time']}")
                    elif status == '4':
                        print(f"    ❌ 处理失败，时间: {data['end_time']}")
        
        # 2. 检查STATUS=2的详细情况
        print(f"\n2. STATUS=2 (OCR识别完成) 详细分析:")
        print("-" * 50)
        
        status2_query = f"""
        SELECT batchid, COUNT(*) as count,
               MIN(updatetime) as first_completed,
               MAX(updatetime) as last_completed
        FROM `{case_relation_table}`
        WHERE status = '2'
        GROUP BY batchid
        ORDER BY last_completed DESC
        LIMIT 10
        """
        
        cursor.execute(status2_query)
        status2_results = cursor.fetchall()
        
        if status2_results:
            print("最近完成OCR识别的批次:")
            for row in status2_results:
                batchid = row['batchid']
                count = row['count']
                first_time = row['first_completed']
                last_time = row['last_completed']
                
                print(f"  批次 {batchid}:")
                print(f"    OCR完成案件数: {count}")
                print(f"    首个完成时间: {first_time}")
                print(f"    最后完成时间: {last_time}")
                
                # 检查是否有要素提取完成的案件
                extract_query = f"""
                SELECT COUNT(*) as extracted_count
                FROM `{case_relation_table}`
                WHERE batchid = %s AND status = '3'
                """
                cursor.execute(extract_query, (batchid,))
                extracted = cursor.fetchone()['extracted_count']
                
                if extracted > 0:
                    print(f"    要素提取完成: {extracted} 个 ✅")
                else:
                    print(f"    要素提取状态: 待处理 ⏳")
        
        # 3. 检查是否有失败的案件
        print(f"\n3. 失败案件分析:")
        print("-" * 50)
        
        failed_query = f"""
        SELECT batchid, COUNT(*) as failed_count,
               MAX(updatetime) as last_failed
        FROM `{case_relation_table}`
        WHERE status = '4'
        GROUP BY batchid
        ORDER BY last_failed DESC
        LIMIT 5
        """
        
        cursor.execute(failed_query)
        failed_results = cursor.fetchall()
        
        if failed_results:
            print("最近失败的批次:")
            for row in failed_results:
                print(f"  批次 {row['batchid']}: {row['failed_count']} 个失败案件")
                print(f"    最后失败时间: {row['last_failed']}")
        else:
            print("  ✅ 没有失败的案件!")
        
        # 4. 分片批处理效果分析
        print(f"\n4. 分片批处理效果分析:")
        print("-" * 50)
        
        # 获取最新批次的处理时间分布
        latest_batch_query = f"""
        SELECT batchid, status, updatetime
        FROM `{case_relation_table}`
        WHERE batchid = (
            SELECT batchid FROM `{case_relation_table}` 
            WHERE status IN ('2', '3') 
            ORDER BY updatetime DESC 
            LIMIT 1
        )
        AND status IN ('1', '2', '3')
        ORDER BY updatetime
        """
        
        cursor.execute(latest_batch_query)
        timeline_results = cursor.fetchall()
        
        if timeline_results:
            latest_batch = timeline_results[0]['batchid']
            print(f"最新批次 {latest_batch} 的处理时间线:")
            
            status_times = defaultdict(list)
            for row in timeline_results:
                status_times[row['status']].append(row['updatetime'])
            
            if '1' in status_times and '2' in status_times:
                pdf_merge_time = min(status_times['1'])
                ocr_start_time = min(status_times['2'])
                ocr_end_time = max(status_times['2'])
                
                print(f"  PDF合并完成: {pdf_merge_time}")
                print(f"  OCR开始时间: {ocr_start_time}")
                print(f"  OCR结束时间: {ocr_end_time}")
                
                ocr_duration = (ocr_end_time - ocr_start_time).total_seconds()
                print(f"  OCR总耗时: {ocr_duration:.1f} 秒 ({ocr_duration/60:.1f} 分钟)")
                
                if '3' in status_times:
                    extract_end_time = max(status_times['3'])
                    extract_duration = (extract_end_time - ocr_end_time).total_seconds()
                    print(f"  要素提取耗时: {extract_duration:.1f} 秒 ({extract_duration/60:.1f} 分钟)")
        
        cursor.close()
        connection.close()
        
        print(f"\n" + "=" * 80)
        print("✅ 状态检查完成!")
        print("🎉 恭喜! 分片批处理系统运行成功，OCR识别已完成!")
        print("💡 下一步: 系统会自动进行要素提取，请继续观察STATUS=3的案件数量")
        print("=" * 80)
        
    except Exception as e:
        print(f"状态检查失败: {e}")
        import traceback
        traceback.print_exc()


def show_next_steps():
    """显示下一步操作建议"""
    print(f"\n📋 下一步操作建议:")
    print(f"1. 继续监控要素提取进度:")
    print(f"   SELECT status, COUNT(*) FROM ds_case_relation GROUP BY status;")
    
    print(f"\n2. 查看要素提取日志:")
    print(f"   tail -f logs/main_controller_*.log | grep '要素提取'")
    
    print(f"\n3. 如果要素提取较慢，可以检查:")
    print(f"   - 大模型API连接状态")
    print(f"   - 并发处理配置")
    print(f"   - 系统资源使用情况")
    
    print(f"\n4. 手动触发要素提取（如果需要）:")
    print(f"   python -c \"")
    print(f"   from case_extraction_agent import CaseExtractionAgent")
    print(f"   agent = CaseExtractionAgent()")
    print(f"   cases = agent.get_cases_for_extraction('your_batch_id')")
    print(f"   print(f'待提取案件数: {{len(cases)}}')\"")


if __name__ == "__main__":
    check_batch_status()
    show_next_steps()
