#!/usr/bin/env python3
"""
手动触发要素提取脚本
完成分片批处理的最后一步
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from case_extraction_agent import CaseExtractionAgent
from config import config


async def manual_extraction_for_batch(batch_id: str, max_cases: int = None):
    """手动触发指定批次的要素提取"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    print("=" * 80)
    print(f"手动触发要素提取 - 批次: {batch_id}")
    print("=" * 80)
    
    try:
        # 初始化要素提取智能体
        agent = CaseExtractionAgent()
        logger.info("要素提取智能体初始化成功")
        
        # 获取待提取案件
        extraction_cases = agent.get_cases_for_extraction(batch_id)
        
        if not extraction_cases:
            print(f"❌ 批次 {batch_id} 没有待提取案件")
            return
        
        total_cases = len(extraction_cases)
        process_cases = extraction_cases[:max_cases] if max_cases else extraction_cases
        
        print(f"📊 批次信息:")
        print(f"  批次号: {batch_id}")
        print(f"  总待提取案件: {total_cases}")
        print(f"  本次处理案件: {len(process_cases)}")
        print(f"  开始时间: {datetime.now()}")
        
        # 统计信息
        successful_extractions = []
        failed_extractions = []
        
        print(f"\n🚀 开始要素提取...")
        print("-" * 60)
        
        # 控制并发数
        semaphore = asyncio.Semaphore(3)  # 限制并发数为3
        
        async def process_single_case_with_semaphore(case_info, index):
            async with semaphore:
                ajbh = case_info['ajbh']
                print(f"[{index+1}/{len(process_cases)}] 处理案件: {ajbh}")
                
                try:
                    result = await agent.process_single_case_complete(case_info)
                    
                    if result.get("status") == "success":
                        successful_extractions.append(result)
                        print(f"  ✅ 成功 - 提取了 {len(result.get('extracted_data', {}))} 个要素")
                        return result
                    else:
                        error_msg = result.get('error', '未知错误')
                        failed_extractions.append({
                            "ajbh": ajbh,
                            "error": error_msg
                        })
                        print(f"  ❌ 失败: {error_msg}")
                        return result
                        
                except Exception as e:
                    error_msg = str(e)
                    failed_extractions.append({
                        "ajbh": ajbh,
                        "error": error_msg
                    })
                    print(f"  ❌ 异常: {error_msg}")
                    return {"status": "error", "error": error_msg}
        
        # 并发处理所有案件
        tasks = []
        for i, case_info in enumerate(process_cases):
            task = process_single_case_with_semaphore(case_info, i)
            tasks.append(task)
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        print(f"\n" + "=" * 80)
        print(f"📈 要素提取完成统计")
        print("=" * 80)
        
        print(f"总处理案件: {len(process_cases)}")
        print(f"成功案件: {len(successful_extractions)} ✅")
        print(f"失败案件: {len(failed_extractions)} ❌")
        print(f"成功率: {(len(successful_extractions)/len(process_cases)*100):.1f}%")
        print(f"完成时间: {datetime.now()}")
        
        if failed_extractions:
            print(f"\n❌ 失败案件详情:")
            for failure in failed_extractions[:5]:  # 只显示前5个失败案件
                print(f"  {failure['ajbh']}: {failure['error']}")
            if len(failed_extractions) > 5:
                print(f"  ... 还有 {len(failed_extractions)-5} 个失败案件")
        
        if successful_extractions:
            print(f"\n✅ 成功案件示例:")
            sample = successful_extractions[0]
            extracted_data = sample.get('extracted_data', {})
            print(f"  案件: {sample.get('ajbh', 'N/A')}")
            print(f"  提取要素: {list(extracted_data.keys())}")
        
        # 检查数据库状态更新
        print(f"\n🔍 验证数据库状态更新...")
        import pymysql
        
        try:
            db_config = config.get_db_config()
            connection = pymysql.connect(**db_config)
            cursor = connection.cursor()
            
            table_config = config.get_table_config()
            case_relation_table = table_config['case_relation_table']
            
            # 查询当前状态分布
            cursor.execute(f"""
                SELECT status, COUNT(*) as count 
                FROM `{case_relation_table}` 
                WHERE batchid = %s 
                GROUP BY status 
                ORDER BY status
            """, (batch_id,))
            
            status_results = cursor.fetchall()
            
            print(f"批次 {batch_id} 状态分布:")
            status_desc = {
                '0': '初始状态',
                '1': 'PDF合并完成', 
                '2': 'OCR识别完成',
                '3': '要素提取完成',
                '4': '处理失败'
            }
            
            for status, count in status_results:
                desc = status_desc.get(str(status), f'状态{status}')
                print(f"  {desc}: {count} 个案件")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            print(f"  数据库状态检查失败: {e}")
        
        print(f"\n🎉 要素提取任务完成!")
        
        if len(successful_extractions) == len(process_cases):
            print(f"🌟 完美! 所有案件都成功完成要素提取!")
        elif len(successful_extractions) > 0:
            print(f"✅ 部分成功! {len(successful_extractions)}/{len(process_cases)} 案件完成要素提取")
        else:
            print(f"❌ 需要检查! 没有案件成功完成要素提取")
        
        return {
            "status": "success",
            "batch_id": batch_id,
            "total_cases": len(process_cases),
            "successful_count": len(successful_extractions),
            "failed_count": len(failed_extractions),
            "successful_extractions": successful_extractions,
            "failed_extractions": failed_extractions
        }
        
    except Exception as e:
        logger.error(f"要素提取任务失败: {e}")
        import traceback
        traceback.print_exc()
        
        return {
            "status": "error",
            "batch_id": batch_id,
            "error": str(e)
        }


async def main():
    """主函数"""
    print("分片批处理系统 - 手动要素提取")
    print(f"运行时间: {datetime.now()}")
    
    # 最新的批次ID（从之前的检查结果得到）
    batch_id = "2025081115490801"
    
    print(f"\n将为批次 {batch_id} 触发要素提取...")
    print(f"这将完成分片批处理系统的最后一步")
    
    # 先处理前5个案件作为测试
    result = await manual_extraction_for_batch(batch_id, max_cases=5)
    
    if result["status"] == "success" and result["successful_count"] > 0:
        print(f"\n🎯 测试成功! 是否继续处理剩余案件?")
        print(f"输入 'yes' 继续处理所有案件，或按Enter跳过:")
        
        # 在实际环境中可以取消注释下面的代码来等待用户输入
        # user_input = input().strip().lower()
        # if user_input == 'yes':
        #     print(f"\n继续处理所有案件...")
        #     full_result = await manual_extraction_for_batch(batch_id)
        #     return full_result
        
        print(f"测试完成。如需处理所有案件，请运行:")
        print(f"python -c \"import asyncio; from manual_extraction_trigger import manual_extraction_for_batch; asyncio.run(manual_extraction_for_batch('{batch_id}'))\"")
    
    return result


if __name__ == "__main__":
    asyncio.run(main())
