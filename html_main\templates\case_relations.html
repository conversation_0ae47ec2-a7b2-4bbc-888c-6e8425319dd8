<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件关系数据 - 智慧"双反"平台</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.css" rel="stylesheet">
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-bg-color: #0c235b;
            --el-bg-color-page: #f2f3f5;
            --el-bg-color-overlay: #00326f;
            --el-text-color-primary: rgba(255, 255, 255, 0.9);
            --el-text-color-regular: rgba(255, 255, 255, 0.8);
            --el-text-color-secondary: #909399;
            --el-border-color: rgba(255, 255, 255, 0.25);
            --el-border-color-light: #1e3f77;
            --el-fill-color-light: rgba(255, 255, 255, 0.08);
            --el-font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }

        body {
            font-family: var(--el-font-family);
            background: linear-gradient(135deg, #0c235b 0%, #1e3f77 100%);
            min-height: 100vh;
            margin: 0;
            color: var(--el-text-color-primary);
        }

        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .platform-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
        }

        .platform-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .platform-subtitle {
            font-size: 16px;
            color: var(--el-text-color-regular);
            margin-top: 8px;
        }

        .tab-navigation {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 24px;
            border: 1px solid var(--el-border-color);
        }

        .nav-tabs {
            border: none;
            display: flex;
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 16px 24px;
            background: transparent;
            border: none;
            border-radius: 8px;
            color: var(--el-text-color-regular);
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .nav-tab:hover {
            background: rgba(64, 158, 255, 0.1);
            color: var(--el-color-primary);
        }

        .nav-tab.active {
            background: var(--el-color-primary);
            color: white;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            color: #333;
        }
        .search-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .search-input, .search-select {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: #333;
            transition: all 0.3s ease;
        }

        .search-input:focus, .search-select:focus {
            outline: none;
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .search-btn {
            background: var(--el-color-primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #3a8ee6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .reset-btn {
            background: rgba(255, 255, 255, 0.1);
            color: var(--el-text-color-regular);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .reset-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .table-container {
            overflow-x: auto;
            margin-bottom: 24px;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            background: white;
        }

        .data-table th {
            background: linear-gradient(135deg, #0c235b 0%, #1e3f77 100%);
            color: white;
            font-weight: 600;
            padding: 16px 12px;
            text-align: left;
            border-bottom: 2px solid var(--el-color-primary);
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
            font-size: 14px;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        .data-table tr:hover {
            background-color: rgba(64, 158, 255, 0.05);
        }

        .text-truncate {
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-0 { background: #e6f7ff; color: #1890ff; }
        .status-1 { background: #f6ffed; color: #52c41a; }
        .status-2 { background: #fff7e6; color: #fa8c16; }
        .status-3 { background: #f6ffed; color: #52c41a; }
        .status-4 { background: #fff2f0; color: #ff4d4f; }

        .stats-info {
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
            border: 1px solid rgba(64, 158, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            color: #333;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }

        .page-btn {
            padding: 8px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: var(--el-text-color-regular);
            border-radius: 6px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .page-btn:hover {
            background: var(--el-color-primary);
            color: white;
            border-color: var(--el-color-primary);
        }

        .page-btn.active {
            background: var(--el-color-primary);
            color: white;
            border-color: var(--el-color-primary);
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid var(--el-color-primary);
            background: transparent;
            color: var(--el-color-primary);
            border-radius: 6px;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .action-btn:hover {
            background: var(--el-color-primary);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 平台标题 -->
        <div class="platform-header">
            <h1 class="platform-title">智慧"双反"平台</h1>
            <p class="platform-subtitle">案件关系数据管理系统</p>
        </div>

        <!-- 页签导航 -->
        <div class="tab-navigation">
            <div class="nav-tabs">
                <a class="nav-tab active" href="/relations">
                    <i class="el-icon-connection"></i>
                    案件关系数据
                </a>
                <a class="nav-tab" href="/case_details">
                    <i class="el-icon-document"></i>
                    案件详细信息列表
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-card">
            <!-- 搜索表单 -->
            <form class="search-form" method="GET">
                <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto auto; gap: 16px; align-items: end;">
                    <div>
                        <label style="display: block; margin-bottom: 8px; color: var(--el-text-color-regular); font-size: 14px;">关键词搜索</label>
                        <input type="text" class="search-input" name="search"
                               placeholder="搜索案件编号、案件名称、批次号..."
                               value="{{ search_term }}" style="width: 100%;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 8px; color: var(--el-text-color-regular); font-size: 14px;">处理状态</label>
                        <select class="search-select" name="status" style="width: 100%;">
                            <option value="">全部状态</option>
                            <option value="0" {% if status_filter == '0' %}selected{% endif %}>开始</option>
                            <option value="1" {% if status_filter == '1' %}selected{% endif %}>下载完成</option>
                            <option value="2" {% if status_filter == '2' %}selected{% endif %}>OCR完成</option>
                            <option value="3" {% if status_filter == '3' %}selected{% endif %}>AI完成</option>
                            <option value="4" {% if status_filter == '4' %}selected{% endif %}>报错</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 8px; color: var(--el-text-color-regular); font-size: 14px;">每页显示</label>
                        <select class="search-select" name="per_page" style="width: 100%;">
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20条/页</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50条/页</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100条/页</option>
                        </select>
                    </div>
                    <div>
                        <button type="submit" class="search-btn">
                            🔍 搜索
                        </button>
                    </div>
                    <div>
                        <a href="/relations" class="reset-btn">
                            🔄 重置
                        </a>
                    </div>
                </div>
            </form>

            <!-- 统计信息 -->
            <div class="stats-info">
                📊 数据统计：共找到 <strong>{{ total_records }}</strong> 条记录，当前第 <strong>{{ page }}</strong> 页，共 <strong>{{ total_pages }}</strong> 页
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>数据批次号</th>
                            <th>案件编号</th>
                            <th>案件名称</th>
                            <th>录入时间</th>
                            <th>同步更新时间</th>
                            <th>意见书数量</th>
                            <th>信息主键编号</th>
                            <th>案件类型</th>
                            <th>法律文书地址</th>
                            <th>同步入库时间</th>
                            <th>案件内容</th>
                            <th>关系图代码</th>
                            <th>最终关系图代码</th>
                            <th>修改人员</th>
                            <th>图代码来源</th>
                            <th>AI处理状态</th>
                            <th>AI开始时间</th>
                            <th>AI完成时间</th>
                            <th>更新时间</th>
                            <th>AI重跑次数</th>
                            <th>错误信息</th>
                            <th>删除状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for case in cases %}
                        <tr>
                            <td class="text-truncate" title="{{ case.batchid }}">{{ case.batchid }}</td>
                            <td>
                                <strong style="color: var(--el-color-primary);">{{ case.ajbh }}</strong>
                            </td>
                            <td class="text-truncate" title="{{ case.ajmc or '' }}">{{ case.ajmc or '-' }}</td>
                            <td class="text-truncate" title="{{ case.llsj_formatted or '' }}">{{ case.llsj_formatted or '-' }}</td>
                            <td class="text-truncate" title="{{ case.tbgxsj_formatted or '' }}">{{ case.tbgxsj_formatted or '-' }}</td>
                            <td><span class="status-badge" style="background: #e6f7ff; color: #1890ff;">{{ case.counts or 0 }}</span></td>
                            <td class="text-truncate" title="{{ case.xxzjbh_display or '' }}">{{ case.xxzjbh_display or '-' }}</td>
                            <td><span class="status-badge" style="background: #f6ffed; color: #52c41a;">{{ case.ajlx or '-' }}</span></td>
                            <td style="text-align: center;">
                                {% if case.flwsxzdz %}
                                <a href="{{ case.flwsxzdz }}" target="_blank" class="action-btn" title="查看文书">
                                    🔗 文书
                                </a>
                                {% else %}
                                <span style="color: #ccc;">-</span>
                                {% endif %}
                            </td>
                            <td class="text-truncate" title="{{ case.tbrksj_formatted }}">{{ case.tbrksj_formatted or '-' }}</td>
                            <td class="text-truncate" title="{{ case.ajnr or '' }}">
                                {% if case.ajnr %}
                                    {{ case.ajnr[:50] }}{% if case.ajnr|length > 50 %}...{% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td style="text-align: center;">
                                {% if case.code %}
                                <span style="color: #52c41a;">✓ 有代码</span>
                                {% else %}
                                <span style="color: #ccc;">- 无代码</span>
                                {% endif %}
                            </td>
                            <td style="text-align: center;">
                                {% if case.lastcode %}
                                <a href="/mermaid/{{ case.ajbh }}" target="_blank" class="action-btn" title="查看关系图">
                                    📊 关系图
                                </a>
                                {% else %}
                                <span style="color: #ccc;">-</span>
                                {% endif %}
                            </td>
                            <td>{{ case.updater or '-' }}</td>
                            <td>
                                {% if case.codesource == '1' %}
                                <span class="status-badge" style="background: #fff7e6; color: #fa8c16;">人工修改</span>
                                {% else %}
                                <span class="status-badge" style="background: #e6f7ff; color: #1890ff;">AI生成</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="status-badge status-{{ case.status or '0' }}">
                                    {% if case.status == '0' %}开始
                                    {% elif case.status == '1' %}下载完成
                                    {% elif case.status == '2' %}OCR完成
                                    {% elif case.status == '3' %}AI完成
                                    {% elif case.status == '4' %}报错
                                    {% else %}未知{% endif %}
                                </span>
                            </td>
                            <td class="text-truncate" title="{{ case.starttime_formatted }}">{{ case.starttime_formatted or '-' }}</td>
                            <td class="text-truncate" title="{{ case.endtime_formatted }}">{{ case.endtime_formatted or '-' }}</td>
                            <td class="text-truncate" title="{{ case.updatetime_formatted }}">{{ case.updatetime_formatted or '-' }}</td>
                            <td><span class="status-badge" style="background: #fff7e6; color: #fa8c16;">{{ case.nums or 0 }}</span></td>
                            <td class="text-truncate" title="{{ case.error or '' }}">
                                {% if case.error %}
                                    {{ case.error[:30] }}{% if case.error|length > 30 %}...{% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                <span class="status-badge" style="background: {% if case.isdelete_text == '有效' %}#f6ffed; color: #52c41a{% else %}#fff2f0; color: #ff4d4f{% endif %};">
                                    {{ case.isdelete_text }}
                                </span>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px;">
                                    <a href="/case/{{ case.ajbh }}" class="action-btn" title="查看详情">
                                        👁️ 详情
                                    </a>
                                    {% if case.lastcode %}
                                    <a href="/mermaid/{{ case.ajbh }}" target="_blank" class="action-btn" title="查看关系图">
                                        📊 图表
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if total_pages > 1 %}
            <div class="pagination">
                {% if page > 1 %}
                    <a class="page-btn" href="?page=1&per_page={{ per_page }}&status={{ status_filter }}&search={{ search_term }}">⏮️ 首页</a>
                    <a class="page-btn" href="?page={{ page - 1 }}&per_page={{ per_page }}&status={{ status_filter }}&search={{ search_term }}">⬅️ 上一页</a>
                {% endif %}

                {% for p in pages_to_show %}
                    {% if p == page %}
                        <span class="page-btn active">{{ p }}</span>
                    {% else %}
                        <a class="page-btn" href="?page={{ p }}&per_page={{ per_page }}&status={{ status_filter }}&search={{ search_term }}">{{ p }}</a>
                    {% endif %}
                {% endfor %}

                {% if page < total_pages %}
                    <a class="page-btn" href="?page={{ page + 1 }}&per_page={{ per_page }}&status={{ status_filter }}&search={{ search_term }}">下一页 ➡️</a>
                    <a class="page-btn" href="?page={{ total_pages }}&per_page={{ per_page }}&status={{ status_filter }}&search={{ search_term }}">末页 ⏭️</a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 表格行悬停效果
            const rows = document.querySelectorAll('.data-table tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(2px)';
                    this.style.boxShadow = '0 2px 8px rgba(64, 158, 255, 0.15)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // 搜索框焦点效果
            const searchInputs = document.querySelectorAll('.search-input, .search-select');
            searchInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'translateY(-1px)';
                });
                input.addEventListener('blur', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
