#!/bin/bash
# 检查后台案件处理系统状态脚本

cd /data/ai/AJagent-main

echo "📊 案件处理系统状态检查"
echo "=========================="

# 检查PID文件
if [ -f "logs/system.pid" ]; then
    PID=$(cat logs/system.pid)
    echo "📄 PID文件: logs/system.pid"
    echo "🆔 进程ID: $PID"
    
    # 检查进程是否运行
    if ps -p $PID > /dev/null 2>&1; then
        echo "✅ 状态: 运行中"
        
        # 显示进程详细信息
        echo ""
        echo "📋 进程详情:"
        ps aux | grep $PID | grep -v grep
        
        # 显示进程启动时间
        echo ""
        echo "⏰ 启动时间:"
        ps -o pid,lstart -p $PID
        
        # 显示CPU和内存使用
        echo ""
        echo "💻 资源使用:"
        ps -o pid,pcpu,pmem,rss -p $PID
        
    else
        echo "❌ 状态: 已停止 (PID文件存在但进程不运行)"
        echo "🗑️  建议清理PID文件: rm logs/system.pid"
    fi
else
    echo "⚠️  PID文件不存在"
    
    # 查找可能的相关进程
    PIDS=$(ps aux | grep "python.*start.py" | grep -v grep | awk '{print $2}')
    if [ -n "$PIDS" ]; then
        echo "🔍 发现相关进程:"
        ps aux | grep "python.*start.py" | grep -v grep
    else
        echo "❌ 状态: 未运行"
    fi
fi

echo ""
echo "📁 日志文件:"
if [ -d "logs" ]; then
    ls -la logs/system_*.log 2>/dev/null | tail -5 || echo "无日志文件"
else
    echo "logs目录不存在"
fi

echo ""
echo "🔧 管理命令:"
echo "启动服务: ./start_background.sh"
echo "停止服务: ./stop_background.sh"
echo "重启服务: ./restart_background.sh"
echo "查看日志: tail -f logs/system_YYYYMMDD_HHMMSS.log"
