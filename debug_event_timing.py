#!/usr/bin/env python3
"""
调试事件通知时间问题
验证asyncio.Event的响应时间
"""

import asyncio
import logging
from datetime import datetime


def log_with_time(message):
    """带精确时间戳的日志"""
    now = datetime.now()
    print(f"{now.strftime('%H:%M:%S')}.{now.microsecond//1000:03d} - {message}")


async def test_event_response_timing():
    """测试事件响应时间"""
    
    print("=" * 80)
    print("🔍 事件响应时间调试")
    print("=" * 80)
    
    # 创建事件
    events = {1: asyncio.Event(), 2: asyncio.Event(), 3: asyncio.Event()}
    
    async def ocr_simulator():
        """模拟OCR工作器"""
        log_with_time("🔄 OCR工作器开始")
        
        for ppid in [1, 2, 3]:
            log_with_time(f"🔄 OCR片批{ppid}开始处理")
            await asyncio.sleep(0.5)  # 模拟OCR处理时间
            
            log_with_time(f"✅ OCR片批{ppid}完成")
            events[ppid].set()  # 立即设置事件
            log_with_time(f"📢 通知要素提取片批{ppid}可以开始")
        
        log_with_time("🏁 OCR工作器完成所有任务")
    
    async def extraction_simulator(ppid):
        """模拟要素提取工作器"""
        log_with_time(f"⏳ 要素提取工作器{ppid}开始等待OCR完成...")
        
        await events[ppid].wait()  # 等待对应的OCR完成
        
        log_with_time(f"🚀 要素提取片批{ppid}开始（OCR已完成）")
        
        await asyncio.sleep(0.3)  # 模拟要素提取处理时间
        
        log_with_time(f"✅ 要素提取片批{ppid}完成")
        
        return ppid
    
    # 创建任务
    log_with_time("🚀 创建并行任务...")
    
    # 1. 先创建要素提取任务
    extraction_tasks = []
    for ppid in [1, 2, 3]:
        task = asyncio.create_task(extraction_simulator(ppid))
        extraction_tasks.append(task)
        log_with_time(f"📋 要素提取工作器{ppid}任务已创建")
    
    # 2. 创建OCR任务
    ocr_task = asyncio.create_task(ocr_simulator())
    log_with_time("📋 OCR工作器任务已创建")
    
    # 3. 并行执行
    log_with_time("🎯 开始并行执行...")
    
    all_tasks = [ocr_task] + extraction_tasks
    await asyncio.gather(*all_tasks, return_exceptions=True)
    
    log_with_time("🏁 所有任务完成")
    
    print(f"\n📊 预期的时间戳模式:")
    print(f"  ✅ OCR片批1完成 → 立即(几毫秒内) → 要素提取片批1开始")
    print(f"  ✅ OCR片批2完成 → 立即(几毫秒内) → 要素提取片批2开始")
    print(f"  ✅ OCR片批3完成 → 立即(几毫秒内) → 要素提取片批3开始")


async def test_real_parallel_pattern():
    """测试真正的并行模式"""
    
    print(f"\n" + "=" * 80)
    print("🎯 真正并行模式测试")
    print("=" * 80)
    
    # 模拟您需要的并行模式
    async def ocr_and_extraction_pipeline():
        """OCR和要素提取流水线"""
        
        # 创建事件
        events = {1: asyncio.Event(), 2: asyncio.Event(), 3: asyncio.Event()}
        
        async def ocr_worker():
            """OCR工作器 - 串行处理"""
            for ppid in [1, 2, 3]:
                log_with_time(f"🔄 OCR片批{ppid}开始")
                await asyncio.sleep(0.5)  # 模拟OCR处理
                log_with_time(f"✅ OCR片批{ppid}完成")
                
                # 立即通知要素提取
                events[ppid].set()
                log_with_time(f"📢 立即通知要素提取片批{ppid}")
        
        async def extraction_worker(ppid):
            """要素提取工作器 - 等待对应OCR完成"""
            log_with_time(f"⏳ 要素提取工作器{ppid}等待中...")
            
            await events[ppid].wait()
            
            log_with_time(f"🚀 要素提取片批{ppid}立即开始")
            await asyncio.sleep(0.8)  # 模拟要素提取处理
            log_with_time(f"✅ 要素提取片批{ppid}完成")
        
        # 创建所有任务
        tasks = [
            asyncio.create_task(ocr_worker()),
            asyncio.create_task(extraction_worker(1)),
            asyncio.create_task(extraction_worker(2)),
            asyncio.create_task(extraction_worker(3))
        ]
        
        log_with_time("🎯 流水线并行开始...")
        await asyncio.gather(*tasks)
        log_with_time("🏁 流水线并行完成")
    
    await ocr_and_extraction_pipeline()
    
    print(f"\n💡 这个测试应该显示:")
    print(f"  OCR片批1完成 → 要素提取片批1立即开始 (同时OCR片批2开始)")
    print(f"  OCR片批2完成 → 要素提取片批2立即开始 (同时OCR片批3开始)")
    print(f"  OCR片批3完成 → 要素提取片批3立即开始")


async def main():
    """主函数"""
    print("事件响应时间调试工具")
    print(f"运行时间: {datetime.now()}")
    
    # 1. 测试基本事件响应
    await test_event_response_timing()
    
    # 2. 测试真正的并行模式
    await test_real_parallel_pattern()
    
    print(f"\n" + "=" * 80)
    print("🎯 调试结论")
    print("=" * 80)
    
    print(f"如果上面的测试显示正确的并行模式，")
    print(f"但实际系统仍然等到所有OCR完成才开始要素提取，")
    print(f"那么问题可能在于:")
    print(f"")
    print(f"1. 要素提取工作器的事件等待逻辑")
    print(f"2. OCR失败时的事件设置")
    print(f"3. asyncio.gather()的执行顺序")
    print(f"4. 任务间的隐式依赖")
    
    print(f"\n💡 解决方案:")
    print(f"  确保OCR完成时立即设置事件，无论成功还是失败")
    print(f"  确保要素提取工作器在事件设置后立即响应")


if __name__ == "__main__":
    asyncio.run(main())
