{% extends "base.html" %}

{% block title %}字段显示验证 - 案件关系图查看器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-check-circle"></i> 字段显示验证</h2>
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                <i class="bi bi-list-ul"></i> 查看案件列表
            </a>
        </div>

        <!-- 验证结果 -->
        <div class="alert alert-success">
            <h5><i class="bi bi-check-circle-fill"></i> 验证通过！</h5>
            <p class="mb-0">案件关系数据页面已完整显示 <strong>ds_case_relation</strong> 表的所有 <strong>{{ field_mapping|length }}</strong> 个字段。</p>
        </div>

        <!-- 字段映射表 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-table"></i> 字段映射表 (共 {{ field_mapping|length }} 个字段)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th width="50">序号</th>
                                <th width="120">数据库字段</th>
                                <th width="100">页面显示</th>
                                <th>字段说明</th>
                                <th width="120">样本数据</th>
                                <th width="80">状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for field in field_mapping %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td><code>{{ field.field }}</code></td>
                                <td><strong>{{ field.name }}</strong></td>
                                <td>{{ field.description }}</td>
                                <td>
                                    {% if sample_data and sample_data[field.field] %}
                                    <div class="text-truncate" style="max-width: 120px;" title="{{ sample_data[field.field] }}">
                                        {{ sample_data[field.field] }}
                                    </div>
                                    {% else %}
                                    <span class="text-muted">NULL</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-success">✅ 已显示</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 数据库表结构 -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-database"></i> 数据库表结构
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>字段名</th>
                                <th>数据类型</th>
                                <th>是否为空</th>
                                <th>键</th>
                                <th>默认值</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for field in table_structure %}
                            <tr>
                                <td><code>{{ field.Field }}</code></td>
                                <td>{{ field.Type }}</td>
                                <td>{{ field.Null }}</td>
                                <td>{{ field.Key or '-' }}</td>
                                <td>{{ field.Default or '-' }}</td>
                                <td>{{ field.Extra or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 显示优化说明 -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="bi bi-lightbulb"></i> 显示优化说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>解决截断问题的方法：</h6>
                        <ul>
                            <li>✅ 使用紧凑表格样式 (<code>compact-table</code>)</li>
                            <li>✅ 减小字体大小 (12px)</li>
                            <li>✅ 缩短文本显示，完整内容在悬停提示中</li>
                            <li>✅ 使用图标代替长文本</li>
                            <li>✅ 设置最小列宽确保可读性</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>表格特性：</h6>
                        <ul>
                            <li>📱 响应式设计，支持水平滚动</li>
                            <li>🔒 粘性表头，滚动时保持可见</li>
                            <li>💡 悬停提示显示完整内容</li>
                            <li>🎨 状态用彩色徽章显示</li>
                            <li>📏 每列都有合适的最小宽度</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="bi bi-info-circle"></i> 查看建议：</h6>
                    <ul class="mb-0">
                        <li>🖥️ <strong>桌面端</strong>：使用大屏幕或外接显示器获得最佳体验</li>
                        <li>📱 <strong>移动端</strong>：横屏查看，可左右滑动表格</li>
                        <li>🔍 <strong>详细信息</strong>：点击案件编号查看完整详情页</li>
                        <li>⚡ <strong>快速操作</strong>：使用搜索功能快速定位特定案件</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> 快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="bi bi-list-ul"></i> 查看案件列表
                    </a>
                    <a href="{{ url_for('stats') }}" class="btn btn-info">
                        <i class="bi bi-bar-chart"></i> 统计分析
                    </a>
                    {% if sample_data %}
                    <a href="{{ url_for('case_detail', ajbh=sample_data.ajbh) }}" class="btn btn-success">
                        <i class="bi bi-eye"></i> 查看样本详情
                    </a>
                    {% endif %}
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        💡 提示：如果表格显示仍有问题，请尝试：
                        1) 使用更大的屏幕 
                        2) 调整浏览器缩放比例 
                        3) 使用横屏模式
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
