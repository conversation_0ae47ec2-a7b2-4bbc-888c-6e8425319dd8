# 案件处理系统

基于原有工程改造的无前端案件处理系统，支持从数据库自动获取案件数据，进行PDF下载、OCR识别、AI要素提取和关系图生成的完整流程。

## 系统架构

```
数据库筛选 → PDF下载 → OCR识别 → AI要素提取 → 关系图生成 → 数据入库
     ↓           ↓         ↓          ↓           ↓          ↓
数据获取智能体 → 下载智能体 → OCR智能体 → 提取智能体 → 关系图智能体 → 数据库操作
```

## 核心功能模块

### 1. 配置模块 (`config.py`)
- 数据库连接配置
- 大模型连接配置
- 系统参数配置

### 2. 数据获取模块 (`data_fetcher.py`)
- 从 `djzs_db.ds_case_instrument_his` 表筛选案件数据
- 支持按时间范围、案件编号筛选
- 生成批次号管理任务
- 插入案件关系记录

### 3. PDF下载智能体 (`pdf_downloader.py`)
- 批量下载法律文书PDF
- 异步并发下载，支持重试机制
- 文件以案件编号命名，保存到指定目录
- 下载状态跟踪和错误处理

### 4. OCR识别智能体 (`ocr_processor.py`)
- 调用MonkeyOCR Docker容器进行PDF识别
- 批量处理PDF文件，生成Markdown格式案件内容
- 识别结果保存到数据库
- 支持超时控制和错误恢复

### 5. 案件要素提取智能体 (`case_extraction_agent.py`)
- 基于原有multi_agents.py改造
- 从OCR结果获取案件内容进行AI分析
- 提取预定义要素字段
- 生成Mermaid关系图代码
- 支持异步并发处理和修复机制
- Mermaid代码验证功能

### 6. 主控制脚本 (`main_controller.py`)
- 协调所有智能体完成完整流程
- 支持多种运行模式
- 日志记录和错误处理
- 进度跟踪和状态管理

## 数据库表结构

### 案件关系表 (`ds_case_relation`)
- 管理案件处理状态和关系图代码
- 状态码：0开始，1下载完成，2OCR完成，3AI完成，4报错

### 案件详细信息表 (`ds_case_details`)
- 存储提取的要素数据
- 包含24个要素字段

## 安装和配置

### 1. 环境要求
- Python 3.8+
- MySQL 5.7+
- Docker (用于MonkeyOCR和Mermaid验证)
- 大模型API访问权限

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置修改
编辑 `config.py` 文件，设置：
- 数据库连接信息
- 大模型连接信息
- 系统参数

### 4. 数据库准备
确保以下表存在：
- `djzs_db.ds_case_instrument_his` (源数据表)
- `djzs_db.ds_case_relation` (案件关系表)
- `djzs_db.ds_case_details` (案件详细信息表)

## 使用方法

### 1. 临时跑模式

#### 时间范围处理
```bash
python main_controller.py 0 "2025-01-03 09:00:00" "2025-01-04 09:00:00"
```

#### 时间范围 + 指定案件
```bash
python main_controller.py 0 "2025-01-03 09:00:00" "2025-01-04 09:00:00" "A4401171601002021036001"
```

#### 单个案件处理
```bash
python main_controller.py 0 "A4401171601002021036001"
```

### 2. 定时任务模式

#### 每小时执行（处理上一小时数据）
```bash
python main_controller.py 1
```

#### 每12小时执行
```bash
python main_controller.py 2
```
- 每天13点：处理0-12点的任务
- 每天0点：处理前一天13-24点的任务

#### 每24小时执行（每天0点处理前一天数据）
```bash
python main_controller.py 3
```

## 处理流程

1. **数据获取**：从源表筛选符合条件的案件数据
2. **批次创建**：生成批次号，插入案件关系记录
3. **PDF下载**：批量下载法律文书到指定目录
4. **OCR识别**：调用MonkeyOCR进行文书内容识别
5. **要素提取**：AI分析提取24个要素字段
6. **关系图生成**：生成并验证Mermaid关系图代码
7. **数据入库**：将结果保存到数据库表

## 状态码说明

- `0`: 开始处理
- `1`: PDF下载完成
- `2`: OCR识别完成
- `3`: AI分析完成
- `4`: 处理出错

## 要素字段

系统提取以下24个要素字段：
- 实体类型, 姓名/代号/昵称/公司, 性别, 年龄, 身份证号, 户籍地/现居地
- 文化程度, 直接上级, 所属公司, 所属组织, 角色, 主要职责
- 横向关联人物, 横向关联关系, 纵向关联人物, 纵向关联关系
- 关联工具, 关联物品, 关联犯罪行为, 关联场所
- 强制措施或状态, 司法处置结果, 经济收益, 前科

## 日志管理

日志文件保存在 `logs/` 目录下：
- `main_controller_YYYYMMDD.log`: 主控制器日志
- 各模块独立日志记录

## 性能特性

- 支持异步并发处理，默认最大并发数为10
- PDF下载支持断点续传和重试机制
- OCR处理支持批量操作
- AI分析支持修复机制，最大修复次数为3
- 数据库操作使用事务确保一致性

## 错误处理

- 完整的错误捕获和日志记录
- 失败案件自动标记错误状态
- 支持修复智能体重试机制
- 数据库状态实时更新

## 注意事项

1. 确保MonkeyOCR Docker容器正常运行
2. 确保Mermaid CLI Docker镜像可用（用于验证）
3. 检查文件系统权限，确保可以创建目录和文件
4. 监控磁盘空间，PDF文件可能占用较大空间
5. 定期清理日志文件和临时文件
6. 备份重要的数据库表

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查config.py中的数据库配置
   - 确认网络连接和用户权限

2. **PDF下载失败**
   - 检查网络连接和下载地址
   - 确认目录权限和磁盘空间

3. **OCR处理失败**
   - 检查MonkeyOCR Docker容器状态
   - 确认文件路径和权限

4. **AI分析失败**
   - 检查大模型API配置和连接
   - 确认模型可用性和配额

5. **Mermaid验证失败**
   - 检查Docker环境和Mermaid CLI镜像
   - 确认生成的代码格式正确

## 版本信息

- v1.0: 初始版本，完整的案件处理流程
- 支持多种任务模式和定时执行
- 集成AI要素提取和关系图生成
