#!/usr/bin/env python3
"""
PDF分片批分配器
将PDF文件分配到各个片批目录中，支持分片批处理
"""

import os
import shutil
import logging
import pymysql
from datetime import datetime
from typing import List, Dict, Any, Tuple
from pathlib import Path
from config import config


class PDFBatchSlicer:
    """PDF分片批分配器"""
    
    def __init__(self):
        self.db_config = config.get_db_config()
        self.system_config = config.get_system_config()
        self.table_config = config.get_table_config()
        self.logger = logging.getLogger(__name__)

        # 表名配置
        self.case_relation_table = self.table_config['case_relation_table']
        
        # 系统配置
        self.ocr_base_path = self.system_config['ocr_base_path']
        self.batch_slice_size = self.system_config['batch_slice_size']
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def get_pdf_files_from_batch(self, batch_id: str) -> List[Dict[str, str]]:
        """
        从数据库获取批次中的PDF文件信息
        
        Args:
            batch_id: 批次号
            
        Returns:
            PDF文件信息列表，包含ajbh和文件路径
        """
        try:
            connection = self.get_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            # 查询状态为1（PDF合并完成）的案件
            query = f"""
            SELECT ajbh, batchid
            FROM `{self.case_relation_table}`
            WHERE batchid = %s AND status = '1'
            ORDER BY ajbh
            """
            
            cursor.execute(query, (batch_id,))
            results = cursor.fetchall()
            
            # 构建PDF文件信息
            pdf_files = []
            input_dir = f"{self.ocr_base_path}/{batch_id}/input"
            
            for row in results:
                ajbh = row['ajbh']
                pdf_path = f"{input_dir}/{ajbh}.pdf"
                
                # 检查文件是否存在
                if os.path.exists(pdf_path):
                    pdf_files.append({
                        'ajbh': ajbh,
                        'pdf_path': pdf_path,
                        'filename': f"{ajbh}.pdf"
                    })
                else:
                    self.logger.warning(f"PDF文件不存在: {pdf_path}")
            
            cursor.close()
            connection.close()
            
            self.logger.info(f"批次 {batch_id} 找到 {len(pdf_files)} 个PDF文件")
            return pdf_files
            
        except Exception as e:
            self.logger.error(f"获取PDF文件信息失败: {e}")
            raise
    
    def create_batch_slices(self, batch_id: str) -> List[Dict[str, Any]]:
        """
        创建分片批目录结构并分配PDF文件
        
        Args:
            batch_id: 批次号
            
        Returns:
            分片批信息列表
        """
        try:
            # 获取PDF文件列表
            pdf_files = self.get_pdf_files_from_batch(batch_id)
            
            if not pdf_files:
                self.logger.warning(f"批次 {batch_id} 没有找到PDF文件")
                return []
            
            # 计算分片批数量
            total_files = len(pdf_files)
            slice_count = (total_files + self.batch_slice_size - 1) // self.batch_slice_size
            
            self.logger.info(f"批次 {batch_id}: 总文件数 {total_files}, 分片批大小 {self.batch_slice_size}, 分片批数量 {slice_count}")
            
            batch_slices = []
            
            # 为每个分片批创建目录并分配文件
            for ppid in range(1, slice_count + 1):
                start_idx = (ppid - 1) * self.batch_slice_size
                end_idx = min(start_idx + self.batch_slice_size, total_files)
                slice_files = pdf_files[start_idx:end_idx]
                
                # 创建分片批目录
                slice_input_dir = f"{self.ocr_base_path}/{batch_id}/{ppid}/input"
                slice_output_dir = f"{self.ocr_base_path}/{batch_id}/{ppid}/output"
                
                os.makedirs(slice_input_dir, exist_ok=True)
                os.makedirs(slice_output_dir, exist_ok=True)
                
                # 移动PDF文件到分片批目录
                moved_files = []
                for file_info in slice_files:
                    src_path = file_info['pdf_path']
                    dst_path = f"{slice_input_dir}/{file_info['filename']}"
                    
                    try:
                        if os.path.exists(src_path):
                            shutil.move(src_path, dst_path)
                            moved_files.append(file_info['ajbh'])
                            self.logger.debug(f"移动文件: {src_path} -> {dst_path}")
                        else:
                            self.logger.warning(f"源文件不存在: {src_path}")
                    except Exception as e:
                        self.logger.error(f"移动文件失败 {src_path}: {e}")
                
                # 设置目录权限
                try:
                    os.chmod(slice_input_dir, 0o777)
                    os.chmod(slice_output_dir, 0o777)
                except Exception as e:
                    self.logger.warning(f"设置目录权限失败: {e}")
                
                batch_slice_info = {
                    'ppid': ppid,
                    'batch_id': batch_id,
                    'input_dir': slice_input_dir,
                    'output_dir': slice_output_dir,
                    'file_count': len(moved_files),
                    'ajbh_list': moved_files
                }
                
                batch_slices.append(batch_slice_info)
                
                self.logger.info(f"分片批 {ppid} 创建完成: {len(moved_files)} 个文件")
            
            return batch_slices
            
        except Exception as e:
            self.logger.error(f"创建分片批失败: {e}")
            raise
    
    def get_batch_slice_info(self, batch_id: str, ppid: int) -> Dict[str, Any]:
        """
        获取指定分片批的信息
        
        Args:
            batch_id: 批次号
            ppid: 片批ID
            
        Returns:
            分片批信息
        """
        slice_input_dir = f"{self.ocr_base_path}/{batch_id}/{ppid}/input"
        slice_output_dir = f"{self.ocr_base_path}/{batch_id}/{ppid}/output"
        
        # 获取输入目录中的PDF文件
        pdf_files = []
        ajbh_list = []
        
        if os.path.exists(slice_input_dir):
            for filename in os.listdir(slice_input_dir):
                if filename.endswith('.pdf'):
                    pdf_files.append(filename)
                    # 从文件名提取案件编号（去掉.pdf后缀）
                    ajbh = filename[:-4]
                    ajbh_list.append(ajbh)
        
        return {
            'ppid': ppid,
            'batch_id': batch_id,
            'input_dir': slice_input_dir,
            'output_dir': slice_output_dir,
            'file_count': len(pdf_files),
            'ajbh_list': ajbh_list,
            'pdf_files': pdf_files
        }
    
    def cleanup_original_input_dir(self, batch_id: str):
        """
        清理原始输入目录（在分片批分配完成后）
        
        Args:
            batch_id: 批次号
        """
        try:
            original_input_dir = f"{self.ocr_base_path}/{batch_id}/input"
            
            if os.path.exists(original_input_dir):
                # 检查是否还有剩余文件
                remaining_files = [f for f in os.listdir(original_input_dir) if f.endswith('.pdf')]
                
                if remaining_files:
                    self.logger.warning(f"原始输入目录中还有 {len(remaining_files)} 个PDF文件未分配")
                    for filename in remaining_files:
                        self.logger.warning(f"未分配文件: {filename}")
                else:
                    self.logger.info(f"原始输入目录已清空: {original_input_dir}")
            
        except Exception as e:
            self.logger.error(f"清理原始输入目录失败: {e}")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    slicer = PDFBatchSlicer()
    
    # 示例：为批次创建分片批
    batch_id = "test_batch"
    slices = slicer.create_batch_slices(batch_id)
    
    for slice_info in slices:
        print(f"分片批 {slice_info['ppid']}: {slice_info['file_count']} 个文件")
