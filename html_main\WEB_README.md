# 案件关系图查看器 - Web版本

基于Flask的网页应用，用于查看MySQL数据库中的案件关系数据和Mermaid关系图。**显示所有数据库字段**。

## 🚀 快速启动

### 方法1：使用启动脚本（推荐）
```bash
python start_web.py
```

### 方法2：直接运行
```bash
# 安装依赖
pip install -r web_requirements.txt

# 启动应用
python web_case_viewer.py
```

## 📋 数据库字段显示

本Web应用显示 `ds_case_relation` 表的**所有18个字段**：

| 字段名 | 说明 | 显示位置 |
|--------|------|----------|
| `batchid` | 数据批次号 | 列表页、详情页、统计页 |
| `ajbh` | 案件编号 | 列表页、详情页 |
| `ajmc` | 案件名称 | 列表页、详情页 |
| `rksj` | 入库时间 | 列表页、详情页 |
| `ajlx` | 案件类型 | 列表页、详情页 |
| `flwsxzdz` | 法律文书下载地址 | 列表页、详情页 |
| `tbrksj` | 同步入库时间 | 列表页、详情页 |
| `ajnr` | 案件内容 | 列表页（摘要）、详情页（完整） |
| `code` | 关系图代码 | 详情页 |
| `lastcode` | 最终的关系图代码 | 详情页、关系图查看 |
| `updater` | 修改人员 | 列表页、详情页 |
| `updatetype` | 修改类型 | 列表页、详情页、统计页 |
| `status` | AI处理状态 | 列表页、详情页、统计页 |
| `starttime` | AI开始处理时间 | 列表页、详情页 |
| `endtime` | AI完成处理时间 | 列表页、详情页 |
| `updatetime` | 更新时间 | 列表页、详情页 |
| `nums` | AI重跑次数 | 列表页、详情页 |
| `error` | 错误信息 | 列表页、详情页 |

## 📋 功能特性

### 🔍 案件数据查看
- **完整字段显示**：显示数据库表的所有16个字段
- **分页浏览**：支持分页显示案件列表
- **搜索筛选**：按案件编号、名称搜索，按状态筛选
- **详情查看**：点击案件编号查看完整详情
- **响应式设计**：支持桌面和移动设备

### 🎨 关系图查看
- **一键跳转**：点击"关系图"按钮直接跳转到Mermaid Live Editor
- **本地优先**：优先使用本地Mermaid Live Editor，失败时回退到在线版本
- **内嵌预览**：支持在网页内直接渲染Mermaid图表
- **多种操作**：下载SVG、复制代码、查看源码
- **双代码显示**：同时显示原始代码(`code`)和最终代码(`lastcode`)

### 📊 统计分析
- **状态统计**：各种处理状态的数量统计
- **修改类型统计**：AI生成 vs 人工修改统计
- **趋势分析**：每日处理数量趋势图
- **可视化图表**：饼图、折线图等多种图表

## 🔧 配置说明

### 数据库配置
在 `web_case_viewer.py` 中修改数据库配置：
```python
DB_CONFIG = {
    'host': '*************',      # 数据库主机
    'user': 'root',               # 数据库用户名
    'password': '123456',         # 数据库密码
    'database': 'djzs_db',        # 数据库名
    'charset': 'utf8mb4'          # 字符集
}
```

### Mermaid Live Editor配置
```python
MERMAID_LIVE_EDITOR_URL = "http://localhost:8080"  # 本地Mermaid Live Editor地址
```

## 📱 页面说明

### 主页 - 案件列表
- **URL**: `http://localhost:5000/`
- **功能**: 显示案件关系数据列表
- **特性**: 搜索、筛选、分页、状态显示

### 关系图查看
- **URL**: `http://localhost:5000/mermaid/<案件编号>`
- **功能**: 查看具体案件的Mermaid关系图
- **特性**: 多种查看方式、下载、复制代码

### 统计分析
- **URL**: `http://localhost:5000/stats`
- **功能**: 显示处理统计和分析图表
- **特性**: 实时统计、可视化图表

### API接口
- **URL**: `http://localhost:5000/api/case/<案件编号>`
- **功能**: 获取案件JSON数据
- **用途**: 供其他系统调用

## 🎯 使用场景

### 1. 案件数据浏览
- 查看所有案件的处理状态
- 搜索特定案件
- 筛选不同状态的案件

### 2. 关系图查看
- 点击"关系图"按钮查看案件关系图
- 在Mermaid Live Editor中编辑和优化
- 下载关系图为SVG格式

### 3. 处理监控
- 监控案件处理进度
- 查看错误信息和重跑次数
- 分析处理效率和成功率

### 4. 数据分析
- 查看处理统计
- 分析处理趋势
- 监控批次处理情况

## 🔗 Mermaid Live Editor集成

### 本地部署（推荐）
如果你已经本地部署了Mermaid Live Editor：
1. 确保服务运行在 `http://localhost:8080`
2. 点击"关系图"按钮会直接跳转到本地编辑器
3. 支持实时编辑和预览

### 在线版本（备选）
如果本地版本不可用，会自动跳转到：
- `https://mermaid.live/edit#<编码后的图表>`

### 内嵌查看（兜底）
如果跳转失败，会在网页内直接渲染图表：
- 使用Mermaid.js库渲染
- 支持下载SVG
- 支持查看和复制源码

## 🛠 技术栈

- **后端**: Flask + PyMySQL
- **前端**: Bootstrap 5 + Chart.js + Mermaid.js
- **数据库**: MySQL
- **图表**: Mermaid Live Editor

## 📝 注意事项

1. **数据库权限**: 确保数据库用户有读取权限
2. **网络访问**: 如需外网访问，请配置防火墙
3. **本地编辑器**: 确保Mermaid Live Editor正常运行
4. **浏览器兼容**: 推荐使用Chrome、Firefox等现代浏览器

## 🚨 故障排除

### 数据库连接失败
- 检查数据库配置是否正确
- 确认数据库服务是否运行
- 验证用户权限

### 关系图无法显示
- 检查`lastcode`字段是否有数据
- 验证Mermaid代码语法
- 确认本地Mermaid Live Editor状态

### 页面加载缓慢
- 检查数据库查询性能
- 考虑添加数据库索引
- 调整分页大小

## 📞 支持

如有问题，请检查：
1. 数据库连接配置
2. 依赖包安装情况
3. 端口占用情况
4. 浏览器控制台错误信息
