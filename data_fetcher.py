#!/usr/bin/env python3
"""
数据获取模块
从数据库筛选案件数据并插入到案件关系表
"""

import pymysql
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from config import config


class DataFetcher:
    """数据获取器"""
    
    def __init__(self):
        self.db_config = config.get_db_config()
        self.table_config = config.get_table_config()
        self.logger = logging.getLogger(__name__)

        # 表名配置
        self.source_table = self.table_config['source_table']
        self.case_relation_table = self.table_config['case_relation_table']
        self.case_details_table = self.table_config['case_details_table']
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def generate_batch_id(self) -> str:
        """生成批次号 - 格式：2025073009461901"""
        now = datetime.now()
        # 格式：YYYYMMDDHHMISS + 01
        return now.strftime("%Y%m%d%H%M%S") + "01"
    
    def fetch_cases_by_time_range(self, stime: str, etime: str, ajbh: str = None) -> Dict[str, Any]:
        """
        根据时间范围筛选案件数据
        按时间范围取出案件编号，再查询该案件编号的所有历史数据

        Args:
            stime: 开始时间
            etime: 结束时间
            ajbh: 案件编号（可选）

        Returns:
            筛选结果
        """
        try:
            # 构建SQL - 先按时间范围取案件编号，再查询所有历史数据
            if ajbh:
                # 时间范围 + 案件编号
                base_sql = f"""
                select t2.ajbh, t2.xxzjbh, t2.ajmc, t2.flwsxzdz, t2.llsj, t2.ajlx, DATE(t2.tbrksj) as tbrksjdate
                from (
                      SELECT  ajbh
                      FROM `{self.source_table}`
                      WHERE flwsxzdz <> ''
                        AND flwsxzdz IS NOT NULL
                        -- flwszldm = '02060403'
                        AND (tbrksj BETWEEN %s AND %s   OR  tbgxsj BETWEEN %s AND %s )
                        AND ajbh = %s
                      group by ajbh
                      )t1 ,
                  (
                   SELECT  ajbh, xxzjbh, ajmc, flwsxzdz, llsj, ajlx, tbrksj, tbgxsj
                   FROM `{self.source_table}`
                   WHERE flwsxzdz <> ''
                     AND flwsxzdz IS NOT NULL
                     -- flwszldm = '02060403'
                   )t2
                where t1.ajbh = t2.ajbh
                """
                params = [stime, etime, stime, etime, ajbh]
            else:
                # 仅时间范围
                base_sql = f"""
                select t2.ajbh, t2.xxzjbh, t2.ajmc, t2.flwsxzdz, t2.llsj, t2.ajlx, DATE(t2.tbrksj) as tbrksjdate
                from (
                      SELECT  ajbh
                      FROM `{self.source_table}`
                      WHERE flwsxzdz <> ''
                        AND flwsxzdz IS NOT NULL
                        -- flwszldm = '02060403'
                        AND (tbrksj BETWEEN %s AND %s   OR  tbgxsj BETWEEN %s AND %s )
                      group by ajbh
                      )t1 ,
                  (
                   SELECT  ajbh, xxzjbh, ajmc, flwsxzdz, llsj, ajlx, tbrksj, tbgxsj
                   FROM `{self.source_table}`
                   WHERE flwsxzdz <> ''
                     AND flwsxzdz IS NOT NULL
                     -- flwszldm = '02060403'
                   )t2
                where t1.ajbh = t2.ajbh
                """
                params = [stime, etime, stime, etime]

            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(base_sql, params)
                    cases = cursor.fetchall()

            self.logger.info(f"筛选到 {len(cases)} 条记录，时间范围: {stime} - {etime}")

            return {
                "status": "success",
                "total_count": len(cases),
                "cases": cases,
                "time_range": f"{stime} - {etime}",
                "ajbh_filter": ajbh
            }

        except Exception as e:
            self.logger.error(f"数据筛选失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "cases": []
            }
    
    def fetch_cases_by_ajbh(self, ajbh: str) -> Dict[str, Any]:
        """
        根据案件编号筛选案件数据
        查询该案件编号的所有历史数据

        Args:
            ajbh: 案件编号

        Returns:
            筛选结果
        """
        try:
            sql = f"""
            SELECT ajbh, xxzjbh, ajmc, flwsxzdz, llsj, ajlx, DATE(tbrksj) as tbrksjdate
            FROM `{self.source_table}`
            WHERE flwsxzdz <> ''
              AND flwsxzdz IS NOT NULL
              -- flwszldm = '02060403'
              AND ajbh = %s
            """

            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql, (ajbh,))
                    cases = cursor.fetchall()

            self.logger.info(f"根据案件编号筛选到 {len(cases)} 条记录: {ajbh}")

            return {
                "status": "success",
                "total_count": len(cases),
                "cases": cases,
                "ajbh_filter": ajbh
            }

        except Exception as e:
            self.logger.error(f"根据案件编号筛选失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "cases": []
            }
    
    def insert_case_relations(self, cases: List[Dict[str, Any]], batch_id: str,
                             stime: str = None, etime: str = None, ajbh: str = None) -> Dict[str, Any]:
        """
        将筛选的案件数据插入到案件关系表
        使用软删除机制确保每个ajbh只有一条有效数据
        按ajbh分组，xxzjbh和flwsxzdz做数组合并

        Args:
            cases: 案件数据列表
            batch_id: 批次号
            stime: 开始时间（可选）
            etime: 结束时间（可选）
            ajbh: 案件编号（可选）

        Returns:
            插入结果
        """
        try:
            if not cases:
                return {
                    "status": "error",
                    "message": "没有案件数据需要插入"
                }

            # 从cases中提取所有ajbh，用于批量软删除
            ajbh_list = list(set([case['ajbh'] for case in cases]))
            self.logger.info(f"准备处理 {len(ajbh_list)} 个不同的案件编号")

            with self.get_connection() as connection:
                with connection.cursor() as cursor:

                    # 1. 批量软删除现有记录
                    if ajbh_list:
                        self.logger.info("执行批量软删除...")
                        placeholders = ','.join(['%s'] * len(ajbh_list))
                        
                        # 案件关系图表
                        soft_delete_sql_relation = f"""
                        UPDATE `{self.case_relation_table}`
                        SET isdelete = '1', updatetime = NOW()
                        WHERE ajbh IN ({placeholders}) AND (isdelete IS NULL OR isdelete = '0')
                        """
                        affected_rows = cursor.execute(soft_delete_sql_relation, ajbh_list)
                        self.logger.info(f"案件关系图表:软删除了 {affected_rows} 条现有记录")

                        # 案件详细信息表
                        soft_delete_sql_details = f"""
                        UPDATE `{self.case_details_table}`
                        SET isdelete = '1', update_time = NOW()
                        WHERE ajbh IN ({placeholders}) AND (isdelete IS NULL OR isdelete = '0')
                        """
                        affected_rows = cursor.execute(soft_delete_sql_details, ajbh_list)
                        self.logger.info(f"案件详细信息表:软删除了 {affected_rows} 条现有记录")

                    # 2. 执行批量插入
                    self.logger.info("执行批量插入新记录...")

                    # 构建插入SQL（避免重复读取大表，直接使用传入的cases数据）
                    insert_sql = f"""
                    INSERT INTO `{self.case_relation_table}`
                    (batchid, ajbh, ajmc, llsj, counts, xxzjbh, ajlx, flwsxzdz, tbrksj, tbgxsj, status, starttime, nums, isdelete)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """

                    # 按ajbh分组处理cases数据
                    grouped_cases = {}
                    for case in cases:
                        ajbh = case['ajbh']
                        if ajbh not in grouped_cases:
                            grouped_cases[ajbh] = []
                        grouped_cases[ajbh].append(case)

                    # 准备批量插入数据
                    insert_data = []
                    for ajbh, case_list in grouped_cases.items():
                        # 合并同一案件的数据
                        xxzjbh_list = [case.get('xxzjbh', '') for case in case_list if case.get('xxzjbh')]
                        flwsxzdz_list = [case.get('flwsxzdz', '') for case in case_list if case.get('flwsxzdz')]

                        # 取第一个案件的基本信息
                        first_case = case_list[0]

                        from datetime import datetime

                        insert_record = (
                            batch_id,                                    # batchid
                            ajbh,                                       # ajbh
                            first_case.get('ajmc', ''),                # ajmc
                            first_case.get('llsj'),                    # llsj (录入时间)
                            len(xxzjbh_list),                          # counts
                            ','.join(xxzjbh_list),                     # xxzjbh
                            first_case.get('ajlx', ''),                # ajlx
                            ','.join(flwsxzdz_list),                   # flwsxzdz
                            first_case.get('tbrksj'),                  # tbrksj
                            datetime.now(),                            # tbgxsj (同步更新时间)
                            '0',                                       # status
                            datetime.now(),                            # starttime
                            0,                                         # nums
                            '0'                                        # isdelete
                        )
                        insert_data.append(insert_record)

                    # 执行批量插入
                    affected_rows = cursor.executemany(insert_sql, insert_data)
                    connection.commit()

                    self.logger.info(f"成功插入 {len(insert_data)} 条新记录")

                    return {
                        "status": "success",
                        "message": f"成功处理 {len(insert_data)} 个案件",
                        "batch_id": batch_id,
                        "affected_rows": len(insert_data),
                        "soft_deleted": affected_rows if 'affected_rows' in locals() else 0
                    }

        except Exception as e:
            self.logger.error(f"插入案件关系数据失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "batch_id": batch_id
            }

    def get_cases_for_processing(self, batch_id: str) -> List[Dict[str, Any]]:
        """
        获取需要处理的案件列表
        
        Args:
            batch_id: 批次号
            
        Returns:
            案件列表
        """
        try:
            sql = f"""
            SELECT batchid, ajbh, ajmc, llsj, tbgxsj, counts, xxzjbh, ajlx, flwsxzdz, tbrksj
            FROM `{self.case_relation_table}`
            WHERE batchid = %s AND status = 0 AND (isdelete IS NULL OR isdelete = '0')
            """
            
            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql, (batch_id,))
                    cases = cursor.fetchall()
            
            self.logger.info(f"获取到 {len(cases)} 个需要处理的案件，批次: {batch_id}")
            return cases
            
        except Exception as e:
            self.logger.error(f"获取处理案件列表失败: {e}")
            return []

def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    fetcher = DataFetcher()
    
    # 测试生成批次号
    batch_id = fetcher.generate_batch_id()
    print(f"生成的批次号: {batch_id}")
    
    # 测试数据库连接
    try:
        connection = fetcher.get_connection()
        connection.close()
        print("数据库连接测试成功")
    except Exception as e:
        print(f"数据库连接测试失败: {e}")


if __name__ == "__main__":
    main()
