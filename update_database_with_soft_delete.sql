-- 更新数据库表结构，添加软删除支持
-- 确保每个ajbh只有一条有效数据

USE `djzs_db`;

-- 1. 检查当前表结构
DESCRIBE `ds_case_relation`;

-- 2. 添加缺失的字段（如果不存在）
-- 添加填发时间字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN IF NOT EXISTS `tfsj` datetime DEFAULT NULL COMMENT '填发时间' AFTER `ajmc`;

-- 添加修改时间字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN IF NOT EXISTS `xgsj` datetime DEFAULT NULL COMMENT '修改时间' AFTER `tfsj`;

-- 添加意见书数量字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN IF NOT EXISTS `counts` int DEFAULT NULL COMMENT '意见书数量' AFTER `xgsj`;

-- 添加信息主键编号字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN IF NOT EXISTS `xxzjbh` varchar(250) DEFAULT NULL COMMENT '信息主键编号' AFTER `counts`;

-- 添加软删除字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN IF NOT EXISTS `isdelete` varchar(10) DEFAULT '0' COMMENT '是否删除，0 否，1是' AFTER `error`;

-- 3. 添加索引优化查询性能
-- 软删除索引
ALTER TABLE `ds_case_relation` 
ADD INDEX IF NOT EXISTS `idx_isdelete` (`isdelete`);

-- 案件编号+软删除复合索引（用于确保唯一性）
ALTER TABLE `ds_case_relation` 
ADD INDEX IF NOT EXISTS `idx_ajbh_isdelete` (`ajbh`, `isdelete`);

-- 状态+软删除复合索引（用于查询处理中的案件）
ALTER TABLE `ds_case_relation` 
ADD INDEX IF NOT EXISTS `idx_status_isdelete` (`status`, `isdelete`);

-- 批次+状态+软删除复合索引（用于批次处理）
ALTER TABLE `ds_case_relation` 
ADD INDEX IF NOT EXISTS `idx_batch_status_isdelete` (`batchid`, `status`, `isdelete`);

-- 4. 数据迁移（如果需要）
-- 如果存在rksj字段，将数据迁移到tfsj
-- UPDATE `ds_case_relation` SET `tfsj` = `rksj` WHERE `tfsj` IS NULL AND `rksj` IS NOT NULL;

-- 5. 初始化现有数据的软删除字段
UPDATE `ds_case_relation` SET `isdelete` = '0' WHERE `isdelete` IS NULL;

-- 6. 处理重复数据（保留最新的记录）
-- 标记重复的旧记录为删除状态
UPDATE `ds_case_relation` r1
JOIN (
    SELECT ajbh, MAX(starttime) as max_starttime
    FROM `ds_case_relation`
    WHERE isdelete = '0'
    GROUP BY ajbh
    HAVING COUNT(*) > 1
) r2 ON r1.ajbh = r2.ajbh
SET r1.isdelete = '1', r1.updatetime = NOW()
WHERE r1.starttime < r2.max_starttime AND r1.isdelete = '0';

-- 7. 验证表结构
DESCRIBE `ds_case_relation`;

-- 8. 验证数据完整性
-- 检查每个ajbh是否只有一条有效记录
SELECT ajbh, COUNT(*) as active_count
FROM `ds_case_relation`
WHERE isdelete = '0'
GROUP BY ajbh
HAVING COUNT(*) > 1
LIMIT 10;

-- 9. 统计信息
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN isdelete = '0' THEN 1 ELSE 0 END) as active_records,
    SUM(CASE WHEN isdelete = '1' THEN 1 ELSE 0 END) as deleted_records
FROM `ds_case_relation`;

-- 10. 显示最新的几条记录
SELECT batchid, ajbh, ajmc, tfsj, counts, status, isdelete, starttime
FROM `ds_case_relation`
WHERE isdelete = '0'
ORDER BY starttime DESC
LIMIT 10;

-- 11. 创建视图简化查询（可选）
CREATE OR REPLACE VIEW `ds_case_relation_active` AS
SELECT batchid, ajbh, ajmc, llsj, counts, xxzjbh, ajlx, flwsxzdz, tbrksj, tbgxsj,
       ajnr, code, lastcode, updater, codesource, status, starttime, endtime,
       updatetime, nums, error
FROM `ds_case_relation`
WHERE isdelete = '0';

-- 12. 验证视图
SELECT COUNT(*) as active_view_count FROM `ds_case_relation_active`;

COMMIT;
