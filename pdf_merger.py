#!/usr/bin/env python3
"""
PDF合并智能体
合并同一案件的多个PDF文件
"""

import os
import glob
import logging
from pathlib import Path
from typing import List, Dict, Any
from collections import defaultdict
from config import config

try:
    from PyPDF2 import PdfReader, PdfWriter
except ImportError:
    try:
        import PyPDF2
        from PyPDF2 import PdfFileReader as PdfReader, PdfFileWriter as PdfWriter
    except ImportError:
        print("❌ 需要安装PyPDF2库")
        print("请运行: pip install PyPDF2")
        raise ImportError("PyPDF2库未安装")


class PDFMerger:
    """PDF合并智能体"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system_config = config.get_system_config()
        self.ocr_base_path = self.system_config['ocr_base_path']
    
    def find_pdf_groups(self, directory: str) -> Dict[str, List[str]]:
        """
        在指定目录中查找需要合并的PDF文件组
        按案件编号（ajbh）分组

        Args:
            directory: 目录路径

        Returns:
            dict: {案件编号: [文件路径列表]}
        """
        pdf_groups = defaultdict(list)

        # 查找所有PDF文件
        pdf_pattern = os.path.join(directory, "*.pdf")
        pdf_files = glob.glob(pdf_pattern)

        self.logger.info(f"在目录 {directory} 中找到 {len(pdf_files)} 个PDF文件")

        for file_path in pdf_files:
            filename = os.path.basename(file_path)
            self.logger.info(f"  处理文件: {filename}")

            # 解析文件名：ajbh_xxzjbh.pdf
            if '_' in filename and filename.endswith('.pdf'):
                ajbh = filename.split('_')[0]
                pdf_groups[ajbh].append(file_path)
                self.logger.info(f"    归类到案件: {ajbh}")
            else:
                self.logger.warning(f"    文件名格式不符合预期: {filename}")

        # 返回所有文件组（包括单个文件的组）
        self.logger.info(f"找到 {len(pdf_groups)} 个案件的PDF文件:")
        for ajbh, files in pdf_groups.items():
            self.logger.info(f"  案件 {ajbh}: {len(files)} 个文件")

        return dict(pdf_groups)
    
    def merge_pdfs_for_case(self, ajbh: str, file_list: List[str], output_dir: str) -> Dict[str, Any]:
        """
        合并单个案件的PDF文件
        
        Args:
            ajbh: 案件编号
            file_list: 文件路径列表
            output_dir: 输出目录
            
        Returns:
            合并结果
        """
        try:
            # 按文件名排序
            file_list.sort()

            output_path = os.path.join(output_dir, f"{ajbh}.pdf")

            self.logger.info(f"开始处理案件 {ajbh} 的 {len(file_list)} 个PDF文件")

            # 如果只有一个文件，直接重命名
            if len(file_list) == 1:
                source_file = file_list[0]
                if source_file != output_path:
                    import shutil
                    shutil.copy2(source_file, output_path)
                    self.logger.info(f"单个文件已重命名: {os.path.basename(source_file)} -> {ajbh}.pdf")

                    # 获取页数信息
                    try:
                        reader = PdfReader(output_path)
                        total_pages = len(reader.pages)
                    except:
                        total_pages = 0

                    return {
                        "status": "success",
                        "ajbh": ajbh,
                        "output_path": output_path,
                        "total_pages": total_pages,
                        "source_files": 1,
                        "message": f"单个文件已重命名，共 {total_pages} 页"
                    }

            # 多个文件，执行合并
            writer = PdfWriter()
            total_pages = 0
            
            for file_path in file_list:
                filename = os.path.basename(file_path)
                self.logger.info(f"  处理文件: {filename}")
                
                try:
                    reader = PdfReader(file_path)
                    page_count = len(reader.pages)
                    total_pages += page_count
                    
                    # 添加所有页面
                    for page in reader.pages:
                        writer.add_page(page)
                    
                    self.logger.info(f"    ✅ 已添加 {page_count} 页")
                    
                except Exception as e:
                    self.logger.error(f"    ❌ 处理文件失败: {e}")
                    return {
                        "status": "error",
                        "ajbh": ajbh,
                        "error": f"处理文件 {filename} 失败: {str(e)}"
                    }
            
            # 写入合并后的PDF
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)
            
            self.logger.info(f"✅ 案件 {ajbh} 合并完成: {output_path}")
            self.logger.info(f"📊 总页数: {total_pages}")
            
            return {
                "status": "success",
                "ajbh": ajbh,
                "output_path": output_path,
                "total_pages": total_pages,
                "source_files": len(file_list),
                "message": f"成功合并 {len(file_list)} 个文件，共 {total_pages} 页"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 案件 {ajbh} 合并失败: {e}")
            return {
                "status": "error",
                "ajbh": ajbh,
                "error": str(e)
            }
    
    def batch_merge_pdfs(self, batch_id: str, input_dir: str = None, output_dir: str = None,
                        delete_originals: bool = False) -> Dict[str, Any]:
        """
        批量合并PDF文件

        Args:
            batch_id: 批次号
            input_dir: 输入目录（如果为None，使用ocr_base_path配置）
            output_dir: 输出目录（默认为输入目录）
            delete_originals: 是否删除原文件

        Returns:
            合并结果统计
        """
        try:
            # 如果没有指定输入目录，使用配置的路径
            if input_dir is None:
                input_dir = f"{self.ocr_base_path}/{batch_id}/input"

            # 如果没有指定输出目录，使用输入目录
            if output_dir is None:
                output_dir = input_dir

            self.logger.info(f"PDF合并 - 批次: {batch_id}")
            self.logger.info(f"输入目录: {input_dir}")
            self.logger.info(f"输出目录: {output_dir}")

            # 确保输入目录存在
            if not os.path.exists(input_dir):
                self.logger.error(f"输入目录不存在: {input_dir}")
                return {
                    "status": "error",
                    "batch_id": batch_id,
                    "error": f"输入目录不存在: {input_dir}"
                }

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 查找PDF文件组
            pdf_groups = self.find_pdf_groups(input_dir)
            
            if not pdf_groups:
                self.logger.info("没有找到PDF文件")
                return {
                    "status": "success",
                    "batch_id": batch_id,
                    "message": "没有找到PDF文件",
                    "successful_count": 0,
                    "failed_count": 0,
                    "successful_merges": [],
                    "failed_merges": []
                }
            
            self.logger.info(f"找到 {len(pdf_groups)} 个案件需要合并PDF")
            
            successful_merges = []
            failed_merges = []
            
            for ajbh, file_list in pdf_groups.items():
                self.logger.info(f"\n处理案件: {ajbh} ({len(file_list)} 个文件)")
                
                # 执行合并
                result = self.merge_pdfs_for_case(ajbh, file_list, output_dir)
                
                if result["status"] == "success":
                    successful_merges.append(result)
                    
                    # 删除原文件（如果指定）
                    if delete_originals:
                        self.logger.info(f"删除原文件:")
                        for file_path in file_list:
                            try:
                                os.remove(file_path)
                                self.logger.info(f"  ✅ 已删除: {os.path.basename(file_path)}")
                            except Exception as e:
                                self.logger.warning(f"  ❌ 删除失败: {os.path.basename(file_path)} - {e}")
                else:
                    failed_merges.append(result)
            
            self.logger.info(f"\n批量合并完成 - 成功: {len(successful_merges)}, 失败: {len(failed_merges)}")
            
            return {
                "status": "success",
                "batch_id": batch_id,
                "input_dir": input_dir,
                "output_dir": output_dir,
                "total_cases": len(pdf_groups),
                "successful_count": len(successful_merges),
                "failed_count": len(failed_merges),
                "successful_merges": successful_merges,
                "failed_merges": failed_merges,
                "summary": f"成功合并 {len(successful_merges)}/{len(pdf_groups)} 个案件的PDF文件"
            }
            
        except Exception as e:
            self.logger.error(f"批量合并失败: {e}")
            return {
                "status": "error",
                "batch_id": batch_id,
                "error": str(e)
            }
    
    def merge_case_pdfs(self, ajbh: str, batch_id: str = None, input_dir: str = None, output_dir: str = None) -> Dict[str, Any]:
        """
        合并指定案件的PDF文件

        Args:
            ajbh: 案件编号
            batch_id: 批次号（如果提供，用于构建路径）
            input_dir: 输入目录（如果为None且提供了batch_id，使用ocr_base_path配置）
            output_dir: 输出目录（默认为输入目录）

        Returns:
            合并结果
        """
        try:
            # 如果没有指定输入目录但提供了batch_id，使用配置的路径
            if input_dir is None and batch_id:
                input_dir = f"{self.ocr_base_path}/{batch_id}/input"
            elif input_dir is None:
                raise ValueError("必须提供 input_dir 或 batch_id")

            if output_dir is None:
                output_dir = input_dir

            self.logger.info(f"合并案件 {ajbh} 的PDF文件")
            self.logger.info(f"输入目录: {input_dir}")
            self.logger.info(f"输出目录: {output_dir}")
            
            # 查找该案件的PDF文件
            pattern = os.path.join(input_dir, f"{ajbh}_*.pdf")
            files = glob.glob(pattern)
            
            if not files:
                return {
                    "status": "error",
                    "ajbh": ajbh,
                    "error": f"没有找到案件 {ajbh} 的PDF文件"
                }
            
            if len(files) == 1:
                # 只有一个文件，直接重命名
                source_file = files[0]
                target_file = os.path.join(output_dir, f"{ajbh}.pdf")
                
                if source_file != target_file:
                    import shutil
                    shutil.copy2(source_file, target_file)
                
                return {
                    "status": "success",
                    "ajbh": ajbh,
                    "output_path": target_file,
                    "total_pages": 0,  # 不统计页数
                    "source_files": 1,
                    "message": "单个文件，已重命名"
                }
            
            # 多个文件，执行合并
            return self.merge_pdfs_for_case(ajbh, files, output_dir)
            
        except Exception as e:
            self.logger.error(f"合并案件 {ajbh} 的PDF失败: {e}")
            return {
                "status": "error",
                "ajbh": ajbh,
                "error": str(e)
            }


def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
    
    merger = PDFMerger()
    
    # 测试合并
    test_dir = "./test_pdfs"
    if os.path.exists(test_dir):
        result = merger.batch_merge_pdfs("test_batch", test_dir)
        print(f"合并结果: {result}")
    else:
        print(f"测试目录 {test_dir} 不存在")


if __name__ == "__main__":
    main()
