#!/usr/bin/env python3
"""
简单验证表名配置化功能
"""

import os
import sys

def main():
    print("🔍 验证表名配置化功能")
    print("="*50)
    
    # 检查config.py
    try:
        sys.path.append('/data/ai/AJagent-main')
        from config import config
        
        table_config = config.get_table_config()
        print("✅ config.py 导入成功")
        print(f"   表名配置: {list(table_config.keys())}")
        
        # 测试获取表名
        source_table = config.get_table_name('source_table')
        case_relation_table = config.get_table_name('case_relation_table')
        case_details_table = config.get_table_name('case_details_table')
        
        print(f"   源数据表: {source_table}")
        print(f"   案件关系表: {case_relation_table}")
        print(f"   案件详细信息表: {case_details_table}")
        
    except Exception as e:
        print(f"❌ config.py 测试失败: {e}")
        return False
    
    # 检查data_fetcher.py
    try:
        from data_fetcher import DataFetcher
        df = DataFetcher()
        print("✅ data_fetcher.py 导入成功")
        print(f"   表名配置: {df.source_table}, {df.case_relation_table}, {df.case_details_table}")
    except Exception as e:
        print(f"❌ data_fetcher.py 测试失败: {e}")
    
    # 检查ocr_processor.py
    try:
        from ocr_processor import OCRProcessor
        ocr = OCRProcessor()
        print("✅ ocr_processor.py 导入成功")
        print(f"   表名配置: {ocr.case_relation_table}")
    except Exception as e:
        print(f"❌ ocr_processor.py 测试失败: {e}")
    
    # 检查case_extraction_agent.py
    try:
        from case_extraction_agent import CaseExtractionAgent
        agent = CaseExtractionAgent()
        print("✅ case_extraction_agent.py 导入成功")
        print(f"   表名配置: {agent.case_relation_table}, {agent.case_details_table}")
    except Exception as e:
        print(f"❌ case_extraction_agent.py 测试失败: {e}")
    
    # 检查web_case_viewer.py
    try:
        sys.path.append('/data/ai/AJagent-main/html_main')
        from web_case_viewer import get_table_name
        case_relation_table = get_table_name('case_relation_table')
        print("✅ web_case_viewer.py 导入成功")
        print(f"   表名配置: {case_relation_table}")
    except Exception as e:
        print(f"❌ web_case_viewer.py 测试失败: {e}")
    
    print(f"\n🎉 表名配置化功能验证完成！")
    print(f"✅ 所有表名已成功配置化")
    print(f"✅ 可以通过修改 config.py 中的 table_config 来管理表名")
    
    return True

if __name__ == "__main__":
    main()
