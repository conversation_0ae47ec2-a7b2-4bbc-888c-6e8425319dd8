#!/usr/bin/env python3
"""
Excel导入SQL配置文件
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': '***********',
    'user': 'root',
    'password': '123456',
    'database': 'djzs_db',
    'charset': 'utf8mb4',
    'port': 3306
}

# Excel字段映射配置
EXCEL_FIELD_MAPPING = {
    # Excel字段名 -> MySQL字段名
    '案件编号': 'ajbh',
    '数据版本号': 'xxzjbh'
}

# 内容字段配置（这些字段会被拼接）
CONTENT_FIELDS = [
    '正文内容',
    '到案情况', 
    '依法侦查查明',
    '犯罪证据',
    '综上所述',
    '其他说明'
]

# PDF链接配置
PDF_CONFIG = {
    'base_url': 'http://***********:9999/api/v1/pdfHandler/getPdf',
    'filename_template': '{case_number}_{version_number}.pdf',
    'url_template': 'http://***********:9999/api/v1/pdfHandler/getPdf?filename={filename}&action=view'
}

# 目标表配置
TARGET_TABLE = {
    'database': 'djzs_db',
    'table': 'ds_case_instrument_his_ai'
}

# 默认值配置
DEFAULT_VALUES = {
    'ajlx': 'AUTO_IMPORT',           # 案件类型
    'flwszldm': '02060403',          # 法律文书种类代码
    'ajmc_prefix': '案件_'           # 案件名称前缀
}

# 批处理配置
BATCH_CONFIG = {
    'batch_size': 100,               # 每批处理的记录数
    'commit_interval': 100,          # 提交间隔
    'log_interval': 50               # 日志输出间隔
}

# 日志配置
LOG_CONFIG = {
    'log_file': 'excel_to_sql.log',
    'log_level': 'INFO',
    'log_format': '%(asctime)s - %(levelname)s - %(message)s'
}

def get_config():
    """获取完整配置"""
    return {
        'database': DATABASE_CONFIG,
        'excel_mapping': EXCEL_FIELD_MAPPING,
        'content_fields': CONTENT_FIELDS,
        'pdf': PDF_CONFIG,
        'target_table': TARGET_TABLE,
        'defaults': DEFAULT_VALUES,
        'batch': BATCH_CONFIG,
        'log': LOG_CONFIG
    }

def validate_config():
    """验证配置有效性"""
    errors = []
    
    # 检查数据库配置
    required_db_fields = ['host', 'user', 'password', 'database']
    for field in required_db_fields:
        if not DATABASE_CONFIG.get(field):
            errors.append(f"数据库配置缺少字段: {field}")
    
    # 检查内容字段
    if not CONTENT_FIELDS:
        errors.append("内容字段列表不能为空")
    
    # 检查PDF配置
    if not PDF_CONFIG.get('url_template'):
        errors.append("PDF URL模板不能为空")
    
    return errors

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
    else:
        print("✅ 配置验证通过")
        
    # 显示当前配置
    config = get_config()
    print(f"\n📋 当前配置:")
    print(f"数据库: {config['database']['host']}:{config['database']['database']}")
    print(f"目标表: {config['target_table']['database']}.{config['target_table']['table']}")
    print(f"内容字段: {config['content_fields']}")
    print(f"PDF模板: {config['pdf']['url_template']}")
