#!/bin/bash

# Nginx PDF服务器设置脚本

echo "🔧 设置Nginx PDF服务器..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 检查Nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo "📦 安装Nginx..."
    
    # 检测系统类型
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        apt update
        apt install -y nginx
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum install -y nginx
    else
        echo "❌ 不支持的系统类型，请手动安装Nginx"
        exit 1
    fi
fi

# 检查PDF目录
PDF_DIR="/bigai/ai/AJagent-main/pdf"
if [ ! -d "$PDF_DIR" ]; then
    echo "❌ 目录不存在: $PDF_DIR"
    exit 1
fi

# 检查test.pdf文件
if [ -f "$PDF_DIR/test.pdf" ]; then
    echo "✅ 找到test.pdf文件"
else
    echo "⚠️  警告: 未找到test.pdf文件在 $PDF_DIR"
fi

# 复制配置文件
echo "📝 配置Nginx..."
cp nginx_pdf.conf /etc/nginx/sites-available/pdf-server

# 创建软链接（如果sites-enabled目录存在）
if [ -d /etc/nginx/sites-enabled ]; then
    ln -sf /etc/nginx/sites-available/pdf-server /etc/nginx/sites-enabled/
fi

# 测试Nginx配置
echo "🧪 测试Nginx配置..."
if nginx -t; then
    echo "✅ Nginx配置测试通过"
else
    echo "❌ Nginx配置测试失败"
    exit 1
fi

# 重启Nginx
echo "🔄 重启Nginx服务..."
systemctl restart nginx
systemctl enable nginx

# 检查服务状态
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx服务运行正常"
else
    echo "❌ Nginx服务启动失败"
    systemctl status nginx
    exit 1
fi

# 获取服务器IP
LOCAL_IP=$(hostname -I | awk '{print $1}')
if [ -z "$LOCAL_IP" ]; then
    LOCAL_IP="localhost"
fi

echo ""
echo "🎉 Nginx PDF服务器设置完成!"
echo ""
echo "🌐 访问地址:"
echo "   主页: http://$LOCAL_IP:8888/"
echo "   在线查看: http://$LOCAL_IP:8888/api/v1/pdfHandler/getPdf?filename=test.pdf&action=view"
echo "   直接下载: http://$LOCAL_IP:8888/api/v1/pdfHandler/getPdf?filename=test.pdf&action=download"
echo ""
echo "📁 PDF文件目录: $PDF_DIR"
echo ""
echo "🔧 管理命令:"
echo "   重启服务: sudo systemctl restart nginx"
echo "   查看状态: sudo systemctl status nginx"
echo "   查看日志: sudo tail -f /var/log/nginx/error.log"
