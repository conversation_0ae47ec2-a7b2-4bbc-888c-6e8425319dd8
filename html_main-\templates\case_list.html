{% extends "base.html" %}

{% block title %}案件列表 - 案件关系图查看器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-list-ul"></i> 案件关系数据</h2>
            <div class="text-muted">
                <div>共 {{ total_records }} 条记录</div>
                <div><small>显示所有 18 个数据字段 + 操作列</small></div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">搜索案件</label>
                        <input type="text" class="form-control search-box" id="search" name="search"
                               value="{{ search_term }}" placeholder="输入批次号、案件编号或名称">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态筛选</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="0" {% if status_filter == '0' %}selected{% endif %}>开始处理</option>
                            <option value="1" {% if status_filter == '1' %}selected{% endif %}>PDF下载完成</option>
                            <option value="2" {% if status_filter == '2' %}selected{% endif %}>OCR识别完成</option>
                            <option value="3" {% if status_filter == '3' %}selected{% endif %}>AI分析完成</option>
                            <option value="4" {% if status_filter == '4' %}selected{% endif %}>处理出错</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per_page" class="form-label">每页显示</label>
                        <select class="form-select" name="per_page">
                            <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 案件列表 -->
        {% if cases %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            <strong>显示所有22个字段</strong> - 表格可左右滚动查看所有列。建议使用横屏或大屏幕查看完整内容。
            <a href="/case_details" class="btn btn-sm btn-outline-primary ms-3">
                <i class="bi bi-table"></i> 查看案件详细信息
            </a>
        </div>

        <div class="table-responsive">
            <table class="table table-hover compact-table">
                <thead class="table-dark sticky-header">
                    <tr>
                        <th class="field-header" style="min-width: 80px;">批次号<br><small>batchid</small></th>
                        <th class="field-header" style="min-width: 100px;">案件编号<br><small>ajbh</small></th>
                        <th class="field-header" style="min-width: 120px;">案件名称<br><small>ajmc</small></th>
                        <th class="field-header" style="min-width: 100px;">填发时间<br><small>tfsj</small></th>
                        <th class="field-header" style="min-width: 100px;">修改时间<br><small>xgsj</small></th>
                        <th class="field-header" style="min-width: 60px;">意见书数量<br><small>counts</small></th>
                        <th class="field-header" style="min-width: 120px;">信息主键编号<br><small>xxzjbh</small></th>
                        <th class="field-header" style="min-width: 80px;">案件类型<br><small>ajlx</small></th>
                        <th class="field-header" style="min-width: 80px;">文书地址<br><small>flwsxzdz</small></th>
                        <th class="field-header" style="min-width: 100px;">同步时间<br><small>tbrksj</small></th>
                        <th class="field-header" style="min-width: 100px;">案件内容<br><small>ajnr</small></th>
                        <th class="field-header" style="min-width: 80px;">关系图代码<br><small>code</small></th>
                        <th class="field-header" style="min-width: 80px;">最终代码<br><small>lastcode</small></th>
                        <th class="field-header" style="min-width: 80px;">修改人员<br><small>updater</small></th>
                        <th class="field-header" style="min-width: 80px;">修改类型<br><small>updatetype</small></th>
                        <th class="field-header" style="min-width: 80px;">状态<br><small>status</small></th>
                        <th class="field-header" style="min-width: 100px;">开始时间<br><small>starttime</small></th>
                        <th class="field-header" style="min-width: 100px;">完成时间<br><small>endtime</small></th>
                        <th class="field-header" style="min-width: 100px;">更新时间<br><small>updatetime</small></th>
                        <th class="field-header" style="min-width: 60px;">重跑次数<br><small>nums</small></th>
                        <th class="field-header" style="min-width: 80px;">错误信息<br><small>error</small></th>
                        <th class="field-header" style="min-width: 60px;">删除状态<br><small>isdelete</small></th>
                        <th class="field-header" style="min-width: 120px;">操作<br><small>actions</small></th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases %}
                    <tr>
                        <!-- 1. 批次号 -->
                        <td title="{{ case.batchid }}">
                            <div class="text-truncate" style="max-width: 80px;">
                                {{ case.batchid or 'N/A' }}
                            </div>
                        </td>
                        <!-- 2. 案件编号 -->
                        <td title="{{ case.ajbh }}">
                            <strong>{{ case.ajbh }}</strong>
                            <br><a href="{{ url_for('case_detail', ajbh=case.ajbh) }}" class="text-decoration-none">
                                <i class="bi bi-eye"></i>
                            </a>
                        </td>
                        <!-- 3. 案件名称 -->
                        <td title="{{ case.ajmc }}">
                            <div class="text-truncate" style="max-width: 120px;">
                                {{ case.ajmc or 'N/A' }}
                            </div>
                        </td>
                        <!-- 4. 填发时间 -->
                        <td title="{{ case.tfsj_formatted }}">
                            <div class="text-truncate" style="max-width: 100px;">
                                {{ case.tfsj_formatted or 'N/A' }}
                            </div>
                        </td>
                        <!-- 5. 修改时间 -->
                        <td title="{{ case.xgsj_formatted }}">
                            <div class="text-truncate" style="max-width: 100px;">
                                {{ case.xgsj_formatted or 'N/A' }}
                            </div>
                        </td>
                        <!-- 6. 意见书数量 -->
                        <td title="{{ case.counts }}">
                            <span class="badge bg-secondary">{{ case.counts or 0 }}</span>
                        </td>
                        <!-- 7. 信息主键编号 -->
                        <td title="{{ case.xxzjbh_display }}">
                            <div class="text-truncate" style="max-width: 120px;">
                                {{ case.xxzjbh_display or 'N/A' }}
                            </div>
                        </td>
                        <!-- 8. 案件类型 -->
                        <td title="{{ case.ajlx }}">
                            <span class="badge bg-info">{{ case.ajlx or 'N/A' }}</span>
                        </td>
                        <!-- 9. 文书地址 -->
                        <td title="{{ case.flwsxzdz }}">
                            {% if case.flwsxzdz %}
                            <a href="{{ case.flwsxzdz }}" target="_blank" class="text-decoration-none">
                                <i class="bi bi-link-45deg"></i>
                            </a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <!-- 10. 同步时间 -->
                        <td title="{{ case.tbrksj_formatted }}">
                            <div class="text-truncate" style="max-width: 100px;">
                                {{ case.tbrksj_formatted }}
                            </div>
                        </td>
                        <!-- 7. 文书地址 -->
                        <td title="{{ case.flwsxzdz }}">
                            {% if case.flwsxzdz %}
                            <a href="{{ case.flwsxzdz }}" target="_blank" class="text-decoration-none">
                                <i class="bi bi-link-45deg"></i>
                            </a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <!-- 8. 案件内容 -->
                        <td title="{{ case.ajnr }}">
                            {% if case.ajnr_short != 'N/A' %}
                            <div class="text-truncate" style="max-width: 100px;">
                                {{ case.ajnr_short }}
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <!-- 9. 关系图代码 -->
                        <td title="{{ case.code }}">
                            {% if case.has_original_code %}
                            <div class="text-truncate" style="max-width: 80px;">
                                <span class="text-info">
                                    <i class="bi bi-code-slash"></i>
                                    {{ case.code[:20] }}...
                                </span>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <!-- 10. 最终代码 -->
                        <td title="{{ case.lastcode }}">
                            {% if case.has_mermaid %}
                            <div class="text-truncate" style="max-width: 80px;">
                                <span class="text-success">
                                    <i class="bi bi-diagram-3"></i>
                                    {{ case.lastcode[:20] }}...
                                </span>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <!-- 11. 修改人员 -->
                        <td title="{{ case.updater }}">
                            <div class="text-truncate" style="max-width: 80px;">
                                {{ case.updater or '-' }}
                            </div>
                        </td>
                        <!-- 12. 修改类型 -->
                        <td title="{{ case.updatetype_text }}">
                            <span class="badge {% if case.updatetype == '1' %}bg-success{% else %}bg-secondary{% endif %}">
                                {{ case.updatetype_text[:2] }}
                            </span>
                        </td>
                        <!-- 13. 状态 -->
                        <td title="{{ case.status_text }}">
                            <span class="badge {{ case.status_class }}">
                                {{ case.status_text[:4] }}
                            </span>
                        </td>
                        <!-- 14. 开始时间 -->
                        <td title="{{ case.starttime_formatted }}">
                            <div class="text-truncate" style="max-width: 100px;">
                                {{ case.starttime_formatted }}
                            </div>
                        </td>
                        <!-- 15. 完成时间 -->
                        <td title="{{ case.endtime_formatted }}">
                            <div class="text-truncate" style="max-width: 100px;">
                                {{ case.endtime_formatted }}
                            </div>
                        </td>
                        <!-- 16. 更新时间 -->
                        <td title="{{ case.updatetime_formatted }}">
                            <div class="text-truncate" style="max-width: 100px;">
                                {{ case.updatetime_formatted }}
                            </div>
                        </td>
                        <!-- 17. 重跑次数 -->
                        <td title="AI重跑次数: {{ case.nums or 0 }}">
                            {% if case.nums %}
                            <span class="badge bg-warning">{{ case.nums }}</span>
                            {% else %}
                            <span class="text-muted">0</span>
                            {% endif %}
                        </td>
                        <!-- 18. 错误信息 -->
                        <td title="{{ case.error }}">
                            {% if case.error %}
                            <i class="bi bi-exclamation-triangle text-danger"></i>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <!-- 19. 操作 -->
                        <td>
                            <div class="btn-group" role="group">
                                {% if case.has_mermaid %}
                                <a href="{{ url_for('view_mermaid', ajbh=case.ajbh) }}"
                                   class="btn btn-sm btn-primary" target="_blank"
                                   title="关系图">
                                    <i class="bi bi-diagram-3"></i>
                                </a>
                                <a href="{{ url_for('mermaid_view', ajbh=case.ajbh) }}"
                                   class="btn btn-sm btn-outline-primary" target="_blank"
                                   title="预览">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% else %}
                                <button class="btn btn-sm btn-secondary" disabled title="暂无关系图">
                                    <i class="bi bi-diagram-3"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if total_pages > 1 %}
        <nav aria-label="案件列表分页">
            <ul class="pagination justify-content-center">
                <!-- 上一页 -->
                {% if page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('index', page=page-1, per_page=per_page, status=status_filter, search=search_term) }}">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
                {% endif %}

                <!-- 页码 -->
                {% set start_page = [1, page - 2]|max %}
                {% set end_page = [total_pages, page + 2]|min %}

                {% if start_page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('index', page=1, per_page=per_page, status=status_filter, search=search_term) }}">1</a>
                </li>
                {% if start_page > 2 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
                {% endif %}

                {% for p in range(start_page, end_page + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('index', page=p, per_page=per_page, status=status_filter, search=search_term) }}">{{ p }}</a>
                </li>
                {% endfor %}

                {% if end_page < total_pages %}
                {% if end_page < total_pages - 1 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('index', page=total_pages, per_page=per_page, status=status_filter, search=search_term) }}">{{ total_pages }}</a>
                </li>
                {% endif %}

                <!-- 下一页 -->
                {% if page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('index', page=page+1, per_page=per_page, status=status_filter, search=search_term) }}">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="text-muted mt-3">暂无数据</h4>
            <p class="text-muted">没有找到符合条件的案件记录</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 启用工具提示
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
})

// 自动提交搜索表单
document.getElementById('search').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
{% endblock %}
