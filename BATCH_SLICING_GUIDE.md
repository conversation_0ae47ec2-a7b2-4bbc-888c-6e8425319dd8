# 分片批处理系统使用指南

## 概述

分片批处理系统是为了解决大批量案件（600+）OCR超时问题而设计的解决方案。通过将大批次分割成小的片批，每个片批独立处理，避免了单次处理时间过长导致的超时问题。

## 系统架构

### 原始流程
```
数据获取智能体 → PDF下载智能体 → PDF合并智能体 → OCR识别智能体 → 案件要素提取智能体
```

### 新的分片批流程
```
数据获取智能体 → PDF下载智能体 → PDF合并智能体 → 
├─ OCR识别智能体（片批1） → 案件要素提取智能体（片批1）
├─ OCR识别智能体（片批2） → 案件要素提取智能体（片批2）
└─ OCR识别智能体（片批3） → 案件要素提取智能体（片批3）
```

## 核心组件

### 1. 配置文件 (config.py)
新增配置项：
- `batch_slice_size`: 分片批大小，默认10个PDF为一个片批

### 2. PDF分片批分配器 (pdf_batch_slicer.py)
负责：
- 将PDF文件分配到各个片批目录
- 创建分片批目录结构
- 管理片批信息

### 3. OCR处理器 (ocr_processor.py)
增强功能：
- 支持分片批模式的目录结构
- 支持按片批更新数据库状态
- 独立处理每个片批

### 4. 主控制器 (main_controller.py)
新增方法：
- `process_batch_with_slicing()`: 分片批处理主流程
- 实现增量处理：片批OCR完成后立即进行要素提取

## 目录结构

### 原始结构
```
/bigai/ai/AJagent-main/data/
└── {batch_id}/
    ├── input/          # PDF输入目录
    └── output/         # OCR输出目录
```

### 分片批结构
```
/bigai/ai/AJagent-main/data/
└── {batch_id}/
    ├── input/          # 原始输入目录（分配后清空）
    ├── 1/              # 片批1
    │   ├── input/      # 片批1 PDF输入
    │   └── output/     # 片批1 OCR输出
    ├── 2/              # 片批2
    │   ├── input/      # 片批2 PDF输入
    │   └── output/     # 片批2 OCR输出
    └── 3/              # 片批3
        ├── input/      # 片批3 PDF输入
        └── output/     # 片批3 OCR输出
```

## 处理流程详解

### 1. PDF分片批分配
1. 从数据库获取状态为1（PDF合并完成）的案件
2. 按配置的分片批大小分组
3. 为每个分片批创建目录结构
4. 将PDF文件移动到对应的片批目录

### 2. OCR分片批处理
1. 按顺序处理每个片批（片批1 → 片批2 → 片批3）
2. 每个片批调用独立的Docker OCR命令
3. 处理完成后立即更新数据库状态
4. 成功案件状态更新为2，失败案件状态更新为4

### 3. 增量要素提取
1. 每个片批OCR完成后，立即对成功案件进行要素提取
2. 不需要等待所有片批OCR完成
3. 实现真正的流水线处理

## 数据库状态管理

### 状态码说明
- `0`: 初始状态
- `1`: PDF合并完成
- `2`: OCR识别完成
- `3`: 要素提取完成
- `4`: 处理失败

### 分片批状态更新
```sql
-- 更新成功案件
UPDATE ds_case_relation 
SET status = '2', updatetime = NOW()
WHERE batchid = ? AND ajbh IN (success_ajbh_list)

-- 更新失败案件
UPDATE ds_case_relation 
SET status = '4', updatetime = NOW()
WHERE batchid = ? AND ajbh IN (error_ajbh_list)
```

## 使用方法

### 1. 配置调整
在 `config.py` 中调整分片批大小：
```python
'batch_slice_size': 10,  # 根据系统性能调整
```

### 2. 运行验证
```bash
# 验证配置
python verify_batch_slicing_config.py

# 功能测试
python test_batch_slicing.py
```

### 3. 实际使用
```python
# 使用新的分片批处理
controller = MainController()
result = await controller.process_batch_with_slicing(batch_id, cases)
```

## 性能优化建议

### 1. 分片批大小调整
- 小批量（10个）：适合稳定性优先
- 中批量（20-30个）：平衡性能和稳定性
- 大批量（50+个）：性能优先，但风险较高

### 2. 并发控制
- 分片批内要素提取并发数：5（可调整）
- 避免同时处理多个分片批的OCR

### 3. 错误恢复
- 单个片批失败不影响其他片批
- 支持重新处理失败的片批
- 详细的错误日志记录

## 故障排除

### 1. 常见问题
- **分片批目录创建失败**: 检查OCR基础路径权限
- **PDF文件移动失败**: 检查磁盘空间和文件权限
- **Docker命令失败**: 检查Docker容器状态

### 2. 日志查看
```bash
# 查看主控制器日志
tail -f logs/main_controller_*.log

# 查看OCR处理日志
grep "分片批" logs/main_controller_*.log
```

### 3. 手动恢复
如果需要手动恢复某个片批：
```python
# 重新处理特定片批
ocr_processor = OCRProcessor()
result = ocr_processor.process_batch_slice(batch_id, ppid, ajbh_list)
```

## 监控和维护

### 1. 性能监控
- 监控每个片批的处理时间
- 跟踪OCR成功率
- 观察系统资源使用情况

### 2. 定期维护
- 清理旧的OCR输出目录
- 检查磁盘空间使用
- 更新Docker镜像

## 版本兼容性

- 新系统完全向后兼容原始处理模式
- 可以通过配置选择使用分片批处理或原始处理
- 数据库结构无需修改

## 测试建议

1. **小规模测试**: 先用10-20个案件测试
2. **中规模测试**: 50-100个案件验证稳定性
3. **大规模测试**: 300-600个案件验证性能
4. **压力测试**: 1000+个案件测试极限情况

---

**注意**: 在生产环境使用前，请务必进行充分测试，确保系统稳定性和数据完整性。
