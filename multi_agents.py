################multi_agents.py##########
import asyncio
import logging
import json
import uuid
import re
import base64
import io
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional
import pandas as pd
import pymysql
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken

import docker
import os
import tempfile
import time

# 导入原有的基础类
from agents import (
    DatabaseManager, ModelManager,
    RelationshipVisualizationAgent, ReportGeneratorAgent,
    generate_session_id
)

# 设置matplotlib中文字体
def setup_matplotlib_font():
    """设置matplotlib中文字体"""
    try:
        system = platform.system()
        if system == "Windows":
            font_names = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
        elif system == "Darwin":  # macOS
            font_names = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
        else:  # Linux
            font_names = ['WenQuanYi Micro Hei', 'SimSun', 'DejaVu Sans']

        available_fonts = [f.name for f in fm.fontManager.ttflist]

        for font_name in font_names:
            if font_name in available_fonts:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                break
        else:
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

    except Exception as e:
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

# 初始化字体设置
setup_matplotlib_font()

class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理不可序列化的对象"""
    def default(self, obj):
        if isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, pd.Series):
            return obj.to_dict()
        elif hasattr(obj, 'isoformat'):  # datetime对象
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):  # 其他对象
            return obj.__dict__
        return super().default(obj)

class SessionManager:
    """会话管理器 - 支持复杂对象序列化"""

    def __init__(self, base_dir: str = "sessions"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

    def create_session_dir(self, session_id: str) -> Path:
        """创建会话目录"""
        session_dir = self.base_dir / session_id
        session_dir.mkdir(exist_ok=True)

        # 创建子目录
        (session_dir / "uploads").mkdir(exist_ok=True)
        (session_dir / "outputs").mkdir(exist_ok=True)
        (session_dir / "reports").mkdir(exist_ok=True)
        (session_dir / "data").mkdir(exist_ok=True)

        return session_dir

    def get_session_dir(self, session_id: str) -> Path:
        """获取会话目录"""
        session_dir = self.base_dir / session_id
        if not session_dir.exists():
            return self.create_session_dir(session_id)
        return session_dir

    def save_uploaded_file(self, session_id: str, file_content: bytes, original_filename: str) -> str:
        """保存上传的文件，保持原始文件名"""
        session_dir = self.get_session_dir(session_id)
        file_path = session_dir / "uploads" / original_filename

        with open(file_path, 'wb') as f:
            f.write(file_content)

        return str(file_path)

    def save_user_analysis(self, session_id: str, analysis_content: str, filename: str) -> str:
        """保存用户输入的案件分析内容到uploads文件夹"""
        session_dir = self.get_session_dir(session_id)
        file_path = session_dir / "uploads" / filename

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(analysis_content)

        return str(file_path)

    def save_user_config(self, session_id: str, config_data: dict, filename: str) -> str:
        """保存用户配置信息到uploads文件夹"""
        session_dir = self.get_session_dir(session_id)
        file_path = session_dir / "uploads" / filename

        import json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        return str(file_path)

    def save_session_data(self, session_id: str, data: Dict[str, Any]):
        """保存会话数据 - 使用自定义JSON编码器"""
        session_dir = self.get_session_dir(session_id)
        data_file = session_dir / "data" / "session_data.json"

        try:
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
        except Exception as e:
            logging.error(f"保存会话数据失败: {e}")
            # 尝试保存简化版本
            simplified_data = self._simplify_data(data)
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(simplified_data, f, ensure_ascii=False, indent=2)

    def _simplify_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """简化数据结构，移除不可序列化的对象"""
        simplified = {}
        for key, value in data.items():
            try:
                # 尝试序列化测试
                json.dumps(value, cls=CustomJSONEncoder)
                simplified[key] = value
            except (TypeError, ValueError):
                # 如果无法序列化，保存类型信息
                simplified[key] = f"<{type(value).__name__}>"
        return simplified

    def load_session_data(self, session_id: str) -> Dict[str, Any]:
        """加载会话数据"""
        session_dir = self.get_session_dir(session_id)
        data_file = session_dir / "data" / "session_data.json"

        if data_file.exists():
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"加载会话数据失败: {e}")
                return {}
        return {}

class ConversationManager:
    """对话管理器 - 支持复杂对象序列化"""

    def __init__(self, session_manager: SessionManager):
        self.conversations = {}
        self.session_manager = session_manager

    def get_conversation(self, session_id: str) -> Dict[str, Any]:
        if session_id not in self.conversations:
            # 尝试从文件加载
            saved_data = self.session_manager.load_session_data(session_id)
            self.conversations[session_id] = saved_data if saved_data else {
                "messages": [],
                "case_data": None,
                "files": [],
                "context": {}
            }
        return self.conversations[session_id]

    def add_message(self, session_id: str, message_type: str, content: str, metadata: Dict[str, Any] = None):
        conversation = self.get_conversation(session_id)
        message = {
            "type": message_type,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }
        conversation["messages"].append(message)

        # 保存到文件
        self.session_manager.save_session_data(session_id, conversation)

    def set_case_data(self, session_id: str, case_data: Any):
        conversation = self.get_conversation(session_id)
        conversation["case_data"] = case_data

        # 保存到文件
        self.session_manager.save_session_data(session_id, conversation)



class DatabaseManager:
    """数据库连接管理器"""

    def __init__(self):
        self.config = {
            'host': '*************',
            'user': 'root',
            'password': '123456',
            'database': 'mydb',
            'charset': 'utf8mb4'
        }

        # 设置logging级别，减少不必要的日志
        logging.getLogger('pymysql').setLevel(logging.WARNING)

    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.config)
            return connection
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            raise

    def execute_query(self, sql: str) -> Dict[str, Any]:
        """执行SQL查询"""
        try:
            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql)
                    if sql.strip().upper().startswith('INSERT'):
                        connection.commit()
                        return {
                            "status": "success",
                            "message": f"成功插入 {cursor.rowcount} 条记录",
                            "affected_rows": cursor.rowcount,
                            "sql": sql
                        }
                    else:
                        results = cursor.fetchall()
                        return {
                            "status": "success",
                            "data": results,
                            "sql": sql,
                            "count": len(results)
                        }
        except Exception as e:
            logging.error(f"SQL执行失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "sql": sql,
                "data": None
            }

class ModelManager:
    """大模型管理器"""

    def __init__(self):
        # 设置OpenAI客户端日志级别
        logging.getLogger('openai._base_client').setLevel(logging.WARNING)

        self.model_client = OpenAIChatCompletionClient(
            model="Qwen3-32B",
            base_url="http://192.168.2.240:8011/v1",
            #model="Qwen3-235B-A22B-AWQ",
            #base_url="http://10.10.20.42:8012/v1",
            #model="Qwen2.5-Coder-32B",
            #base_url="http://192.168.2.240:8012/v1",
            api_key="jc888",
            temperature=0.2,
            model_info=ModelInfo(
                family="openai",
                vision=True,
                function_calling=True,
                json_output=True
            )
        )

    def get_client(self):
        return self.model_client

class ExcelFileProcessor:
    """Excel/CSV文件处理器 - 支持多案件文件处理"""

    @staticmethod
    def read_excel_file(file_path: str) -> tuple[pd.DataFrame, Dict[str, Any]]:
        """读取Excel/CSV文件并返回DataFrame和文件信息"""
        file_path = Path(file_path)
        file_info = {
            "original_name": file_path.name,
            "file_size": file_path.stat().st_size,
            "file_type": file_path.suffix.lower().lstrip('.'),
            "modified_time": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
        }

        try:
            # 根据文件类型读取数据
            if file_info["file_type"] in ['xlsx', 'xls']:
                df = pd.read_excel(file_path)
            elif file_info["file_type"] == 'csv':
                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                if df is None:
                    raise ValueError("无法读取CSV文件，编码格式不支持")
            else:
                raise ValueError(f"不支持的文件类型: {file_info['file_type']}")

            # 添加数据统计信息
            file_info["total_rows"] = len(df)
            file_info["total_columns"] = len(df.columns)
            file_info["columns"] = df.columns.tolist()

            return df, file_info

        except Exception as e:
            logging.error(f"文件读取失败: {e}")
            raise

    @staticmethod
    def preprocess_case_data(df: pd.DataFrame) -> Dict[str, Any]:
        """预处理案件数据：去重、合并等"""
        try:
            original_count = len(df)

            # 1. 数据去重：每个字段的数据都相同的情况下，取其中一行
            df_deduplicated = df.drop_duplicates()
            deduplicated_count = len(df_deduplicated)

            # 2. 按案件编号分组处理
            if '案件编号' not in df_deduplicated.columns:
                raise ValueError("数据中缺少'案件编号'字段")

            # 需要合并的内容字段
            content_fields = ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明']
            # 需要取最大版本号的字段
            version_fields = ['案件名称', '承办单位']

            processed_cases = []

            for case_id, group in df_deduplicated.groupby('案件编号'):
                if len(group) == 1:
                    # 只有一条记录，直接使用
                    case_data = group.iloc[0].to_dict()
                else:
                    # 多条记录需要合并
                    # 获取数据版本号最大的记录作为基础
                    if '数据版本号' in group.columns:
                        base_record = group.loc[group['数据版本号'].idxmax()]
                    else:
                        base_record = group.iloc[0]

                    case_data = base_record.to_dict()

                    # 合并内容字段，添加字段标题
                    merged_content_parts = []
                    field_titles = {
                        '正文内容': '一.正文内容',
                        '到案情况': '二.到案情况',
                        '依法侦查查明': '三.依法侦查查明',
                        '犯罪证据': '四.犯罪证据',
                        '综上所述': '五.综上所述',
                        '其他说明': '六.其他说明'
                    }

                    for field in content_fields:
                        if field in group.columns:
                            field_contents = group[field].dropna().astype(str)
                            field_contents = field_contents[field_contents != 'nan']
                            if len(field_contents) > 0:
                                # 添加字段标题
                                field_title = field_titles.get(field, field)
                                field_content = '\n'.join(field_contents.tolist())
                                merged_content_parts.append(f"{field_title}\n{field_content}")

                    case_data['案件内容'] = '\n\n'.join(merged_content_parts) if merged_content_parts else ''

                # 确保必要字段存在
                if '案件内容' not in case_data:
                    # 如果没有合并的案件内容，尝试从现有字段构建
                    content_parts = []
                    field_titles = {
                        '正文内容': '一.正文内容',
                        '到案情况': '二.到案情况',
                        '依法侦查查明': '三.依法侦查查明',
                        '犯罪证据': '四.犯罪证据',
                        '综上所述': '五.综上所述',
                        '其他说明': '六.其他说明'
                    }

                    for field in content_fields:
                        if field in case_data and pd.notna(case_data[field]):
                            field_title = field_titles.get(field, field)
                            field_content = str(case_data[field])
                            content_parts.append(f"{field_title}\n{field_content}")
                    case_data['案件内容'] = '\n\n'.join(content_parts)

                processed_cases.append(case_data)

            # 转换为DataFrame
            processed_df = pd.DataFrame(processed_cases)

            # 确保必要的字段存在
            required_fields = ['案件编号', '案件名称', '承办单位', '案件内容', '录入时间']
            for field in required_fields:
                if field not in processed_df.columns:
                    if field == '录入时间':
                        processed_df[field] = ''  # 录入时间如果不存在则为空
                    else:
                        processed_df[field] = ''

            return {
                "status": "success",
                "processed_data": processed_df.to_dict('records'),  # 转换为可序列化的格式
                "original_count": original_count,
                "deduplicated_count": deduplicated_count,
                "final_count": len(processed_df),
                "processing_summary": f"原始数据{original_count}行 -> 去重后{deduplicated_count}行 -> 合并后{len(processed_df)}个案件"
            }

        except Exception as e:
            logging.error(f"数据预处理失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

class BatchCaseExtractionAgent:
    """批量案件要素提取智能体 - 支持并发处理多个案件"""

    def __init__(self, model_manager: ModelManager, session_manager: SessionManager, max_concurrent: int = 10, max_repair_attempts: int = 2):
        self.model_client = model_manager.get_client()
        self.session_manager = session_manager
        self.max_concurrent = max_concurrent
        self.max_repair_attempts = max_repair_attempts  # 案件提取修复次数上限
        self.agent = None  # 延迟初始化，等待用户需求
        self.repair_agent = CaseExtractionRepairAgent(model_manager, session_manager)  # 修复智能体

        # 全局用户需求变量
        self.user_requirements = None
        self.user_requirements_new = None
        self.user_requirements_count = 0
        self.user_relationship_images = None
        self.user_analysis = None

    def _set_user_requirements(self, user_requirements: str = None, user_relationship_images: str = None, user_analysis: str = None):
        """设置用户需求并处理相关变量"""
        self.user_requirements = user_requirements

        # 处理用户需求字段
        if user_requirements:
            # 处理中文逗号替换为英文逗号
            self.user_requirements_new = user_requirements.replace("，", ",")
        else:
            # 默认字段
            #self.user_requirements_new = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）"
            self.user_requirements_new = "实体类型,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,角色分工,横向关联人物,横向关联关系,纵向关联人物,纵向关联关系,关联工具,关联物品,关联行为,关联场所,强制措施,司法处置结果,经济收益,前科"
            
        # 统计字段数量
        self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])

        # 处理关系图需求
        if user_relationship_images:
            self.user_relationship_images = user_relationship_images
        else:
            # 默认关系图需求
            self.user_relationship_images = """
【图谱设计要求】
1. 布局原则：
   - 采用作案链条层级结构
   - 同级人员横向排列

2. 节点信息规范：
   - 人员节点：姓名-角色-年龄-强制措施（-前科，如有）
   - 公司节点：公司名称-性质
   - 地点节点：具体地点-功能（窝点/交货点/仓库等）
   - 货物节点：货物名称-数量-金额

3. 连线规范：
   - 有明确关系描述的在连线上标注关系类型
   - 箭头方向：箭头方向必须准确反映关系链条流转
   - 避免重复连线，保持图谱清晰
   - 无明确关系的实体不强行连接
   
4. 布局原则：
   - 避免线条交叉重叠，保持图谱清晰
   - 相同层级横向排列
   - 箭头方向体现关系流向,无明确流行则用无箭头关系线条
   
5. Mermaid语法规范：
   - 使用flowchart TB
   - 节点内换行示例：A["张某某-组织者<br/>35岁-逮捕"]
   - 关系标注示例：A -->|雇佣| B
"""

        # 处理案件分析需求
        if user_analysis and user_analysis.strip():
            self.user_analysis = user_analysis.strip()
        else:
            self.user_analysis = None

    def _initialize_agent(self, user_requirements: str = None, user_relationship_images: str = None, user_analysis: str = None):
        """初始化或重新初始化智能体"""
        try:
            # 设置用户需求
            self._set_user_requirements(user_requirements, user_relationship_images, user_analysis)

            # 生成系统消息
            system_message = self._get_system_message()
            self.agent = AssistantAgent(
                name="batch_case_extractor",
                model_client=self.model_client,
                system_message=system_message
            )
        except Exception as e:
            print(f"Error initializing agent: {e}")
            # 使用默认系统消息作为后备
            self._set_user_requirements(None)
            default_system_message = self._get_system_message()
            self.agent = AssistantAgent(
                name="batch_case_extractor",
                model_client=self.model_client,
                system_message=default_system_message
            )

    def _get_system_message(self) -> str:
        """生成系统消息，使用类的全局变量"""

        # 动态生成分析需求
        analysis_requirements = f"""
【核心任务】
1. 深度解析犯罪网络：精准分析人员、公司、组织的层级关系、角色分工和犯罪链条
2. 结构化要素提取：严格按照{self.user_requirements_count}列CSV格式提取关键信息
3. 生成高准确性关系图谱：使用Mermaid语法展现所有层级和关联关系

【执行流程】

第一步：案件深度分析
**重点分析区域**：优先分析"二.到案情况"和"三.依法侦查查明"部分

对案件内容进行系统性分析：
1. 犯罪结构与核心要素：逐一识别所有涉案人员（真实姓名、代号、昵称）
2. 时间线与关键事件：按时间顺序构建犯罪行为发展脉络
3. 涉案金额统计：准确记录各环节涉案金额和获利情况
4. 角色分工与组织架构：明确各参与人员的具体职能和指挥关系
5. 证据链与法律依据：梳理关键证据和证明材料

第二步：结构化要素提取

【提取原则（严格执行）】
- 极高严谨性：严格基于案件原文，绝不推测、绝不添加未明确提及的信息
- 极高准确性：完全保持原文用词，确保描述的绝对准确性
- 信息完整性：空缺信息必须留空，严禁填写"未知"、"不详"、"/"等任何占位符
- 关系严谨性：只记录案件中明确描述的关系，不得推测或补充
- 格式规范性：多个值用分号(;)分隔，确保CSV格式兼容性
- 实体限定性：严格限定为人员和公司实体，不包括场所、物品等

【{self.user_requirements_count}列数据字段规范】
1. 实体类型：仅填写"人员"或"公司"
2. 姓名/代号/昵称/公司：完整记录真实姓名、代号、昵称（如：xxapp+昵称）
3. 性别：男/女
4. 年龄：优先原文，无则用"案件年份-身份证出生年份"计算
5. 身份证号：完整18位号码
6. 户籍地/现居地：具体到市/县级
7. 文化程度：小学/初中/高中/大专/本科等
8. 直接上级：明确的上级人员姓名或组织者
9. 所属公司：正式注册公司名称
10. 所属组织：犯罪组织名称
11. 角色：在案件中的角色，按案件内容中的描述用词
12. 主要职责：涉案人员主要职责，按案件内容中的描述用词
13. 同级关联人物：同级别相关人员
14. 同级关联关系：同级别相关人员具体关系
15. 纵向关联人物：上下级相关人员
16. 纵向关联关系：案件犯罪链条上下级，按案件内容中的描述用词
17. 关联工具：车辆/设备/通讯工具/打码机等
18. 关联物品：涉案货物种类及数量/金额
19. 关联犯罪行为：具体犯罪行为描述，例：指使、雇佣、主导、控制、介绍、运输、组织、搬运等，用原文用词描述
20. 关联场所：窝点/仓库/交货点/办公地点等
21. 强制措施或状态：刑事拘留/逮捕/拘留/取保候审/另案处理/另案起诉/在逃等
22. 司法处置结果：判决结果（如有）
23. 经济收益：获利金额或涉案金额
24. 前科：格式为"年份+罪名+刑期"，无前科则留空

【CSV输出格式】({self.user_requirements_count}列)
{self.user_requirements_new}

第三步：高准确性关系图谱生成
{self.user_relationship_images}

【关系识别与验证流程】
步骤1：实体识别验证 - 逐句扫描案件内容，识别所有人员实体（姓名/代号/昵称）和公司/组织实体
步骤2：关系提取验证 - 只提取案件中明确描述的关系，每个关系都必须能在原文中找到具体描述
步骤3：层级结构验证 - 只有案件明确描述的上下级关系才能体现在图谱中

【Mermaid语法增强规范】
- 图类型：必须使用 graph TD
- 节点格式：使用双引号包围，如"李某-窝点负责人<br/>39岁-已逮捕"
- 严禁括号：节点内容中不得使用()或（）
- 关系标注：使用原文用词，如 A --|雇佣| B
- 连线方向：明确指向用-->，双向用<-->，无方向用---
- 节点颜色分类：
  classDef 在逃累犯 fill:#ff4d4d,stroke:#333,stroke-width:2px
  classDef 普通人员 fill:#87cefa,stroke:#333,stroke-width:2px
  classDef 公司 fill:#ffff99,stroke:#333,stroke-width:2px
  classDef 工具 fill:#90ee90,stroke:#333,stroke-width:2px
  classDef 场所 fill:#d3d3d3,stroke:#333,stroke-width:2px
  classDef 物品 fill:#dda0dd,stroke:#333,stroke-width:2px

【质量控制检查清单】
准确性检查：
- 每个节点都能在案件原文中找到对应描述
- 每个关系都有明确的原文支持
- 所有用词都严格使用原文表述
- 没有添加任何推测性信息

完整性检查：
- 所有案件中提及的人员都已包含
- 所有相关公司/组织都已包含
- 重要的关系都已标注
- 没有遗漏关键节点

可读性检查：
- 布局清晰，层次分明
- 连线不重复，不交叉
- 节点信息完整准确
- 颜色分类正确应用

语法检查：
- Mermaid语法正确
- 节点定义规范
- 关系标注格式正确
- 颜色定义完整

"""
        # 使用用户提供的需求，如果没有则使用默认需求
        #analysis_requirements = user_requirements.strip() if user_requirements else default_requirements

        # 根据是否有用户自定义分析需求来生成不同的系统消息
        if self.user_analysis:
            # 使用用户自定义的案件分析需求
            return f"""{self.user_analysis}

{analysis_requirements}

返回格式：
{{
    "analysis": "详细的犯罪网络分析，包括：1)基于原文的组织层级结构分析；2)人员角色分工详述（使用原文用词）；3)犯罪链条梳理（基于明确证据）；4)关键节点和关系验证分析；5)时间线和证据链分析",
    "csv_data": "严格按照{self.user_requirements_count}列格式的CSV数据，每个实体占一行，所有信息基于原文，空缺信息留空",
    "mermaid_code": "完整的Mermaid关系图代码，每个节点和关系都有原文支持，语法正确、可直接渲染"
}}
"""
        else:
            # 使用默认的系统消息
            return f"""你是一位专业的走私刑事案件分析专家，专门负责分析走私刑事案件起诉意见书，提取关键要素并生成可视化关系图谱。

{analysis_requirements}

返回格式：
{{
    "analysis": "详细的犯罪网络分析，包括：1)基于原文的组织层级结构分析；2)人员角色分工详述（使用原文用词）；3)犯罪链条梳理（基于明确证据）；4)关键节点和关系验证分析；5)时间线和证据链分析",
    "csv_data": "严格按照{self.user_requirements_count}列格式的CSV数据，每个实体占一行，所有信息基于原文，空缺信息留空",
    "mermaid_code": "完整的Mermaid关系图代码，每个节点和关系都有原文支持，语法正确、可直接渲染"
}}
"""

    async def extract_multiple_cases(self, cases_data: List[Dict[str, Any]], batch_id: str, session_id: str,
                                   user_requirements: str = None, user_relationship_images: str = None, user_analysis: str = None, progress_callback=None) -> Dict[str, Any]:
        """并发提取多个案件的信息"""
        try:
            # 初始化智能体（如果还没有初始化或需求发生变化）
            if self.agent is None or self.user_requirements != user_requirements or self.user_relationship_images != user_relationship_images or self.user_analysis != user_analysis:
                try:
                    self._initialize_agent(user_requirements, user_relationship_images, user_analysis)
                except Exception as e:
                    print(f"Error initializing agent in extract_multiple_cases: {e}")
                    # 使用默认需求重新尝试
                    self._initialize_agent(None, None, None)

            total_cases = len(cases_data)
            if total_cases == 0:
                return {
                    "status": "error",
                    "message": "没有案件数据需要处理"
                }

            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(self.max_concurrent)

            # 记录案件编号映射，用于验证
            case_id_mapping = {}
            logging.info("=" * 60)
            logging.info("开始批量案件处理 - 案件映射关系:")
            for i, case_data in enumerate(cases_data):
                case_id = case_data.get('案件编号', f'案件{i+1}')
                case_name = case_data.get('案件名称', '未知案件')
                case_id_mapping[i] = case_id
                logging.info(f"  任务索引 {i}: {case_id} - {case_name}")
            logging.info("=" * 60)

            # 创建任务列表，确保每个任务都包含完整的案件信息
            tasks = []
            for i, case_data in enumerate(cases_data):
                # 创建案件数据的深拷贝，避免并发修改
                case_data_copy = case_data.copy()
                case_data_copy['_task_index'] = i  # 添加任务索引标识

                task = self._extract_single_case_with_semaphore(
                    semaphore, case_data_copy, batch_id, session_id, i, progress_callback
                )
                tasks.append(task)

            # 并发执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果，使用任务索引确保正确匹配
            successful_results = []
            failed_results = []

            for result in results:
                if isinstance(result, Exception):
                    # 异常情况，尝试从异常信息中提取案件信息
                    failed_results.append({
                        "case_index": -1,
                        "case_id": "未知案件",
                        "error": str(result)
                    })
                elif result.get("status") == "success":
                    # 成功结果，验证案件编号匹配
                    task_index = result.get("_task_index", -1)
                    result_case_id = result.get("case_id", "")

                    # 验证案件编号是否匹配
                    if task_index >= 0 and task_index < len(cases_data):
                        expected_case_id = cases_data[task_index].get('案件编号', '')
                        if result_case_id != expected_case_id:
                            # 案件编号不匹配，记录警告
                            logging.warning(f"案件编号不匹配: 期望 {expected_case_id}, 实际 {result_case_id}")

                    successful_results.append(result)
                else:
                    # 失败结果
                    task_index = result.get("_task_index", -1)
                    case_id = result.get("case_id", "未知案件")

                    failed_results.append({
                        "case_index": task_index,
                        "case_id": case_id,
                        "error": result.get("error", "未知错误")
                    })

            # 合并所有成功的CSV数据
            all_csv_data = []
            all_mermaid_codes = {}
            all_relationship_images = {}

            for result in successful_results:
                if result.get("csv_data"):
                    # 解析CSV数据并添加到总列表
                    csv_lines = result["csv_data"].strip().split('\n')
                    if len(csv_lines) > 1:  # 跳过表头
                        all_csv_data.extend(csv_lines[1:])

                # 保存每个案件的Mermaid代码和关系图
                case_id = result.get("case_id", "")
                if result.get("mermaid_code"):
                    all_mermaid_codes[case_id] = result["mermaid_code"]
                if result.get("relationship_image"):
                    all_relationship_images[case_id] = result["relationship_image"]

            # 生成合并的CSV数据
            if all_csv_data:
                # 使用标准表头
                #header = "batch_id,host_org,case_id,case_name,实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）"
                header = f"批次号,承办单位,案件编号,案件名称,{self.user_requirements_new}"
                merged_csv = header + '\n' + '\n'.join(all_csv_data)
            else:
                merged_csv = ""

            return {
                "status": "success",
                "total_cases": total_cases,
                "successful_count": len(successful_results),
                "failed_count": len(failed_results),
                "merged_csv_data": merged_csv,
                "individual_results": successful_results,
                "failed_cases": failed_results,
                "mermaid_codes": all_mermaid_codes,
                "relationship_images": all_relationship_images,
                "batch_id": batch_id,
                "processing_summary": f"成功处理 {len(successful_results)}/{total_cases} 个案件"
            }

        except Exception as e:
            logging.error(f"批量案件提取失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def _extract_single_case_with_semaphore(self, semaphore: asyncio.Semaphore,
                                                case_data: Dict[str, Any], batch_id: str,
                                                session_id: str, case_index: int,
                                                progress_callback=None) -> Dict[str, Any]:
        """使用信号量控制的单个案件提取"""
        async with semaphore:
            try:
                # 调用进度回调
                if progress_callback:
                    await progress_callback(case_index, "processing", f"正在处理案件: {case_data.get('案件编号', f'案件{case_index+1}')}")

                result = await self._extract_single_case(case_data, batch_id, session_id)

                # 调用进度回调
                if progress_callback:
                    status = "completed" if result.get("status") == "success" else "failed"
                    await progress_callback(case_index, status, f"案件处理{'成功' if status == 'completed' else '失败'}")

                return result

            except Exception as e:
                if progress_callback:
                    await progress_callback(case_index, "failed", f"案件处理异常: {str(e)}")
                raise e

    async def _extract_single_case(self, case_data: Dict[str, Any], batch_id: str, session_id: str) -> Dict[str, Any]:
        """提取单个案件信息"""
        try:
            case_id = case_data.get('案件编号', '')
            case_name = case_data.get('案件名称', '')
            host_org = case_data.get('承办单位', '')
            case_content = case_data.get('案件内容', '')
            rec_time = case_data.get('录入时间', '')
            task_index = case_data.get('_task_index', -1)

            # 记录案件处理开始
            logging.info(f"开始处理案件 - 任务索引: {task_index}, 案件编号: {case_id}, 案件名称: {case_name}")

            # 为每个案件创建独立的智能体实例，避免并发状态污染
            system_message = self._get_system_message()
            agent_name = f"case_extractor_{case_id}_{task_index}"
            case_agent = AssistantAgent(
                name=agent_name,  # 使用唯一名称
                model_client=self.model_client,
                system_message=system_message
            )

            logging.info(f"为案件 {case_id} 创建独立智能体: {agent_name}")

            prompt = f"""
案件编号：{case_id}
案件名称：{case_name}
承办单位：{host_org}
录入时间：{rec_time}
批次号：{batch_id}
任务索引：{task_index}

案件内容：
{case_content}

请按照系统要求分析并提取案件信息。
"""

            # 使用独立的智能体实例处理该案件，移除超时限制
            try:
                logging.info(f"向智能体 {agent_name} 发送请求，案件: {case_id}")

                # 移除超时限制，让模型有充足时间处理复杂案件
                response = await case_agent.on_messages(
                    [TextMessage(content=prompt, source="user")],
                    CancellationToken()
                )

                model_response = response.chat_message.content
                logging.info(f"智能体 {agent_name} 响应成功，响应长度: {len(model_response)}")

            except Exception as e:
                logging.error(f"智能体 {agent_name} 请求失败，案件: {case_id}，错误: {e}")
                raise

            # 解析JSON响应
            try:
                logging.info(f"开始解析智能体 {agent_name} 的响应，案件: {case_id}")

                # 预处理响应，移除思考标签和其他非JSON内容
                preprocessed_response = self._preprocess_response(model_response)

                # 先清理JSON字符串中的控制字符
                cleaned_response = self._clean_json_string(preprocessed_response)
                result = json.loads(cleaned_response)

                logging.info(f"JSON解析成功，案件: {case_id}，字段数量: {len(result)}")

            except json.JSONDecodeError as e:
                logging.warning(f"初次JSON解析失败，案件: {case_id}，错误: {e}")

                # 记录初始错误信息
                setattr(self, f'_last_error_{case_id}', str(e))
                setattr(self, f'_last_error_type_{case_id}', 'JSONDecodeError')

                # 尝试预处理后再提取JSON
                preprocessed_for_regex = self._preprocess_response(model_response)
                json_match = re.search(r'\{.*\}', preprocessed_for_regex, re.DOTALL)
                if json_match:
                    try:
                        logging.info(f"尝试正则提取JSON，案件: {case_id}")
                        # 清理提取的JSON字符串
                        cleaned_json = self._clean_json_string(json_match.group(0))
                        result = json.loads(cleaned_json)
                        logging.info(f"正则提取JSON解析成功，案件: {case_id}")

                    except json.JSONDecodeError as e2:
                        logging.error(f"正则提取JSON解析失败，案件: {case_id}，错误: {e2}")
                        logging.error(f"预处理后响应前500字符: {preprocessed_for_regex[:500]}")

                        # 记录详细错误信息供修复智能体使用
                        setattr(self, f'_last_error_{case_id}', str(e2))
                        setattr(self, f'_last_error_type_{case_id}', 'JSONDecodeError')

                        # 尝试使用修复智能体
                        logging.info(f"尝试使用修复智能体修复响应，案件: {case_id}")
                        repaired_result = await self._try_repair_response(model_response, case_id, batch_id, host_org, case_name, rec_time, case_content)
                        if repaired_result:
                            result = repaired_result
                            logging.info(f"修复智能体成功修复响应，案件: {case_id}")
                        else:
                            # 修复失败，抛出异常终止处理
                            raise Exception(f"案件 {case_id} 修复智能体也无法修复，建议检查案件内容或AI模型配置")
                else:
                    logging.error(f"未找到JSON结构，案件: {case_id}")
                    logging.error(f"预处理后响应前500字符: {preprocessed_for_regex[:500]}")

                    # 记录详细错误信息供修复智能体使用
                    setattr(self, f'_last_error_{case_id}', '未找到JSON结构')
                    setattr(self, f'_last_error_type_{case_id}', '未找到JSON结构')

                    # 尝试使用修复智能体
                    logging.info(f"尝试使用修复智能体修复响应，案件: {case_id}")
                    repaired_result = await self._try_repair_response(model_response, case_id, batch_id, host_org, case_name, rec_time, case_content)
                    if repaired_result:
                        result = repaired_result
                        logging.info(f"修复智能体成功修复响应，案件: {case_id}")
                    else:
                        # 修复失败，抛出异常终止处理
                        raise Exception(f"案件 {case_id} 修复智能体也无法修复，建议检查案件内容或AI模型配置")

            # 处理CSV数据，添加批次号、承办单位、案件编号和案件名称
            csv_data = result.get("csv_data", "")
            processed_csv = self._process_csv_data(csv_data, batch_id, host_org, case_id, case_name)

            # 记录案件处理完成
            logging.info(f"案件处理完成 - 任务索引: {task_index}, 案件编号: {case_id}, 智能体: {agent_name}")

            # 验证结果中的案件编号是否正确
            result_case_id = result.get("case_id", case_id)  # 从AI结果中获取案件编号，如果没有则使用原始编号
            if result_case_id and result_case_id != case_id:
                logging.warning(f"AI返回的案件编号不匹配 - 期望: {case_id}, AI返回: {result_case_id}, 任务索引: {task_index}")

            return {
                "status": "success",
                "case_id": case_id,  # 使用原始案件编号，确保一致性
                "case_name": case_name,
                "host_org": host_org,
                "rec_time": rec_time,
                "analysis": result.get("analysis", ""),
                "csv_data": processed_csv,
                "mermaid_code": result.get("mermaid_code", ""),
                "batch_id": batch_id,
                "extraction_time": datetime.now().isoformat(),
                "original_data": case_data,  # 保存原始案件数据
                "_task_index": case_data.get("_task_index", -1),  # 保存任务索引
                "_agent_name": agent_name  # 保存智能体名称用于调试
            }

        except Exception as e:
            logging.error(f"单个案件信息提取失败: {e}")
            return {
                "status": "error",
                "case_id": case_data.get('案件编号', ''),
                "error": str(e),
                "_task_index": case_data.get("_task_index", -1)  # 保存任务索引
            }

    def _process_csv_data(self, csv_data: str, batch_id: str, host_org: str, case_id: str, case_name: str) -> str:
        """处理CSV数据，添加批次号、承办单位、案件编号和案件名称"""
        lines = csv_data.strip().split('\n')
        if not lines:
            return ""

        # 处理表头
        header = lines[0]
        if not header.startswith("batch_id"):
            header = f"batch_id,host_org,case_id,case_name,{header}"

        # 处理数据行
        processed_lines = [header]
        for line in lines[1:]:
            if line.strip():
                processed_line = f"{batch_id},{host_org},{case_id},{case_name},{line}"
                processed_lines.append(processed_line)

        return '\n'.join(processed_lines)

    def _clean_json_string(self, json_string: str) -> str:
        """
        清理JSON字符串中的控制字符，确保能正确解析

        Args:
            json_string (str): 原始的JSON字符串

        Returns:
            str: 清理后的JSON字符串
        """
        if not json_string:
            return json_string

        # 移除或替换常见的控制字符
        # 保留合法的JSON转义字符：\n, \r, \t, \", \\, \/
        import re

        # 替换不合法的控制字符
        # ASCII控制字符范围：0x00-0x1F (除了 \t \n \r)
        json_string = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F]', '', json_string)

        return json_string

    def _preprocess_response(self, response: str) -> str:
        """预处理AI响应，移除思考标签和其他非JSON内容"""
        try:
            original_response = response

            # 移除<think>...</think>标签及其内容
            response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)

            # 移除其他可能的XML标签，但保留<br/>标签
            response = re.sub(r'<(?!br/?>)[^>]+>', '', response)

            # 移除markdown代码块标记
            response = re.sub(r'```json\s*', '', response)
            response = re.sub(r'```\s*', '', response)

            # 移除多余的空白字符
            response = response.strip()

            # 尝试多种方式找到JSON内容
            json_candidates = []

            # 方法1: 寻找完整的{}块
            brace_count = 0
            start_pos = -1
            for i, char in enumerate(response):
                if char == '{':
                    if brace_count == 0:
                        start_pos = i
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0 and start_pos != -1:
                        json_candidates.append(response[start_pos:i+1])

            # 方法2: 简单的首尾查找
            if not json_candidates:
                json_start = response.find('{')
                json_end = response.rfind('}')
                if json_start != -1 and json_end != -1 and json_end > json_start:
                    json_candidates.append(response[json_start:json_end+1])

            # 选择最长的候选JSON
            if json_candidates:
                response = max(json_candidates, key=len)

            # 如果仍然没有找到有效的JSON结构，返回原始响应
            if not response.strip().startswith('{') or not response.strip().endswith('}'):
                logging.warning("预处理后仍未找到有效JSON结构")
                return original_response

            return response

        except Exception as e:
            logging.warning(f"预处理响应时出错: {e}")
            return response

    async def _try_repair_response(self, failed_response: str, case_id: str, batch_id: str, host_org: str, case_name: str, rec_time: str = "", case_content: str = "") -> Dict[str, Any]:
        """尝试使用修复智能体修复失败的案件提取"""
        try:
            logging.info(f"启动案件提取修复智能体，案件: {case_id}")

            # 检查修复次数限制
            repair_count_key = f"case_repair_count_{case_id}"
            current_repair_count = getattr(self, repair_count_key, 0)
            max_repair_attempts = getattr(self, 'max_repair_attempts', 2)

            if current_repair_count >= max_repair_attempts:
                logging.warning(f"案件 {case_id} 已达到最大修复次数限制 ({max_repair_attempts})，跳过修复")
                return None

            # 增加修复次数
            setattr(self, repair_count_key, current_repair_count + 1)
            logging.info(f"案件 {case_id} 修复尝试 {current_repair_count + 1}/{max_repair_attempts}")

            # 使用专门的修复智能体，传递详细错误信息
            system_message = self._get_system_message()

            # 获取详细的错误信息
            error_details = getattr(self, f'_last_error_{case_id}', '未知错误')
            error_type = getattr(self, f'_last_error_type_{case_id}', 'JSONDecodeError')

            repair_result = await self.repair_agent.repair_case_extraction(
                failed_response=failed_response,
                case_id=case_id,
                batch_id=batch_id,
                host_org=host_org,
                case_name=case_name,
                case_content=case_content,
                rec_time=rec_time,
                system_message=system_message,
                error_details=error_details,
                error_type=error_type
            )

            if repair_result["status"] != "success":
                logging.error(f"修复智能体调用失败，案件: {case_id}，错误: {repair_result.get('error', '未知错误')}")
                return None

            repair_content = repair_result["content"]
            logging.info(f"修复智能体响应长度: {len(repair_content)}")

            # 尝试解析修复后的响应
            preprocessed_repair = self._preprocess_response(repair_content)
            cleaned_repair = self._clean_json_string(preprocessed_repair)
            repaired_result = json.loads(cleaned_repair)

            # 验证修复结果的完整性
            if all(key in repaired_result for key in ["analysis", "csv_data", "mermaid_code"]):
                logging.info(f"修复智能体成功修复响应，案件: {case_id}，修复次数: {current_repair_count + 1}")
                return repaired_result
            else:
                logging.warning(f"修复智能体返回的JSON缺少必要字段，案件: {case_id}")
                return None

        except Exception as e:
            logging.error(f"修复智能体失败，案件: {case_id}，错误: {e}")
            return None


class CaseExtractionRepairAgent:
    """案件提取修复智能体 - 专门修复失败的案件提取任务"""

    def __init__(self, model_manager: ModelManager, session_manager: SessionManager):
        self.model_client = model_manager.get_client()
        self.session_manager = session_manager

    async def repair_case_extraction(self, failed_response: str, case_id: str, batch_id: str,
                                   host_org: str, case_name: str, case_content: str,
                                   rec_time: str = "", system_message: str = "",
                                   error_details: str = "", error_type: str = "") -> Dict[str, Any]:
        """修复失败的案件提取任务 - 增强版，包含详细错误分析"""
        try:
            # 分析错误类型，提供针对性修复建议
            error_analysis = self._analyze_error_type(failed_response, error_details, error_type)

            # 构建增强的修复提示，包含具体错误分析和针对性修复策略
            repair_prompt = f"""
【修复任务说明】
原始案件提取任务失败，需要进行针对性修复。根据错误分析，采用相应的修复策略。

【详细错误分析】
{error_analysis}

【原始失败响应片段】
{failed_response[:1000] if failed_response else "无原始响应"}

【具体错误信息】
错误类型：{error_type}
错误详情：{error_details}

【案件信息】
案件编号：{case_id}
案件名称：{case_name}
承办单位：{host_org}
录入时间：{rec_time}
批次号：{batch_id}

【案件内容】
{case_content}

【针对性修复要求】
{self._get_targeted_repair_requirements(error_type)}

【输出格式强调】
必须严格按照以下JSON格式输出，不得包含任何非JSON内容（如<think>标签、注释等）：
{{
    "analysis": "详细的案件分析内容",
    "csv_data": "CSV格式的结构化数据",
    "mermaid_code": "完整的Mermaid关系图代码"
}}

请基于错误分析进行针对性修复，重新生成完整的案件提取结果。
"""

            # 创建修复智能体
            repair_agent_name = f"case_repair_agent_{case_id}_{int(datetime.now().timestamp())}"
            repair_agent = AssistantAgent(
                name=repair_agent_name,
                model_client=self.model_client,
                system_message=system_message
            )

            logging.info(f"创建案件提取修复智能体: {repair_agent_name}")

            # 调用修复智能体
            repair_response = await repair_agent.on_messages(
                [TextMessage(content=repair_prompt, source="user")],
                CancellationToken()
            )
            repair_content = repair_response.chat_message.content.strip()

            logging.info(f"修复智能体响应长度: {len(repair_content)}")

            # 对修复后的内容进行额外的清理和验证
            cleaned_repair_content = self._deep_clean_response(repair_content)

            # 验证清理后的内容是否为有效JSON
            try:
                parsed_json = json.loads(cleaned_repair_content)
                logging.info(f"修复智能体返回的内容通过JSON验证")

                # 对Mermaid代码进行语法安全检查和修复
                if 'mermaid_code' in parsed_json:
                    original_mermaid = parsed_json['mermaid_code']
                    fixed_mermaid = self._fix_mermaid_syntax(original_mermaid)
                    if fixed_mermaid != original_mermaid:
                        logging.info(f"Mermaid代码已进行语法安全修复")
                        parsed_json['mermaid_code'] = fixed_mermaid
                        cleaned_repair_content = json.dumps(parsed_json, ensure_ascii=False, indent=2)

            except json.JSONDecodeError as e:
                logging.warning(f"修复智能体返回的内容仍有JSON格式问题: {e}")
                # 尝试进一步修复
                cleaned_repair_content = self._emergency_json_fix(cleaned_repair_content)

            return {
                "status": "success",
                "content": cleaned_repair_content
            }

        except Exception as e:
            logging.error(f"案件提取修复智能体失败，案件: {case_id}，错误: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    def _analyze_error_type(self, failed_response: str, error_details: str, error_type: str) -> str:
        """分析错误类型，提供针对性的错误分析"""
        analysis_parts = []

        # 基于错误类型分析
        if "JSONDecodeError" in error_type or "json" in error_details.lower():
            analysis_parts.append("JSON格式错误：响应包含非JSON格式内容或JSON结构不完整")

            # 检查常见的JSON格式问题
            if "<think>" in failed_response:
                analysis_parts.append("发现<think>标签：AI模型输出了思考过程，需要过滤")
            if "```" in failed_response:
                analysis_parts.append("发现代码块标记：响应可能包含Markdown格式")
            if failed_response.count('{') != failed_response.count('}'):
                analysis_parts.append("括号不匹配：JSON结构不完整")

        elif "Expecting value" in error_details:
            analysis_parts.append("JSON值缺失：可能存在空值或格式错误")

        elif "未找到JSON结构" in error_details:
            analysis_parts.append("完全缺失JSON：响应可能是纯文本或其他格式")

        # 分析响应内容特征
        if failed_response:
            if len(failed_response) < 100:
                analysis_parts.append("响应过短：可能是错误信息或不完整响应")
            elif "analysis" not in failed_response and "csv_data" not in failed_response:
                analysis_parts.append("缺少关键字段：响应不包含预期的数据结构")

        return "；".join(analysis_parts) if analysis_parts else "未知错误类型，需要重新生成完整响应"

    def _get_targeted_repair_requirements(self, error_type: str) -> str:
        """根据错误类型提供针对性的修复要求"""
        if "JSONDecodeError" in error_type:
            return """1. 绝对禁止输出<think>、<analysis>等XML标签
2. 绝对禁止使用```json代码块格式
3. 直接输出纯JSON格式，以{开始，以}结束
4. 确保所有字符串值用双引号包围
5. 确保JSON结构完整，括号匹配"""

        elif "未找到JSON" in error_type:
            return """1. 必须输出标准JSON格式
2. 不得输出任何解释性文字
3. 确保包含analysis、csv_data、mermaid_code三个字段
4. 每个字段的值必须是有效的字符串或数组"""

        else:
            return """1. 严格按照JSON格式输出
2. 确保数据结构完整
3. 验证所有必需字段存在
4. 检查数据类型正确性"""

    def _deep_clean_response(self, response: str) -> str:
        """深度清理响应内容，确保JSON格式正确"""
        # 移除所有XML标签
        import re
        response = re.sub(r'<[^>]+>', '', response)

        # 移除代码块标记
        response = re.sub(r'```[a-zA-Z]*\n?', '', response)
        response = re.sub(r'```', '', response)

        # 移除多余的空白字符
        response = re.sub(r'\s+', ' ', response).strip()

        # 确保以{开始，以}结束
        if not response.startswith('{'):
            # 尝试找到第一个{
            start_idx = response.find('{')
            if start_idx != -1:
                response = response[start_idx:]

        if not response.endswith('}'):
            # 尝试找到最后一个}
            end_idx = response.rfind('}')
            if end_idx != -1:
                response = response[:end_idx + 1]

        return response

    def _fix_mermaid_syntax(self, mermaid_code: str) -> str:
        """修复Mermaid语法问题，确保代码可以正常渲染"""
        import re

        # 1. 修复节点内容中的括号
        def fix_node_brackets(match):
            node_content = match.group(1)
            # 将括号替换为连字符
            fixed_content = re.sub(r'\([^)]*\)', lambda m: '-' + m.group(0)[1:-1] + '-', node_content)
            # 清理多余的连字符
            fixed_content = re.sub(r'-+', '-', fixed_content)
            fixed_content = re.sub(r'^-|-$', '', fixed_content)
            return f'[{fixed_content}]'

        # 修复节点定义中的括号
        mermaid_code = re.sub(r'\[([^\]]*\([^\]]*\)[^\]]*)\]', fix_node_brackets, mermaid_code)

        # 2. 修复subgraph名称中的特殊字符
        def fix_subgraph_name(match):
            subgraph_name = match.group(1)
            # 移除顿号、冒号等特殊字符
            fixed_name = subgraph_name.replace('、', '').replace('：', '-').replace('；', '-')
            # 移除括号
            fixed_name = re.sub(r'\([^)]*\)', '', fixed_name)
            return f'subgraph "{fixed_name}"'

        # 修复subgraph名称
        mermaid_code = re.sub(r'subgraph "([^"]*)"', fix_subgraph_name, mermaid_code)

        # 3. 移除其他危险字符
        # 移除转义引号
        mermaid_code = mermaid_code.replace('\\"', '').replace("\\'", '')

        # 移除反斜杠（除了在<br/>标签中的）
        mermaid_code = re.sub(r'\\(?!br)', '', mermaid_code)

        # 4. 修复其他特殊字符
        # 在节点内容中替换其他危险字符
        def fix_node_content(match):
            content = match.group(1)
            # 替换危险字符为连字符
            dangerous_chars = ['#', '$', '%', '&', '*', '+', '=', '|', '~']
            for char in dangerous_chars:
                content = content.replace(char, '-')
            return f'[{content}]'

        # 应用节点内容修复
        mermaid_code = re.sub(r'\[([^\]]+)\]', fix_node_content, mermaid_code)

        return mermaid_code.strip()

    def _emergency_json_fix(self, response: str) -> str:
        """紧急JSON修复，处理常见的JSON格式错误"""
        import re

        # 修复常见的JSON格式问题
        # 1. 修复单引号为双引号
        response = re.sub(r"'([^']*)':", r'"\1":', response)
        response = re.sub(r":\s*'([^']*)'", r': "\1"', response)

        # 2. 修复缺失的逗号
        response = re.sub(r'"\s*\n\s*"', '",\n    "', response)
        response = re.sub(r'}\s*\n\s*"', '},\n    "', response)
        response = re.sub(r']\s*\n\s*"', '],\n    "', response)

        # 3. 修复多余的逗号
        response = re.sub(r',\s*}', '}', response)
        response = re.sub(r',\s*]', ']', response)

        # 4. 确保字符串值被正确引用
        response = re.sub(r':\s*([^",\[\]{}]+)(?=\s*[,}])', r': "\1"', response)

        return response

class BatchDatabaseInsertAgent:
    """批量数据库插入智能体"""

    def __init__(self, db_manager: DatabaseManager, session_manager: SessionManager):
        self.db_manager = db_manager
        self.session_manager = session_manager

    def execute_batch_insert(self, csv_data: str, session_id: str) -> Dict[str, Any]:
        """批量插入数据到数据库"""
        try:
            # 解析CSV数据
            records = self._parse_csv_data(csv_data)

            if not records:
                return {
                    "status": "error",
                    "message": "没有有效的数据记录可插入"
                }

            # 准备插入语句 - 使用新的表结构
            insert_sql = """
                INSERT INTO mydb.ds_case_details (
                    batch_id, host_org, case_id, case_name, entity_type, name_code, gender, age,
                    id_card, residence, education, direct_superior,
                    organization, org_level, role, related_actions,
                    judicial_result, economic, data_time
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, NOW()
                )
            """

            # 执行批量插入
            connection = self.db_manager.get_connection()
            try:
                with connection.cursor() as cursor:
                    # 批量插入
                    affected_rows = cursor.executemany(insert_sql, records)
                    connection.commit()

                    # 保存插入记录到会话目录
                    session_dir = self.session_manager.get_session_dir(session_id)
                    insert_log_file = session_dir / "data" / "batch_insert_log.json"

                    log_data = {
                        "insert_time": datetime.now().isoformat(),
                        "total_records": len(records),
                        "affected_rows": affected_rows,
                        "sql": insert_sql,
                        "status": "success"
                    }

                    with open(insert_log_file, 'w', encoding='utf-8') as f:
                        json.dump(log_data, f, ensure_ascii=False, indent=2)

                    return {
                        "status": "success",
                        "message": f"成功插入 {len(records)} 条记录",
                        "total_records": len(records),
                        "affected_rows": affected_rows,
                        "sql": insert_sql
                    }

            except Exception as e:
                connection.rollback()
                logging.error(f"数据库插入失败: {e}")
                return {
                    "status": "error",
                    "message": f"数据库插入失败: {str(e)}",
                    "total_records": len(records)
                }
            finally:
                connection.close()

        except Exception as e:
            logging.error(f"批量插入处理失败: {e}")
            return {
                "status": "error",
                "message": f"批量插入处理失败: {str(e)}"
            }

    def _parse_csv_data(self, csv_data: str) -> List[tuple]:
        """解析CSV数据为插入记录列表"""
        lines = csv_data.strip().split('\n')
        if len(lines) < 2:
            raise ValueError("CSV数据格式错误，缺少表头或数据行")

        # 跳过表头，处理数据行
        records = []
        for line_num, line in enumerate(lines[1:], start=2):
            if not line.strip():
                continue

            try:
                # 解析CSV行
                fields = self._parse_csv_line(line)

                if len(fields) < 18:  # 至少需要18个字段
                    logging.warning(f"第{line_num}行字段数量不足，跳过: {line}")
                    continue

                # 提取字段值
                batch_id = fields[0].strip()
                host_org = fields[1].strip()
                case_id = fields[2].strip()
                case_name = fields[3].strip()
                entity_type = fields[4].strip()
                name_code = fields[5].strip()
                gender = fields[6].strip()
                age_str = fields[7].strip()
                id_card = fields[8].strip()
                residence = fields[9].strip()
                education = fields[10].strip()
                direct_superior = fields[11].strip()
                organization = fields[12].strip()
                org_level = fields[13].strip()
                role = fields[14].strip()
                related_actions = fields[15].strip()
                judicial_result = fields[16].strip()
                economic_str = fields[17].strip()

                # 数据类型转换和清理
                age = self._convert_to_int(age_str)
                economic = self._convert_to_decimal(economic_str)

                # 空值处理
                def clean_field(value):
                    if not value or value.lower() in ['null', 'none', 'n/a', '无', '']:
                        return None
                    return value

                record = (
                    clean_field(batch_id),
                    clean_field(host_org),
                    clean_field(case_id),
                    clean_field(case_name),
                    clean_field(entity_type),
                    clean_field(name_code),
                    clean_field(gender),
                    age,
                    clean_field(id_card),
                    clean_field(residence),
                    clean_field(education),
                    clean_field(direct_superior),
                    clean_field(organization),
                    clean_field(org_level),
                    clean_field(role),
                    clean_field(related_actions),
                    clean_field(judicial_result),
                    economic
                )

                records.append(record)

            except Exception as e:
                logging.error(f"解析第{line_num}行数据失败: {line}, 错误: {e}")
                continue

        return records

    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行，处理包含逗号的字段"""
        import csv
        import io

        # 使用Python的csv模块解析
        reader = csv.reader(io.StringIO(line))
        try:
            return next(reader)
        except StopIteration:
            return line.split(',')

    def _convert_to_int(self, value: str) -> Optional[int]:
        """将字符串转换为整数"""
        if not value or value.lower() in ['null', 'none', 'n/a', '无', '']:
            return None

        try:
            # 提取数字部分
            import re
            numbers = re.findall(r'\d+', value)
            if numbers:
                return int(numbers[0])
            return None
        except:
            return None

    def _convert_to_decimal(self, value: str) -> Optional[float]:
        """将字符串转换为数字（经济收益）"""
        if not value or value.lower() in ['null', 'none', 'n/a', '无', '']:
            return None

        try:
            # 移除中文字符和符号，提取数字
            import re
            # 移除"元"、"万"、"千"等单位
            cleaned = re.sub(r'[^\d\.\-]', '', value)
            if cleaned:
                result = float(cleaned)
                # 处理单位转换
                if '万' in value:
                    result *= 10000
                elif '千' in value:
                    result *= 1000
                return result
            return None
        except:
            return None

class BatchReportGeneratorAgent:
    """批量报告生成智能体"""

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager

    def generate_batch_reports(self, batch_results: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """批量生成HTML报告"""
        try:
            session_dir = self.session_manager.get_session_dir(session_id)
            reports_dir = session_dir / "reports"
            reports_dir.mkdir(exist_ok=True)

            individual_results = batch_results.get("individual_results", [])
            relationship_images = batch_results.get("relationship_images", {})

            generated_reports = {}

            for result in individual_results:
                case_id = result.get("case_id", "")
                case_name = result.get("case_name", "未知案件")

                if not case_id:
                    continue

                # 生成单个案件的报告
                report_result = self._generate_single_report(
                    result, session_id, relationship_images.get(case_id)
                )

                if report_result["status"] == "success":
                    generated_reports[case_id] = report_result

            return {
                "status": "success",
                "generated_count": len(generated_reports),
                "reports": generated_reports,
                "reports_dir": str(reports_dir)
            }

        except Exception as e:
            logging.error(f"批量报告生成失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    def _generate_single_report(self, case_result: Dict[str, Any], session_id: str, relationship_image: str = None) -> Dict[str, Any]:
        """生成单个案件的HTML报告"""
        try:
            session_dir = self.session_manager.get_session_dir(session_id)
            reports_dir = session_dir / "reports"

            case_id = case_result.get("case_id", "")
            case_name = case_result.get("case_name", "未知案件")
            analysis = case_result.get("analysis", "")
            csv_data = case_result.get("csv_data", "")
            original_data = case_result.get("original_data", {})

            # 生成文件名
            filename = f"{case_id}.html" if case_id else f"{case_name}.html"
            output_path = reports_dir / filename

            # 格式化分析内容
            formatted_analysis = self._format_analysis_content(analysis)

            # 格式化案件内容
            formatted_case_content = self._format_case_content(original_data)

            # 解析CSV数据为表格（排除前4列：batch_id, host_org, case_id, case_name）
            table_html = self._csv_to_html_table(csv_data, exclude_columns=['batch_id', 'host_org', 'case_id', 'case_name'])

            # 生成HTML内容
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{case_id}:{case_name}案件分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }}
        .analysis-section {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            white-space: pre-wrap;
            line-height: 1.8;
            font-size: 14px;
        }}
        .case-content-section {{
            background-color: #fff8dc;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .case-content-section h3 {{
            color: #d35400;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 2px solid #f39c12;
            padding-bottom: 5px;
        }}
        .case-content-section h3:first-child {{
            margin-top: 0;
        }}
        .case-content-section p {{
            line-height: 1.8;
            font-size: 14px;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: justify;
            text-indent: 2em;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }}
        th {{
            background-color: #3498db;
            color: white;
            font-weight: bold;
            text-align: center;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .relationship-image {{
            text-align: center;
            margin: 20px 0;
        }}
        .relationship-image img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .timestamp {{
            text-align: right;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 30px;
        }}
        .case-info {{
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{case_id}:{case_name}案件分析报告</h1>

        <h2>📁 案件信息</h2>
        <div class="case-info">
            <p><strong>案件编号:</strong> {case_id}</p>
            <p><strong>案件名称:</strong> {case_name}</p>
            <p><strong>承办单位:</strong> {case_result.get('host_org', 'N/A')}</p>
            <p><strong>批次号:</strong> {case_result.get('batch_id', 'N/A')}</p>
            <p><strong>分析时间:</strong> {case_result.get('extraction_time', 'N/A')}</p>
        </div>

        {f'<h2>📄 案件内容</h2><div class="case-content-section">{formatted_case_content}</div>' if formatted_case_content else ''}

        {f'<h2>📊 分析过程</h2><div class="analysis-section">{formatted_analysis}</div>' if analysis else ''}

        <h2>👥 案件人员信息</h2>
        {table_html}

        {f'<h2>🔗 案件人员关系图</h2><div class="relationship-image"><img src="data:image/png;base64,{relationship_image}" alt="人物关系图"/></div>' if relationship_image else ''}

        <div class="timestamp">
            报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>
    </div>
</body>
</html>
"""

            # 保存HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return {
                "status": "success",
                "html_content": html_content,
                "file_path": str(output_path),
                "filename": filename,
                "case_id": case_id,
                "case_name": case_name
            }

        except Exception as e:
            logging.error(f"单个报告生成失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    def _format_case_content(self, original_data: Dict[str, Any]) -> str:
        """格式化案件内容，提高可读性"""
        if not original_data:
            return ""

        # 定义内容字段和对应的标题
        content_fields = ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明']
        field_titles = {
            '正文内容': '一、正文内容',
            '到案情况': '二、到案情况',
            '依法侦查查明': '三、依法侦查查明',
            '犯罪证据': '四、犯罪证据',
            '综上所述': '五、综上所述',
            '其他说明': '六、其他说明'
        }

        content_parts = []
        for field in content_fields:
            if field in original_data and original_data[field] and str(original_data[field]).strip() and str(original_data[field]) != 'nan':
                field_title = field_titles.get(field, field)
                field_content = str(original_data[field]).strip()

                # 将内容分段，提高可读性
                paragraphs = field_content.split('\n')
                formatted_paragraphs = []
                for paragraph in paragraphs:
                    paragraph = paragraph.strip()
                    if paragraph:
                        formatted_paragraphs.append(f"<p>{paragraph}</p>")

                if formatted_paragraphs:
                    content_parts.append(f"<h3>{field_title}</h3>{''.join(formatted_paragraphs)}")

        return ''.join(content_parts) if content_parts else ""

    def _format_analysis_content(self, analysis_text: str) -> str:
        """格式化分析内容，提高可读性"""
        if not analysis_text:
            return analysis_text

        # 处理常见的格式问题
        formatted_text = analysis_text.strip()

        # 处理标题格式
        import re
        formatted_text = re.sub(r'(第[一二三四五六七八九十]+步[：:])', r'<h3>\1</h3>', formatted_text)
        formatted_text = re.sub(r'(步骤[0-9]+[：:])', r'<h3>\1</h3>', formatted_text)

        # 处理数字编号步骤
        formatted_text = re.sub(r'(\n|^)([0-9]+[\.\、])', r'\1<div class="step">\2', formatted_text)

        # 在句号后添加段落分隔
        formatted_text = re.sub(r'([。！？])([^<\n])', r'\1</p><p>\2', formatted_text)

        # 处理冒号后的内容
        formatted_text = re.sub(r'([：:])([^<\n])', r'\1<br>\2', formatted_text)

        # 处理特殊标记
        formatted_text = re.sub(r'【([^】]+)】', r'<strong>【\1】</strong>', formatted_text)

        # 添加段落标签
        if not formatted_text.startswith('<'):
            formatted_text = f'<p>{formatted_text}</p>'

        # 关闭未关闭的div标签
        if '<div class="step">' in formatted_text:
            formatted_text = re.sub(r'(<div class="step">[^<]*?)(?=<div class="step">|$)', r'\1</div>', formatted_text)
            if not formatted_text.endswith('</div>'):
                formatted_text += '</div>'

        # 清理多余的空段落
        formatted_text = re.sub(r'<p>\s*</p>', '', formatted_text)
        formatted_text = re.sub(r'<p>\s*<br>\s*</p>', '', formatted_text)

        return formatted_text

    def _csv_to_html_table(self, csv_data: str, exclude_columns: List[str] = None) -> str:
        """将CSV数据转换为HTML表格"""
        if not csv_data.strip():
            return "<p>无数据</p>"

        exclude_columns = exclude_columns or []

        lines = csv_data.strip().split('\n')
        if len(lines) < 2:
            return "<p>数据格式错误</p>"

        # 表头
        headers = [h.strip() for h in lines[0].split(',')]

        # 找到要排除的列的索引
        exclude_indices = []
        filtered_headers = []
        for i, header in enumerate(headers):
            if header.lower() not in [col.lower() for col in exclude_columns]:
                filtered_headers.append(header)
            else:
                exclude_indices.append(i)

        html = "<table>\n<thead>\n<tr>\n"
        for header in filtered_headers:
            html += f"<th>{header}</th>\n"
        html += "</tr>\n</thead>\n<tbody>\n"

        # 数据行
        for line in lines[1:]:
            if line.strip():
                cells = [c.strip() for c in line.split(',')]
                # 过滤掉排除的列
                filtered_cells = []
                for i, cell in enumerate(cells):
                    if i not in exclude_indices:
                        filtered_cells.append(cell)

                html += "<tr>\n"
                for cell in filtered_cells:
                    # 处理长文本换行
                    if len(cell) > 30:
                        cell = cell[:27] + "..."
                    html += f"<td>{cell}</td>\n"
                html += "</tr>\n"

        html += "</tbody>\n</table>"
        return html

class MultiCaseAnalysisOrchestrator:
    """多案件分析编排器"""

    def __init__(self, max_concurrent: int = 10, max_repair_attempts: int = 2, max_case_repair_attempts: int = 2):
        self.session_manager = SessionManager()
        self.model_manager = ModelManager()
        self.db_manager = DatabaseManager()
        self.file_processor = ExcelFileProcessor()
        self.batch_extractor = BatchCaseExtractionAgent(self.model_manager, self.session_manager, max_concurrent, max_case_repair_attempts)
        self.batch_db_inserter = BatchDatabaseInsertAgent(self.db_manager, self.session_manager)
        self.relationship_visualizer = RelationshipVisualizationAgent(
            self.session_manager, self.model_manager, max_repair_attempts
        )
        self.batch_report_generator = BatchReportGeneratorAgent(self.session_manager)
        self.conversation_manager = ConversationManager(self.session_manager)
        self.max_repair_attempts = max_repair_attempts

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def save_uploaded_file(self, session_id: str, file_content: bytes, original_filename: str) -> str:
        """保存上传的文件，保持原始文件名"""
        return self.session_manager.save_uploaded_file(session_id, file_content, original_filename)

    def save_user_analysis(self, session_id: str, analysis_content: str, filename: str) -> str:
        """保存用户输入的案件分析内容"""
        return self.session_manager.save_user_analysis(session_id, analysis_content, filename)

    def save_user_config(self, session_id: str, config_data: dict, filename: str) -> str:
        """保存用户配置信息"""
        return self.session_manager.save_user_config(session_id, config_data, filename)

    async def _generate_relationship_images_concurrently(self, results: List[Dict],
                                                       session_id: str,
                                                       user_requirements: str) -> Dict[str, str]:
        """并发生成关系图"""
        if not results:
            return {}

        # 动态调整并发数：根据系统资源和案件数量
        max_concurrent_images = self._calculate_optimal_concurrency(len(results))
        semaphore = asyncio.Semaphore(max_concurrent_images)

        logging.info(f"开始并发生成 {len(results)} 个关系图，并发数: {max_concurrent_images}")

        async def generate_single_image(result):
            async with semaphore:
                case_id = result.get("case_id", "")
                mermaid_code = result.get("mermaid_code", "")
                case_content = result.get("original_data", {}).get("正文内容", "")

                if not mermaid_code or not case_id:
                    return case_id, None

                try:
                    start_time = time.time()
                    visualization_result = await self.relationship_visualizer.render_mermaid_to_image(
                        mermaid_code, session_id, case_id, user_requirements, 0, case_content
                    )

                    generation_time = time.time() - start_time

                    if visualization_result["status"] == "success":
                        logging.info(f"案件 {case_id} 关系图生成成功，耗时: {generation_time:.2f}秒")
                        return case_id, visualization_result["image_base64"]
                    else:
                        logging.warning(f"案件 {case_id} 关系图生成失败: {visualization_result.get('error', '未知错误')}")
                        if visualization_result.get("max_repairs_exceeded"):
                            logging.warning(f"案件 {case_id} 已超过最大修复次数 ({self.max_repair_attempts})")
                        return case_id, None

                except Exception as e:
                    logging.error(f"案件 {case_id} 关系图生成异常: {e}")
                    return case_id, None

        # 并发执行所有关系图生成任务
        start_time = time.time()
        tasks = [generate_single_image(result) for result in results]
        task_results = await asyncio.gather(*tasks, return_exceptions=True)

        total_time = time.time() - start_time

        # 整理结果
        relationship_images = {}
        success_count = 0
        for task_result in task_results:
            if isinstance(task_result, Exception):
                logging.error(f"关系图生成任务异常: {task_result}")
                continue

            if isinstance(task_result, tuple) and len(task_result) == 2:
                case_id, image_base64 = task_result
                if case_id and image_base64:
                    relationship_images[case_id] = image_base64
                    success_count += 1

        logging.info(f"关系图并发生成完成: {success_count}/{len(results)} 成功，总耗时: {total_time:.2f}秒")
        return relationship_images

    def _calculate_optimal_concurrency(self, total_cases: int) -> int:
        """动态计算最优并发数"""
        # 基础并发数配置
        base_concurrent = 8
        max_concurrent = 20

        # 根据案件数量调整
        if total_cases <= 5:
            optimal = min(total_cases, base_concurrent)
        elif total_cases <= 20:
            optimal = min(5, total_cases)
        elif total_cases <= 50:
            optimal = min(6, total_cases)
        else:
            optimal = min(max_concurrent, total_cases)

        # 确保至少有1个并发
        return max(1, optimal)

    def get_visualization_performance_stats(self) -> Dict[str, Any]:
        """获取关系图生成性能统计"""
        if hasattr(self.relationship_visualizer, 'get_generation_stats'):
            return self.relationship_visualizer.get_generation_stats()
        return {
            "total_generated": 0,
            "success_count": 0,
            "failure_count": 0,
            "success_rate": 0.0,
            "average_time": 0.0
        }

    async def process_multi_case_file(self, file_path: str, session_id: str, user_requirements: str = None,
                                    max_concurrent: int = 10, progress_callback=None, user_relationship_images: str = None, user_analysis: str = None) -> Dict[str, Any]:
        """处理多案件文件的完整工作流"""
        try:
            # 更新并发数
            self.batch_extractor.max_concurrent = max_concurrent

            # 读取Excel/CSV文件
            df, file_info = self.file_processor.read_excel_file(file_path)

            # 预处理数据
            preprocess_result = self.file_processor.preprocess_case_data(df)

            if preprocess_result["status"] != "success":
                return preprocess_result

            processed_data = preprocess_result["processed_data"]

            # 如果processed_data是字典列表，直接使用；如果是DataFrame，转换为字典列表
            if isinstance(processed_data, list):
                cases_data = processed_data
            else:
                # 假设是DataFrame，转换为字典列表
                cases_data = processed_data

            # 生成批次ID
            batch_id = f"batch_{int(datetime.now().timestamp())}"

            # 并发提取案件信息
            extraction_result = await self.batch_extractor.extract_multiple_cases(
                cases_data, batch_id, session_id, user_requirements, user_relationship_images, user_analysis, progress_callback
            )

            if extraction_result["status"] != "success":
                return extraction_result

            # 不再生成关系图图片，直接使用Mermaid代码
            # relationship_images = await self._generate_relationship_images_concurrently(
            #     extraction_result["individual_results"],
            #     session_id,
            #     user_requirements
            # )

            # 从extraction_result中获取mermaid_codes
            mermaid_codes = extraction_result.get("mermaid_codes", {})

            # 构建完整的批次结果
            batch_result = {
                "status": "success",
                "batch_id": batch_id,
                "file_info": file_info,
                "preprocessing": preprocess_result,
                "extraction": extraction_result,
                "mermaid_codes": mermaid_codes,  # 使用Mermaid代码而不是图片
                "total_cases": len(cases_data),
                "successful_cases": extraction_result["successful_count"],
                "failed_cases": extraction_result["failed_count"],
                "merged_csv_data": extraction_result["merged_csv_data"],
                "processing_time": datetime.now().isoformat(),
                "imported_to_db": False
            }

            # 保存批次数据到对话管理器
            self.conversation_manager.set_case_data(session_id, batch_result)

            return batch_result

        except Exception as e:
            self.logger.error(f"处理多案件文件失败: {e}")
            return {
                "status": "error",
                "message": f"处理多案件文件失败: {str(e)}"
            }

    async def insert_batch_to_database(self, session_id: str) -> Dict[str, Any]:
        """批量插入数据到数据库"""
        try:
            # 获取批次数据
            conversation = self.conversation_manager.get_conversation(session_id)
            batch_data = conversation.get("case_data")

            if not batch_data:
                return {
                    "status": "error",
                    "message": "没有可用的批次数据"
                }

            csv_data = batch_data.get("merged_csv_data", "")
            if not csv_data:
                return {
                    "status": "error",
                    "message": "没有可插入的CSV数据"
                }

            # 执行批量插入
            insert_result = self.batch_db_inserter.execute_batch_insert(csv_data, session_id)

            # 如果插入成功，更新批次数据中的导入状态
            if insert_result["status"] == "success":
                batch_data["imported_to_db"] = True
                self.conversation_manager.set_case_data(session_id, batch_data)

            return insert_result

        except Exception as e:
            self.logger.error(f"批量数据库插入失败: {e}")
            return {
                "status": "error",
                "message": f"批量数据库插入失败: {str(e)}"
            }

    def generate_batch_reports(self, session_id: str) -> Dict[str, Any]:
        """批量生成分析报告"""
        try:
            # 获取批次数据
            conversation = self.conversation_manager.get_conversation(session_id)
            batch_data = conversation.get("case_data")

            if not batch_data:
                return {
                    "status": "error",
                    "message": "没有可用的批次数据"
                }

            extraction_result = batch_data.get("extraction", {})

            # 生成批量报告
            report_result = self.batch_report_generator.generate_batch_reports(
                extraction_result, session_id
            )

            return report_result

        except Exception as e:
            self.logger.error(f"批量报告生成失败: {e}")
            return {
                "status": "error",
                "message": f"批量报告生成失败: {str(e)}"
            }
