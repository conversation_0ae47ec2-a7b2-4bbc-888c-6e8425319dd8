[Unit]
Description=案件处理系统 - 每小时定时任务
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/data/ai/AJagent-main
Environment=PATH=/bigai/envs/StAutoGen/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/bigai/envs/StAutoGen/bin/python main_controller.py 1
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=case-processor

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ReadWritePaths=/data/ai/AJagent-main/logs
ReadWritePaths=/data/ai/AJagent-main/downloads
ReadWritePaths=/data/ai/AJagent-main/temp

[Install]
WantedBy=multi-user.target
