@echo off
chcp 65001 >nul
echo ========================================
echo Excel导入SQL工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查必要的Python包
echo 🔍 检查依赖包...
python -c "import pandas, pymysql" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要的依赖包，正在安装...
    pip install pandas openpyxl pymysql
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
)

REM 检查主脚本是否存在
if not exist "main_excel_to_sql.py" (
    echo ❌ 错误: 未找到 main_excel_to_sql.py 文件
    pause
    exit /b 1
)

echo.
echo 📋 使用说明:
echo 1. 请准备好Excel文件，确保包含以下字段：
echo    - 案件编号
echo    - 数据版本号  
echo    - 正文内容
echo    - 到案情况
echo    - 依法侦查查明
echo    - 犯罪证据
echo    - 综上所述
echo    - 其他说明
echo.
echo 2. 数据将导入到数据库: djzs_db.ds_case_instrument_his_ai
echo 3. 数据库服务器: ***********
echo.

REM 询问是否先运行测试
set /p run_test="是否先运行测试验证环境? (y/N): "
if /i "%run_test%"=="y" (
    echo.
    echo 🧪 运行测试...
    python test_excel_to_sql.py
    echo.
    echo 测试完成，按任意键继续...
    pause >nul
    echo.
)

REM 获取Excel文件路径
set /p excel_path="请输入Excel文件路径（拖拽文件到此窗口）: "

REM 去除路径两端的引号
set excel_path=%excel_path:"=%

REM 检查Excel文件是否存在
if not exist "%excel_path%" (
    echo ❌ 错误: Excel文件不存在: %excel_path%
    pause
    exit /b 1
)

echo.
echo 🔄 开始导入...
echo Excel文件: %excel_path%
echo 目标数据库: djzs_db.ds_case_instrument_his_ai
echo.

REM 执行导入
python main_excel_to_sql.py "%excel_path%"

if errorlevel 1 (
    echo.
    echo ❌ 导入失败，请检查：
    echo 1. 数据库连接是否正常
    echo 2. Excel文件格式是否正确
    echo 3. 是否包含所有必需字段
    echo 4. 查看日志文件 excel_to_sql.log
) else (
    echo.
    echo 🎉 导入完成！
    echo 📊 数据已成功插入到数据库
    echo 📝 详细日志请查看: excel_to_sql.log
    
    REM 询问是否查看日志
    set /p view_log="是否查看日志文件? (y/N): "
    if /i "%view_log%"=="y" (
        if exist "excel_to_sql.log" (
            notepad excel_to_sql.log
        ) else (
            echo ⚠️  日志文件不存在
        )
    )
)

echo.
pause
