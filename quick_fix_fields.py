#!/usr/bin/env python3
"""
快速修复数据库字段问题
"""

import pymysql
import sys
import os

# 添加当前目录到Python路径
sys.path.append('/data/ai/AJagent-main')

try:
    from config import config
except ImportError:
    print("❌ 无法导入config模块，请检查路径")
    sys.exit(1)

def fix_database_fields():
    """修复数据库字段"""
    
    print("🔧 开始修复数据库字段")
    print("="*50)
    
    try:
        # 获取数据库配置
        db_config = config.get_db_config()
        print(f"数据库配置: {db_config['host']}:{db_config['port']}")
        
        # 连接数据库
        with pymysql.connect(**db_config) as connection:
            with connection.cursor() as cursor:
                
                # 1. 检查当前表结构
                print("1. 检查当前表结构...")
                cursor.execute("DESCRIBE `djzs_db`.`ds_case_relation`")
                columns = cursor.fetchall()
                
                existing_fields = [col[0] for col in columns]
                print(f"   现有字段: {', '.join(existing_fields)}")
                
                # 2. 需要添加的字段
                fields_to_add = [
                    ("tfsj", "datetime DEFAULT NULL COMMENT '填发时间'"),
                    ("xgsj", "datetime DEFAULT NULL COMMENT '修改时间'"),
                    ("counts", "int DEFAULT NULL COMMENT '意见书数量'"),
                    ("xxzjbh", "varchar(250) DEFAULT NULL COMMENT '信息主键编号'")
                ]
                
                # 3. 添加缺失的字段
                print("\n2. 添加缺失的字段...")
                for field_name, field_definition in fields_to_add:
                    if field_name not in existing_fields:
                        try:
                            sql = f"ALTER TABLE `djzs_db`.`ds_case_relation` ADD COLUMN `{field_name}` {field_definition}"
                            cursor.execute(sql)
                            print(f"   ✅ 添加字段: {field_name}")
                        except Exception as e:
                            print(f"   ❌ 添加字段 {field_name} 失败: {e}")
                    else:
                        print(f"   ⚠️  字段 {field_name} 已存在")
                
                # 4. 提交更改
                connection.commit()
                print("\n3. 提交数据库更改...")
                
                # 5. 验证修复结果
                print("\n4. 验证修复结果...")
                cursor.execute("DESCRIBE `djzs_db`.`ds_case_relation`")
                new_columns = cursor.fetchall()
                
                new_fields = [col[0] for col in new_columns]
                print(f"   更新后字段: {', '.join(new_fields)}")
                
                # 6. 测试查询
                print("\n5. 测试查询...")
                try:
                    cursor.execute("SELECT batchid, ajbh, ajmc, tfsj, xgsj, counts, xxzjbh FROM `djzs_db`.`ds_case_relation` LIMIT 1")
                    result = cursor.fetchone()
                    print(f"   ✅ 查询测试成功")
                except Exception as e:
                    print(f"   ❌ 查询测试失败: {e}")
                    return False
                
                print(f"\n✅ 数据库字段修复完成！")
                return True
                
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        return False

def test_case_extraction():
    """测试case_extraction_agent的查询"""
    
    print(f"\n🧪 测试case_extraction_agent查询")
    print("="*50)
    
    try:
        db_config = config.get_db_config()
        
        with pymysql.connect(**db_config) as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                
                # 测试修复后的查询
                sql = """
                SELECT batchid, ajbh, ajmc, ajnr, tfsj, ajlx, tbrksj
                FROM `djzs_db`.`ds_case_relation`
                WHERE status = '2' AND ajnr IS NOT NULL
                LIMIT 1
                """
                
                cursor.execute(sql)
                result = cursor.fetchone()
                
                if result:
                    print(f"✅ 查询成功，找到记录:")
                    print(f"   ajbh: {result['ajbh']}")
                    print(f"   ajmc: {result['ajmc']}")
                    print(f"   tfsj: {result['tfsj']}")
                else:
                    print(f"⚠️  没有找到符合条件的记录（status='2' AND ajnr IS NOT NULL）")
                
                return True
                
    except Exception as e:
        print(f"❌ 查询测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 数据库字段快速修复工具")
    print("="*60)
    
    # 1. 修复数据库字段
    fix_result = fix_database_fields()
    
    if fix_result:
        # 2. 测试case_extraction查询
        test_result = test_case_extraction()
        
        if test_result:
            print(f"\n🎉 修复完成！现在可以正常运行case_extraction_agent了")
            print(f"\n📋 下一步:")
            print(f"1. 重新运行主程序")
            print(f"2. 检查PDF合并功能")
        else:
            print(f"\n⚠️  字段修复成功，但查询测试失败")
    else:
        print(f"\n❌ 字段修复失败，请手动检查数据库")
    
    return fix_result

if __name__ == "__main__":
    main()
