#!/bin/bash

# PDF服务器启动脚本

echo "🚀 启动PDF文件服务器..."

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3，请先安装Python3"
    exit 1
fi

# 检查当前目录
CURRENT_DIR=$(pwd)
echo "📁 当前目录: $CURRENT_DIR"

# 检查test.pdf是否存在
if [ -f "test.pdf" ]; then
    echo "✅ 找到test.pdf文件"
else
    echo "⚠️  警告: 未找到test.pdf文件"
    echo "请确保test.pdf文件在当前目录中"
fi

# 获取本机IP地址
LOCAL_IP=$(hostname -I | awk '{print $1}')
if [ -z "$LOCAL_IP" ]; then
    LOCAL_IP="localhost"
fi

echo "🌐 服务器将在以下地址启动:"
echo "   本地访问: http://localhost:9999"
echo "   网络访问: http://$LOCAL_IP:9999"
echo ""
echo "📄 PDF文件访问地址:"
echo "   在线查看: http://$LOCAL_IP:9999/api/v1/pdfHandler/getPdf?filename=test.pdf&action=view"
echo "   直接下载: http://$LOCAL_IP:9999/api/v1/pdfHandler/getPdf?filename=test.pdf&action=download"
echo ""
echo "🏠 主页地址: http://$LOCAL_IP:9999/"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=================================="

# 启动Python服务器
python3 pdf_server.py
