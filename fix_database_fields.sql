-- 修复数据库字段问题
-- 添加缺失的字段到现有的ds_case_relation表

USE `djzs_db`;

-- 检查当前表结构
DESCRIBE `ds_case_relation`;

-- 添加缺失的字段（如果不存在）
-- 注意：使用IF NOT EXISTS语法可能不被所有MySQL版本支持，所以使用ALTER TABLE ADD COLUMN

-- 添加填发时间字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN `tfsj` datetime DEFAULT NULL COMMENT '填发时间' AFTER `ajmc`;

-- 添加修改时间字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN `xgsj` datetime DEFAULT NULL COMMENT '修改时间' AFTER `tfsj`;

-- 添加意见书数量字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN `counts` int DEFAULT NULL COMMENT '意见书数量' AFTER `xgsj`;

-- 添加信息主键编号字段
ALTER TABLE `ds_case_relation` 
ADD COLUMN `xxzjbh` varchar(250) NOT NULL COMMENT '信息主键编号' AFTER `counts`;

-- 如果rksj字段存在，可以将其数据迁移到tfsj字段
-- UPDATE `ds_case_relation` SET `tfsj` = `rksj` WHERE `tfsj` IS NULL AND `rksj` IS NOT NULL;

-- 验证表结构
DESCRIBE `ds_case_relation`;

-- 查看表中的数据
SELECT COUNT(*) as total_records FROM `ds_case_relation`;

-- 显示最新的几条记录
SELECT batchid, ajbh, ajmc, tfsj, xgsj, counts, xxzjbh, status 
FROM `ds_case_relation` 
ORDER BY starttime DESC 
LIMIT 5;
