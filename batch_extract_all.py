#!/usr/bin/env python3
"""
批量处理所有剩余案件的要素提取
"""

import asyncio
import sys
import os
import pymysql
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from case_extraction_agent import CaseExtractionAgent
from config import config


async def batch_extract_all_remaining():
    """批量处理所有剩余的STATUS=2案件"""
    
    print("=" * 80)
    print("批量要素提取 - 处理所有剩余案件")
    print("=" * 80)
    
    try:
        # 1. 获取所有STATUS=2的案件
        db_config = config.get_db_config()
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        table_config = config.get_table_config()
        case_relation_table = table_config['case_relation_table']
        
        query = f"""
        SELECT ajbh, batchid, ajnr, ajmc
        FROM `{case_relation_table}`
        WHERE status = '2'
        ORDER BY updatetime DESC
        """
        
        cursor.execute(query)
        pending_cases = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        if not pending_cases:
            print("✅ 没有待处理案件，所有案件都已完成!")
            return {"status": "success", "message": "没有待处理案件"}
        
        print(f"📊 批量处理信息:")
        print(f"  待处理案件数: {len(pending_cases)}")
        print(f"  开始时间: {datetime.now()}")
        
        # 2. 初始化智能体
        agent = CaseExtractionAgent()
        print(f"  ✅ 智能体初始化成功")
        
        # 3. 控制并发数，避免过载
        semaphore = asyncio.Semaphore(2)  # 限制并发数为2
        
        async def process_with_semaphore(case_info, index):
            async with semaphore:
                ajbh = case_info['ajbh']
                print(f"[{index+1}/{len(pending_cases)}] 处理案件: {ajbh}")
                
                try:
                    # 确保case_info包含所有必要字段
                    complete_case_info = {
                        'ajbh': case_info['ajbh'],
                        'batchid': case_info['batchid'],
                        'ajnr': case_info['ajnr'] or '',
                        'ajmc': case_info.get('ajmc') or f"案件_{case_info['ajbh']}"
                    }
                    
                    start_time = datetime.now()
                    result = await agent.process_single_case_complete(complete_case_info)
                    end_time = datetime.now()
                    
                    duration = (end_time - start_time).total_seconds()
                    
                    if result.get("status") == "success":
                        print(f"  ✅ 成功 ({duration:.1f}秒)")
                        return {"status": "success", "ajbh": ajbh, "duration": duration}
                    else:
                        error_msg = result.get('error', '未知错误')
                        print(f"  ❌ 失败 ({duration:.1f}秒): {error_msg}")
                        return {"status": "error", "ajbh": ajbh, "error": error_msg, "duration": duration}
                        
                except Exception as e:
                    error_msg = str(e)
                    print(f"  ❌ 异常: {error_msg}")
                    return {"status": "error", "ajbh": ajbh, "error": error_msg}
        
        # 4. 并发处理所有案件
        print(f"\n🚀 开始并发处理...")
        print("-" * 60)
        
        tasks = []
        for i, case_info in enumerate(pending_cases):
            task = process_with_semaphore(case_info, i)
            tasks.append(task)
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 5. 统计结果
        print(f"\n" + "=" * 80)
        print(f"📈 批量处理完成统计")
        print("=" * 80)
        
        successful_count = 0
        failed_count = 0
        total_duration = 0
        successful_cases = []
        failed_cases = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_count += 1
                failed_cases.append({"error": str(result)})
            elif result.get("status") == "success":
                successful_count += 1
                successful_cases.append(result)
                total_duration += result.get("duration", 0)
            else:
                failed_count += 1
                failed_cases.append(result)
        
        print(f"总处理案件: {len(pending_cases)}")
        print(f"成功案件: {successful_count} ✅")
        print(f"失败案件: {failed_count} ❌")
        print(f"成功率: {(successful_count/len(pending_cases)*100):.1f}%")
        print(f"平均处理时间: {(total_duration/max(successful_count, 1)):.1f} 秒/案件")
        print(f"完成时间: {datetime.now()}")
        
        # 6. 检查最终数据库状态
        print(f"\n🔍 检查最终数据库状态...")
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        cursor.execute(f'SELECT status, COUNT(*) as count FROM `{case_relation_table}` GROUP BY status ORDER BY status')
        final_results = cursor.fetchall()
        
        print(f"最终状态分布:")
        status_desc = {
            0: '初始状态',
            1: 'PDF合并完成',
            2: 'OCR识别完成', 
            3: '要素提取完成',
            4: '处理失败'
        }
        
        for status, count in final_results:
            desc = status_desc.get(status, f'状态{status}')
            print(f"  STATUS={status} ({desc}): {count} 个案件")
        
        cursor.close()
        connection.close()
        
        # 7. 最终评估
        print(f"\n🎯 分片批处理系统最终评估:")
        
        if successful_count == len(pending_cases):
            print(f"🌟 完美成功! 所有案件都完成了要素提取!")
            print(f"🎉 分片批处理系统完全验证成功!")
        elif successful_count >= len(pending_cases) * 0.8:
            print(f"✅ 高成功率! {successful_count}/{len(pending_cases)} 案件完成要素提取")
            print(f"🎉 分片批处理系统基本验证成功!")
        elif successful_count > 0:
            print(f"⚠️  部分成功! {successful_count}/{len(pending_cases)} 案件完成要素提取")
            print(f"💡 建议检查失败案件的具体原因")
        else:
            print(f"❌ 需要检查! 没有案件成功完成要素提取")
        
        return {
            "status": "success",
            "total_cases": len(pending_cases),
            "successful_count": successful_count,
            "failed_count": failed_count,
            "successful_cases": successful_cases,
            "failed_cases": failed_cases
        }
        
    except Exception as e:
        print(f"批量处理失败: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}


async def main():
    """主函数"""
    print("分片批处理系统 - 批量要素提取")
    print(f"运行时间: {datetime.now()}")
    
    result = await batch_extract_all_remaining()
    
    if result and result.get("status") == "success":
        success_rate = (result["successful_count"] / result["total_cases"]) * 100 if result["total_cases"] > 0 else 0
        
        print(f"\n🎊 批量处理完成!")
        print(f"📊 最终统计: {result['successful_count']}/{result['total_cases']} 成功 ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print(f"🌟 分片批处理系统验证完全成功!")
        elif success_rate >= 50:
            print(f"✅ 分片批处理系统验证基本成功!")
        else:
            print(f"⚠️  分片批处理系统需要进一步优化")


if __name__ == "__main__":
    asyncio.run(main())
