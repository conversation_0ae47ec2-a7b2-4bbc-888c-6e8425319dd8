{% extends "base.html" %}

{% block title %}Mermaid URL调试 - {{ debug_info.case.ajbh }} - 案件关系图查看器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 返回按钮 -->
        <div class="mb-3">
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            <a href="{{ url_for('case_detail', ajbh=debug_info.case.ajbh) }}" class="btn btn-outline-primary">
                <i class="bi bi-eye"></i> 查看详情
            </a>
        </div>

        <!-- 案件信息 -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-bug"></i> Mermaid URL调试信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>案件编号：</strong>{{ debug_info.case.ajbh }}</p>
                        <p><strong>案件名称：</strong>{{ debug_info.case.ajmc or 'N/A' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>代码长度：</strong>{{ debug_info.mermaid_code_length }} 字符</p>
                        <p><strong>基础URL：</strong>{{ debug_info.base_url }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 代码预览 -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="bi bi-code-slash"></i> Mermaid代码预览
                </h6>
            </div>
            <div class="card-body">
                <div class="border rounded p-3" style="background-color: #f8f9fa; max-height: 200px; overflow-y: auto;">
                    <pre style="white-space: pre-wrap; font-size: 12px;">{{ debug_info.mermaid_code_preview }}</pre>
                </div>
            </div>
        </div>

        <!-- URL测试结果 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="bi bi-link-45deg"></i> URL创建结果
                </h6>
            </div>
            <div class="card-body">
                <!-- 基础URL -->
                <div class="mb-4">
                    <h6 class="text-primary">基础URL方法</h6>
                    <div class="input-group mb-2">
                        <span class="input-group-text">URL:</span>
                        <input type="text" class="form-control" value="{{ debug_info.basic_url }}" readonly>
                        <button class="btn btn-outline-primary" onclick="copyToClipboard('{{ debug_info.basic_url }}')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                        <a href="{{ debug_info.basic_url }}" class="btn btn-primary" target="_blank">
                            <i class="bi bi-box-arrow-up-right"></i> 测试
                        </a>
                    </div>
                </div>

                <!-- 高级URL方法 -->
                <h6 class="text-success">高级URL方法</h6>
                
                {% for method, url in debug_info.advanced_urls.items() %}
                <div class="mb-3">
                    <label class="form-label">
                        <strong>{{ method.replace('_', ' ').title() }}:</strong>
                        {% if method == 'pako_json' %}
                        <span class="badge bg-success">推荐</span>
                        {% elif method == 'base64_json' %}
                        <span class="badge bg-info">备选</span>
                        {% elif method == 'simple_json' %}
                        <span class="badge bg-warning">简化</span>
                        {% else %}
                        <span class="badge bg-secondary">实验</span>
                        {% endif %}
                    </label>
                    
                    {% if url.startswith('Error') %}
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> {{ url }}
                    </div>
                    {% else %}
                    <div class="input-group">
                        <input type="text" class="form-control" value="{{ url }}" readonly>
                        <button class="btn btn-outline-primary" onclick="copyToClipboard('{{ url }}')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                        <a href="{{ url }}" class="btn btn-primary" target="_blank">
                            <i class="bi bi-box-arrow-up-right"></i> 测试
                        </a>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> 使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>URL方法优先级：</h6>
                        <ol>
                            <li><strong>Pako JSON</strong> - 压缩效果最好，推荐使用</li>
                            <li><strong>Base64 JSON</strong> - 标准方法，兼容性好</li>
                            <li><strong>Simple JSON</strong> - 简化配置，适合调试</li>
                            <li><strong>Direct Code</strong> - 直接编码，可能不工作</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>测试步骤：</h6>
                        <ol>
                            <li>点击"测试"按钮打开编辑器</li>
                            <li>检查代码是否正确加载</li>
                            <li>验证图表是否正常渲染</li>
                            <li>如果失败，尝试下一个方法</li>
                        </ol>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i class="bi bi-lightbulb"></i> 
                    <strong>提示：</strong>如果所有URL都无法工作，请检查本地Mermaid Live Editor是否正常运行在 {{ debug_info.base_url }}
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="bi bi-lightning"></i> 快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('view_mermaid', ajbh=debug_info.case.ajbh) }}" 
                       class="btn btn-primary" target="_blank">
                        <i class="bi bi-diagram-3"></i> 直接打开编辑器
                    </a>
                    <a href="{{ url_for('mermaid_view', ajbh=debug_info.case.ajbh) }}" 
                       class="btn btn-outline-primary" target="_blank">
                        <i class="bi bi-eye"></i> 网页预览
                    </a>
                    <button class="btn btn-outline-success" onclick="copyCode()">
                        <i class="bi bi-clipboard"></i> 复制代码
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的代码区域用于复制 -->
<textarea id="hiddenCode" style="position: absolute; left: -9999px;">{{ debug_info.case.lastcode }}</textarea>
{% endblock %}

{% block extra_js %}
<script>
// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showToast('已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// 复制代码
function copyCode() {
    const codeElement = document.getElementById('hiddenCode');
    const text = codeElement.value;
    copyToClipboard(text);
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('已复制到剪贴板');
        } else {
            showToast('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    }
    
    document.body.removeChild(textArea);
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
