<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件详细信息 - {{ detail.name_code or detail.ajbh }}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .title {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .nav-links {
            display: flex;
            gap: 15px;
        }
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            padding: 8px 16px;
            border: 1px solid #007bff;
            border-radius: 4px;
            transition: all 0.3s;
        }
        .nav-links a:hover {
            background-color: #007bff;
            color: white;
        }
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .detail-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        .field-row {
            display: flex;
            margin-bottom: 12px;
            align-items: flex-start;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
            min-width: 120px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .field-value {
            color: #333;
            word-break: break-all;
            flex: 1;
        }
        .field-value.empty {
            color: #6c757d;
            font-style: italic;
        }
        .long-text {
            background-color: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-deleted {
            background-color: #f8d7da;
            color: #721c24;
        }
        .back-button {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .back-button:hover {
            background-color: #545b62;
        }
        .metadata {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">案件详细信息</h1>
            <div class="nav-links">
                <a href="/">案件关系图</a>
                <a href="/case_details">详细信息列表</a>
                <a href="/case/{{ detail.ajbh }}">案件详情</a>
            </div>
        </div>

        <div class="detail-grid">
            <!-- 基本信息 -->
            <div class="detail-section">
                <div class="section-title">基本信息</div>
                <div class="field-row">
                    <div class="field-label">ID:</div>
                    <div class="field-value">{{ detail.id }}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">批次号:</div>
                    <div class="field-value">{{ detail.batchid }}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">案件编号:</div>
                    <div class="field-value">{{ detail.ajbh }}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">案件名称:</div>
                    <div class="field-value {{ 'empty' if not detail.ajmc }}">
                        {{ detail.ajmc or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">状态:</div>
                    <div class="field-value">
                        <span class="status-badge {{ 'status-active' if detail.isdelete != '1' else 'status-deleted' }}">
                            {{ detail.isdelete_text }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 实体信息 -->
            <div class="detail-section">
                <div class="section-title">实体信息</div>
                <div class="field-row">
                    <div class="field-label">实体类型:</div>
                    <div class="field-value {{ 'empty' if not detail.entity_type }}">
                        {{ detail.entity_type or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">姓名/代号:</div>
                    <div class="field-value {{ 'empty' if not detail.name_code }}">
                        {{ detail.name_code or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">性别:</div>
                    <div class="field-value {{ 'empty' if not detail.gender }}">
                        {{ detail.gender or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">年龄:</div>
                    <div class="field-value {{ 'empty' if not detail.age }}">
                        {{ detail.age or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">身份证号:</div>
                    <div class="field-value {{ 'empty' if not detail.id_card }}">
                        {{ detail.id_card or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">户籍地/现居地:</div>
                    <div class="field-value {{ 'empty' if not detail.residence }}">
                        {{ detail.residence or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">文化程度:</div>
                    <div class="field-value {{ 'empty' if not detail.education }}">
                        {{ detail.education or '未填写' }}
                    </div>
                </div>
            </div>

            <!-- 组织关系 -->
            <div class="detail-section">
                <div class="section-title">组织关系</div>
                <div class="field-row">
                    <div class="field-label">直接上级:</div>
                    <div class="field-value {{ 'empty' if not detail.direct_superior }}">
                        {{ detail.direct_superior or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">所属公司:</div>
                    <div class="field-value {{ 'empty' if not detail.company }}">
                        {{ detail.company or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">所属组织:</div>
                    <div class="field-value {{ 'empty' if not detail.organization }}">
                        {{ detail.organization or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">分工角色:</div>
                    <div class="field-value {{ 'empty' if not detail.role }}">
                        {{ detail.role or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">主要职责:</div>
                    <div class="field-value {{ 'empty' if not detail.responsibilities }}">
                        {{ detail.responsibilities or '未填写' }}
                    </div>
                </div>
            </div>

            <!-- 关联关系 -->
            <div class="detail-section">
                <div class="section-title">关联关系</div>
                <div class="field-row">
                    <div class="field-label">横向关联人物:</div>
                    <div class="field-value {{ 'empty' if not detail.peers_name }}">
                        {{ detail.peers_name or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">横向关联关系:</div>
                    <div class="field-value {{ 'empty' if not detail.peers }}">
                        {{ detail.peers or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">纵向关联人物:</div>
                    <div class="field-value {{ 'empty' if not detail.vertical_name }}">
                        {{ detail.vertical_name or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">纵向关联关系:</div>
                    <div class="field-value {{ 'empty' if not detail.vertical }}">
                        {{ detail.vertical or '未填写' }}
                    </div>
                </div>
            </div>

            <!-- 关联物品和行为 -->
            <div class="detail-section">
                <div class="section-title">关联物品和行为</div>
                <div class="field-row">
                    <div class="field-label">关联工具:</div>
                    <div class="field-value {{ 'empty' if not detail.related_tools }}">
                        {{ detail.related_tools or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">关联物品:</div>
                    <div class="field-value {{ 'empty' if not detail.related_items }}">
                        {{ detail.related_items or '未填写' }}
                    </div>
                </div>
            </div>

            <!-- 司法信息 -->
            <div class="detail-section">
                <div class="section-title">司法信息</div>
                <div class="field-row">
                    <div class="field-label">强制措施:</div>
                    <div class="field-value {{ 'empty' if not detail.measures }}">
                        {{ detail.measures or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">司法处置结果:</div>
                    <div class="field-value {{ 'empty' if not detail.judicial_result }}">
                        {{ detail.judicial_result or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">经济收益:</div>
                    <div class="field-value {{ 'empty' if not detail.economic }}">
                        {{ detail.economic or '未填写' }}
                    </div>
                </div>
                <div class="field-row">
                    <div class="field-label">前科:</div>
                    <div class="field-value {{ 'empty' if not detail.criminal }}">
                        {{ detail.criminal or '未填写' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- 长文本字段 -->
        {% if detail.related_actions or detail.related_locations %}
        <div class="detail-section">
            <div class="section-title">详细信息</div>
            {% if detail.related_actions %}
            <div class="field-row">
                <div class="field-label">关联犯罪行为:</div>
                <div class="field-value">
                    <div class="long-text">{{ detail.related_actions }}</div>
                </div>
            </div>
            {% endif %}
            {% if detail.related_locations %}
            <div class="field-row">
                <div class="field-label">关联场所:</div>
                <div class="field-value">
                    <div class="long-text">{{ detail.related_locations }}</div>
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- 元数据 -->
        <div class="metadata">
            <strong>数据信息：</strong>
            数据插入时间：{{ detail.data_time_formatted or '未知' }} |
            更新时间：{{ detail.update_time_formatted or '未更新' }}
        </div>

        <a href="/case_details" class="back-button">返回列表</a>
    </div>
</body>
</html>
