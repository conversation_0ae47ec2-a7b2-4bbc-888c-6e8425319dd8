#!/usr/bin/env python3
"""
分片批处理配置验证脚本
验证配置文件和系统环境是否正确设置
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config


def verify_config():
    """验证配置"""
    print("=" * 60)
    print("分片批处理配置验证")
    print("=" * 60)
    
    try:
        # 1. 验证系统配置
        system_config = config.get_system_config()
        
        print("1. 系统配置验证:")
        print(f"   OCR基础路径: {system_config['ocr_base_path']}")
        print(f"   Docker容器名: {system_config['docker_container']}")
        print(f"   分片批大小: {system_config['batch_slice_size']}")
        print(f"   最大并发数: {system_config['max_concurrent']}")
        
        # 验证分片批大小配置
        batch_slice_size = system_config.get('batch_slice_size')
        if batch_slice_size is None:
            print("   ❌ 分片批大小配置缺失")
            return False
        elif not isinstance(batch_slice_size, int) or batch_slice_size <= 0:
            print(f"   ❌ 分片批大小配置无效: {batch_slice_size}")
            return False
        else:
            print(f"   ✓ 分片批大小配置正确: {batch_slice_size}")
        
        # 2. 验证OCR基础路径
        ocr_base_path = system_config['ocr_base_path']
        print(f"\n2. OCR路径验证:")
        print(f"   基础路径: {ocr_base_path}")
        
        if os.path.exists(ocr_base_path):
            print(f"   ✓ OCR基础路径存在")
            
            # 检查路径权限
            if os.access(ocr_base_path, os.R_OK | os.W_OK):
                print(f"   ✓ OCR基础路径可读写")
            else:
                print(f"   ⚠️  OCR基础路径权限不足")
        else:
            print(f"   ❌ OCR基础路径不存在")
            return False
        
        # 3. 验证数据库配置
        db_config = config.get_db_config()
        print(f"\n3. 数据库配置验证:")
        print(f"   主机: {db_config['host']}")
        print(f"   数据库: {db_config['database']}")
        print(f"   用户: {db_config['user']}")
        print(f"   ✓ 数据库配置完整")
        
        # 4. 验证表名配置
        table_config = config.get_table_config()
        print(f"\n4. 表名配置验证:")
        print(f"   案件关系表: {table_config['case_relation_table']}")
        print(f"   案件详情表: {table_config['case_details_table']}")
        print(f"   源数据表: {table_config['source_table']}")
        print(f"   ✓ 表名配置完整")
        
        # 5. 模拟分片批计算
        print(f"\n5. 分片批计算验证:")
        test_cases = [23, 35, 100, 600]
        
        for case_count in test_cases:
            slice_count = (case_count + batch_slice_size - 1) // batch_slice_size
            print(f"   {case_count} 个案件 → {slice_count} 个分片批")
        
        print(f"\n✅ 所有配置验证通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 配置验证失败: {e}")
        return False


def verify_imports():
    """验证模块导入"""
    print("\n" + "=" * 60)
    print("模块导入验证")
    print("=" * 60)
    
    modules_to_test = [
        ('pdf_batch_slicer', 'PDFBatchSlicer'),
        ('ocr_processor', 'OCRProcessor'),
        ('main_controller', 'MainController'),
        ('data_fetcher', 'DataFetcher'),
        ('pdf_downloader', 'PDFDownloader'),
        ('pdf_merger', 'PDFMerger'),
        ('case_extraction_agent', 'CaseExtractionAgent')
    ]
    
    all_imports_ok = True
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name)
            cls = getattr(module, class_name)
            print(f"   ✓ {module_name}.{class_name}")
        except ImportError as e:
            print(f"   ❌ {module_name}.{class_name} - 导入失败: {e}")
            all_imports_ok = False
        except AttributeError as e:
            print(f"   ❌ {module_name}.{class_name} - 类不存在: {e}")
            all_imports_ok = False
        except Exception as e:
            print(f"   ❌ {module_name}.{class_name} - 其他错误: {e}")
            all_imports_ok = False
    
    if all_imports_ok:
        print(f"\n✅ 所有模块导入成功！")
    else:
        print(f"\n❌ 部分模块导入失败，请检查代码")
    
    return all_imports_ok


def main():
    """主函数"""
    print("分片批处理系统配置验证")
    print(f"验证时间: {datetime.now()}")
    
    # 验证配置
    config_ok = verify_config()
    
    # 验证导入
    imports_ok = verify_imports()
    
    print("\n" + "=" * 60)
    print("验证结果汇总:")
    print("=" * 60)
    
    if config_ok and imports_ok:
        print("🎉 系统配置验证全部通过！")
        print("   可以开始使用分片批处理功能")
        print("\n建议的下一步:")
        print("   1. 运行 python test_batch_slicing.py 进行功能测试")
        print("   2. 在实际环境中测试小批量案件")
        print("   3. 逐步增加批次大小验证性能")
    else:
        print("❌ 系统配置验证失败！")
        print("   请根据上述错误信息修复配置问题")
    
    print("=" * 60)


if __name__ == "__main__":
    from datetime import datetime
    main()
