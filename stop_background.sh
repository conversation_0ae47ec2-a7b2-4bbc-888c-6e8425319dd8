#!/bin/bash
# 停止后台案件处理系统脚本

cd /data/ai/AJagent-main

echo "🛑 停止案件处理系统后台服务..."

# 检查PID文件是否存在
if [ -f "logs/system.pid" ]; then
    PID=$(cat logs/system.pid)
    echo "找到进程ID: $PID"
    
    # 检查进程是否还在运行
    if ps -p $PID > /dev/null 2>&1; then
        echo "正在停止进程 $PID..."
        
        # 优雅停止（发送SIGTERM信号）
        kill $PID
        
        # 等待5秒
        sleep 5
        
        # 检查进程是否已停止
        if ps -p $PID > /dev/null 2>&1; then
            echo "进程未响应，强制停止..."
            kill -9 $PID
            sleep 2
        fi
        
        # 再次检查
        if ps -p $PID > /dev/null 2>&1; then
            echo "❌ 无法停止进程 $PID"
            exit 1
        else
            echo "✅ 进程 $PID 已停止"
        fi
    else
        echo "⚠️  进程 $PID 不存在或已停止"
    fi
    
    # 删除PID文件
    rm -f logs/system.pid
    echo "🗑️  已清理PID文件"
else
    echo "⚠️  未找到PID文件，尝试查找相关进程..."
    
    # 查找并停止所有相关进程
    PIDS=$(ps aux | grep "python.*start.py" | grep -v grep | awk '{print $2}')
    
    if [ -n "$PIDS" ]; then
        echo "找到相关进程: $PIDS"
        for pid in $PIDS; do
            echo "停止进程: $pid"
            kill $pid
        done
        sleep 3
        
        # 强制停止仍在运行的进程
        REMAINING=$(ps aux | grep "python.*start.py" | grep -v grep | awk '{print $2}')
        if [ -n "$REMAINING" ]; then
            echo "强制停止剩余进程: $REMAINING"
            for pid in $REMAINING; do
                kill -9 $pid
            done
        fi
        echo "✅ 所有相关进程已停止"
    else
        echo "ℹ️  未找到运行中的案件处理系统进程"
    fi
fi

echo ""
echo "📋 系统状态:"
echo "运行中的Python进程:"
ps aux | grep python | grep -v grep || echo "无"
