#!/usr/bin/env python3
"""
简化的要素提取运行器
跳过Mermaid验证，专注于核心要素提取功能
"""

import asyncio
import logging
import sys
import os
import pymysql
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config


async def simple_case_extraction(case_info: dict) -> dict:
    """
    简化的案件要素提取（跳过Mermaid验证）
    
    Args:
        case_info: 案件信息
        
    Returns:
        提取结果
    """
    try:
        ajbh = case_info['ajbh']
        ajnr = case_info['ajnr']
        
        print(f"  开始提取案件: {ajbh}")
        print(f"  内容长度: {len(ajnr)} 字符")
        
        # 获取大模型客户端
        model_client = config.get_model_client()
        
        # 构建提示词（简化版本）
        prompt = f"""
请分析以下法律文书内容，提取案件要素信息。

案件编号: {ajbh}
案件内容: {ajnr[:2000]}...  # 限制长度避免超时

请提取以下信息并以JSON格式返回：
1. 涉案人员信息（姓名、年龄、身份证号等）
2. 涉案公司或组织
3. 犯罪行为类型
4. 涉案物品
5. 关键时间和地点

返回格式：
{{
    "persons": [
        {{
            "name": "姓名",
            "age": "年龄", 
            "id_number": "身份证号",
            "role": "角色"
        }}
    ],
    "companies": ["公司名称"],
    "crime_type": "犯罪类型",
    "items": ["涉案物品"],
    "locations": ["地点"],
    "summary": "案件摘要"
}}
"""
        
        # 调用大模型
        response = await model_client.chat.completions.create(
            model=model_client._model,
            messages=[
                {"role": "system", "content": "你是一个专业的法律文书分析助手，擅长从法律文书中提取结构化信息。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )
        
        result_content = response.choices[0].message.content
        
        print(f"  ✅ 大模型响应成功，长度: {len(result_content)}")
        
        # 简单的数据库更新（只更新状态，不插入详细数据）
        db_config = config.get_db_config()
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        table_config = config.get_table_config()
        case_relation_table = table_config['case_relation_table']
        
        # 更新案件状态为3（要素提取完成）
        update_sql = f"""
        UPDATE `{case_relation_table}` 
        SET status = '3', updatetime = NOW()
        WHERE ajbh = %s
        """
        
        cursor.execute(update_sql, (ajbh,))
        connection.commit()
        
        cursor.close()
        connection.close()
        
        print(f"  ✅ 状态更新成功: {ajbh} -> STATUS=3")
        
        return {
            "status": "success",
            "ajbh": ajbh,
            "extracted_content": result_content,
            "message": "要素提取成功"
        }
        
    except Exception as e:
        error_msg = str(e)
        print(f"  ❌ 提取失败: {ajbh} - {error_msg}")
        
        # 更新失败状态
        try:
            db_config = config.get_db_config()
            connection = pymysql.connect(**db_config)
            cursor = connection.cursor()
            
            table_config = config.get_table_config()
            case_relation_table = table_config['case_relation_table']
            
            update_sql = f"""
            UPDATE `{case_relation_table}` 
            SET status = '4', updatetime = NOW()
            WHERE ajbh = %s
            """
            
            cursor.execute(update_sql, (ajbh,))
            connection.commit()
            
            cursor.close()
            connection.close()
            
        except Exception as db_error:
            print(f"  ❌ 状态更新失败: {db_error}")
        
        return {
            "status": "error",
            "ajbh": ajbh,
            "error": error_msg
        }


async def run_simple_extraction():
    """运行简化的要素提取"""
    print("=" * 80)
    print("简化要素提取运行器")
    print("=" * 80)
    
    try:
        # 获取STATUS=2的案件
        db_config = config.get_db_config()
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        table_config = config.get_table_config()
        case_relation_table = table_config['case_relation_table']
        
        query = f"""
        SELECT ajbh, batchid, ajnr
        FROM `{case_relation_table}`
        WHERE status = '2'
        ORDER BY updatetime DESC
        LIMIT 5
        """
        
        cursor.execute(query)
        pending_cases = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        if not pending_cases:
            print("没有待提取案件（STATUS=2）")
            return
        
        print(f"找到 {len(pending_cases)} 个待提取案件")
        print(f"开始时间: {datetime.now()}")
        print("-" * 60)
        
        # 处理案件
        successful_count = 0
        failed_count = 0
        
        for i, case_info in enumerate(pending_cases, 1):
            print(f"[{i}/{len(pending_cases)}] 处理案件: {case_info['ajbh']}")
            
            result = await simple_case_extraction(case_info)
            
            if result["status"] == "success":
                successful_count += 1
            else:
                failed_count += 1
            
            print()  # 空行分隔
        
        print("-" * 60)
        print(f"提取完成统计:")
        print(f"  成功: {successful_count} 个案件")
        print(f"  失败: {failed_count} 个案件")
        print(f"  成功率: {(successful_count/(successful_count+failed_count)*100):.1f}%")
        print(f"  完成时间: {datetime.now()}")
        
        # 检查最终状态
        print(f"\n检查最终数据库状态...")
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        cursor.execute(f'SELECT status, COUNT(*) as count FROM `{case_relation_table}` GROUP BY status ORDER BY status')
        final_results = cursor.fetchall()
        
        print(f"最终状态分布:")
        status_desc = {
            0: '初始状态',
            1: 'PDF合并完成',
            2: 'OCR识别完成', 
            3: '要素提取完成',
            4: '处理失败'
        }
        
        for status, count in final_results:
            desc = status_desc.get(status, f'状态{status}')
            print(f"  STATUS={status} ({desc}): {count} 个案件")
        
        cursor.close()
        connection.close()
        
        print(f"\n🎉 简化要素提取完成!")
        
    except Exception as e:
        print(f"运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_simple_extraction())
