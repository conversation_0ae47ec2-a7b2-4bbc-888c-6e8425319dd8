-- 更新数据库表结构
-- 将现有的ds_case_relation表更新为新的结构

USE `djzs_db`;

-- 备份现有表（可选）
-- CREATE TABLE `ds_case_relation_backup` AS SELECT * FROM `ds_case_relation`;

-- 删除现有表并重新创建（如果需要完全重建）
-- DROP TABLE IF EXISTS `ds_case_relation`;

-- 创建新的表结构
CREATE TABLE IF NOT EXISTS `ds_case_relation` (
  `batchid` varchar(50) NOT NULL COMMENT '数据批次号',
  `ajbh` varchar(40) NOT NULL COMMENT '案件编号',
  `ajmc` varchar(250) DEFAULT NULL COMMENT '案件名称',
  `tfsj` datetime DEFAULT NULL COMMENT '填发时间',
  `xgsj` datetime DEFAULT NULL COMMENT '修改时间',
  `counts` int DEFAULT NULL COMMENT '意见书数量',
  `xxzjbh` varchar(250) DEFAULT NULL COMMENT '信息主键编号',
  `ajlx` varchar(30) DEFAULT NULL COMMENT '案件类型',
  `flwsxzdz` varchar(250) DEFAULT NULL COMMENT '法律文书下载地址',
  `tbrksj` timestamp NULL DEFAULT NULL COMMENT '同步入库时间',
  `ajnr` text COMMENT '案件内容',
  `code` text COMMENT '关系图代码',
  `lastcode` text COMMENT '最终的关系图代码',
  `updater` varchar(20) DEFAULT NULL COMMENT '修改人员',
  `updatetype` varchar(10) DEFAULT NULL COMMENT '是否人工修改，0 否，1是【0 AI生成，1人工修改】',
  `status` varchar(10) DEFAULT NULL COMMENT 'ai处理状态，0:开始，1:下载完成，2:ocr完成，3:ai完成，4:报错',
  `starttime` timestamp NULL DEFAULT NULL COMMENT 'AI开始处理时间',
  `endtime` timestamp NULL DEFAULT NULL COMMENT 'AI完成处理时间',
  `updatetime` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `nums` int DEFAULT NULL COMMENT 'ai重跑次数',
  `error` text COMMENT '错误信息',
  `isdelete` varchar(10) DEFAULT '0' COMMENT '是否删除，0 否，1是',
  PRIMARY KEY (`batchid`, `ajbh`) USING BTREE,
  KEY `idx_ajbh` (`ajbh`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_batchid` (`batchid`) USING BTREE,
  KEY `idx_isdelete` (`isdelete`) USING BTREE,
  KEY `idx_ajbh_isdelete` (`ajbh`, `isdelete`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='案件关系图表';

-- 如果是从旧表迁移数据，可以使用以下语句
-- 注意：需要根据实际的旧表结构调整字段映射

/*
INSERT INTO `ds_case_relation` 
(batchid, ajbh, ajmc, tfsj, xgsj, counts, xxzjbh, ajlx, flwsxzdz, tbrksj, 
 ajnr, code, lastcode, updater, updatetype, status, starttime, endtime, updatetime, nums, error)
SELECT 
    batchid,
    ajbh,
    ajmc,
    NULL as tfsj,                    -- 新字段，设为NULL
    NULL as xgsj,                    -- 新字段，设为NULL
    1 as counts,                     -- 新字段，默认为1
    ajbh as xxzjbh,                  -- 新字段，暂时用ajbh填充
    ajlx,
    flwsxzdz,
    tbrksj,
    ajnr,
    code,
    lastcode,
    updater,
    updatetype,
    status,
    starttime,
    endtime,
    updatetime,
    nums,
    error
FROM `ds_case_relation_backup`
ON DUPLICATE KEY UPDATE
    ajmc = VALUES(ajmc),
    ajlx = VALUES(ajlx),
    flwsxzdz = VALUES(flwsxzdz),
    tbrksj = VALUES(tbrksj),
    ajnr = VALUES(ajnr),
    code = VALUES(code),
    lastcode = VALUES(lastcode),
    updatetime = NOW();
*/

-- 验证表结构
DESCRIBE `ds_case_relation`;

-- 查看表中的数据量
SELECT COUNT(*) as total_records FROM `ds_case_relation`;

-- 查看最近的记录
SELECT batchid, ajbh, ajmc, counts, status, starttime 
FROM `ds_case_relation` 
ORDER BY starttime DESC 
LIMIT 10;
