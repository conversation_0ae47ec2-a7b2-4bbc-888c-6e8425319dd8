{% extends "base.html" %}

{% block title %}代码字段显示测试 - 案件关系图查看器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-code-slash"></i> 代码字段显示测试</h2>
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                <i class="bi bi-list-ul"></i> 返回案件列表
            </a>
        </div>

        <!-- 测试说明 -->
        <div class="alert alert-info">
            <h5><i class="bi bi-info-circle"></i> 测试内容</h5>
            <ul class="mb-0">
                <li>✅ 验证关系图代码 (code) 字段显示</li>
                <li>✅ 验证最终代码 (lastcode) 字段显示</li>
                <li>✅ 验证"关系图"按钮功能 (使用streamlit_app.py相同方式)</li>
                <li>✅ 验证URL创建和跳转功能</li>
            </ul>
        </div>

        {% if cases %}
        <!-- 有代码的案件列表 -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-check-circle"></i> 找到 {{ cases|length }} 个有代码的案件
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>案件编号</th>
                                <th>案件名称</th>
                                <th>关系图代码 (code)</th>
                                <th>最终代码 (lastcode)</th>
                                <th>操作测试</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for case in cases %}
                            <tr>
                                <td>
                                    <strong>{{ case.ajbh }}</strong>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="{{ case.ajmc }}">
                                        {{ case.ajmc or 'N/A' }}
                                    </div>
                                </td>
                                <td>
                                    {% if case.has_original_code %}
                                    <div class="border rounded p-2" style="background-color: #f8f9fa;">
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="bi bi-code-slash text-info me-2"></i>
                                            <span class="badge bg-info">有代码</span>
                                        </div>
                                        <div class="text-truncate" style="max-width: 300px;" title="{{ case.code }}">
                                            <small><code>{{ case.code_short }}</code></small>
                                        </div>
                                        <div class="mt-1">
                                            <small class="text-muted">长度: {{ case.code|length }} 字符</small>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">无代码</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if case.has_mermaid %}
                                    <div class="border rounded p-2" style="background-color: #f0fff0;">
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="bi bi-diagram-3 text-success me-2"></i>
                                            <span class="badge bg-success">有代码</span>
                                        </div>
                                        <div class="text-truncate" style="max-width: 300px;" title="{{ case.lastcode }}">
                                            <small><code>{{ case.lastcode_short }}</code></small>
                                        </div>
                                        <div class="mt-1">
                                            <small class="text-muted">长度: {{ case.lastcode|length }} 字符</small>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">无代码</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-vertical" role="group">
                                        {% if case.has_mermaid %}
                                        <a href="{{ url_for('view_mermaid', ajbh=case.ajbh) }}" 
                                           class="btn btn-sm btn-primary mb-1" target="_blank"
                                           title="跳转到Mermaid Live Editor (使用streamlit_app.py相同方式)">
                                            <i class="bi bi-diagram-3"></i> 关系图
                                        </a>
                                        <a href="{{ url_for('mermaid_view', ajbh=case.ajbh) }}" 
                                           class="btn btn-sm btn-outline-primary mb-1" target="_blank"
                                           title="内嵌预览">
                                            <i class="bi bi-eye"></i> 预览
                                        </a>
                                        <a href="{{ url_for('mermaid_debug', ajbh=case.ajbh) }}" 
                                           class="btn btn-sm btn-outline-info" target="_blank"
                                           title="调试URL创建过程">
                                            <i class="bi bi-bug"></i> 调试
                                        </a>
                                        {% else %}
                                        <button class="btn btn-sm btn-secondary" disabled>
                                            <i class="bi bi-diagram-3"></i> 无代码
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="card mt-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> 功能说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>代码字段显示：</h6>
                        <ul>
                            <li><strong>关系图代码 (code)</strong>：原始AI生成的代码</li>
                            <li><strong>最终代码 (lastcode)</strong>：经过处理的最终代码</li>
                            <li>显示前50个字符，完整内容在悬停提示中</li>
                            <li>显示代码长度统计</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>关系图按钮功能：</h6>
                        <ul>
                            <li><strong>关系图</strong>：使用与streamlit_app.py相同的URL创建方式</li>
                            <li><strong>预览</strong>：在网页内嵌显示关系图</li>
                            <li><strong>调试</strong>：查看URL创建过程和测试不同方法</li>
                            <li>优先使用pako压缩，回退到base64编码</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        {% else %}
        <!-- 无代码案件 -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i> 未找到有代码的案件
                </h5>
            </div>
            <div class="card-body">
                <p>数据库中没有包含关系图代码的案件。</p>
                <p>请确保：</p>
                <ul>
                    <li>数据库中有案件数据</li>
                    <li>案件的 <code>code</code> 或 <code>lastcode</code> 字段不为空</li>
                    <li>AI处理已完成</li>
                </ul>
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="bi bi-list-ul"></i> 查看所有案件
                </a>
            </div>
        </div>
        {% endif %}

        <!-- 返回链接 -->
        <div class="mt-4 text-center">
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary me-2">
                <i class="bi bi-list-ul"></i> 案件列表
            </a>
            <a href="{{ url_for('field_check') }}" class="btn btn-outline-info">
                <i class="bi bi-check-circle"></i> 字段验证
            </a>
        </div>
    </div>
</div>
{% endblock %}
