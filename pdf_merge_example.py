#!/usr/bin/env python3
"""
PDF合并使用示例
演示如何使用PDF合并工具
"""

from simple_pdf_merger import quick_merge, batch_merge, merge_case_pdfs
import os

def example_usage():
    """使用示例"""
    
    print("📚 PDF合并工具使用示例")
    print("="*60)
    
    # 示例1: 合并特定案件
    print("\n1️⃣ 合并特定案件:")
    print("="*30)
    
    case_id = "A4401171601002021036001"
    print(f"合并案件: {case_id}")
    
    # 方法1: 使用便捷函数
    success = quick_merge(case_id)
    if success:
        print(f"✅ 案件 {case_id} 合并成功")
    else:
        print(f"❌ 案件 {case_id} 合并失败")
    
    # 示例2: 批量合并所有案件
    print("\n2️⃣ 批量合并所有案件:")
    print("="*30)
    
    results = batch_merge()
    print(f"📊 批量合并结果:")
    print(f"  ✅ 成功: {results['success']} 个")
    print(f"  ❌ 失败: {results['failed']} 个")
    
    # 示例3: 指定目录
    print("\n3️⃣ 指定目录合并:")
    print("="*30)
    
    pdf_directory = "./pdfs"  # 替换为你的PDF目录
    if os.path.exists(pdf_directory):
        print(f"在目录 {pdf_directory} 中合并PDF...")
        results = batch_merge(pdf_directory)
        print(f"结果: 成功 {results['success']} 个, 失败 {results['failed']} 个")
    else:
        print(f"目录 {pdf_directory} 不存在，跳过此示例")

def create_test_files():
    """创建测试PDF文件（仅用于演示）"""
    
    print("\n🧪 创建测试文件:")
    print("="*30)
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # 创建测试目录
        test_dir = "test_pdfs"
        os.makedirs(test_dir, exist_ok=True)
        
        case_id = "A4401171601002021036001"
        
        # 创建3个测试PDF文件
        for i in range(1, 4):
            filename = f"{test_dir}/{case_id}_{i:02d}.pdf"
            
            c = canvas.Canvas(filename, pagesize=letter)
            c.drawString(100, 750, f"测试PDF文件 - 第{i}部分")
            c.drawString(100, 700, f"案件编号: {case_id}")
            c.drawString(100, 650, f"文件: {case_id}_{i:02d}.pdf")
            c.showPage()
            c.save()
            
            print(f"  ✅ 创建: {filename}")
        
        print(f"\n📁 测试文件已创建在 {test_dir} 目录")
        print("现在可以运行合并测试:")
        print(f"  python simple_pdf_merger.py {case_id}")
        
    except ImportError:
        print("❌ 需要安装reportlab库来创建测试文件")
        print("安装命令: pip install reportlab")
        print("或者手动创建一些测试PDF文件")

def advanced_example():
    """高级使用示例"""
    
    print("\n🚀 高级使用示例:")
    print("="*30)
    
    # 示例: 自定义合并逻辑
    def custom_merge_with_validation(case_id, input_dir=".", output_dir=None):
        """带验证的自定义合并"""
        
        import glob
        from PyPDF2 import PdfReader, PdfWriter
        
        # 查找文件
        pattern = os.path.join(input_dir, f"{case_id}_*.pdf")
        files = glob.glob(pattern)
        
        if not files:
            print(f"❌ 没有找到案件 {case_id} 的文件")
            return False
        
        files.sort()
        print(f"🔍 找到 {len(files)} 个文件")
        
        # 验证文件
        total_pages = 0
        valid_files = []
        
        for file_path in files:
            try:
                reader = PdfReader(file_path)
                page_count = len(reader.pages)
                total_pages += page_count
                valid_files.append(file_path)
                print(f"  ✅ {os.path.basename(file_path)}: {page_count} 页")
            except Exception as e:
                print(f"  ❌ {os.path.basename(file_path)}: 无效 - {e}")
        
        if not valid_files:
            print("❌ 没有有效的PDF文件")
            return False
        
        # 执行合并
        if output_dir is None:
            output_dir = input_dir
        
        output_file = os.path.join(output_dir, f"{case_id}.pdf")
        
        try:
            writer = PdfWriter()
            
            for file_path in valid_files:
                reader = PdfReader(file_path)
                for page in reader.pages:
                    writer.add_page(page)
            
            with open(output_file, 'wb') as output:
                writer.write(output)
            
            print(f"✅ 合并完成: {output_file}")
            print(f"📊 总页数: {total_pages}")
            return True
            
        except Exception as e:
            print(f"❌ 合并失败: {e}")
            return False
    
    # 使用自定义函数
    case_id = "A4401171601002021036001"
    print(f"使用自定义合并函数处理案件: {case_id}")
    custom_merge_with_validation(case_id)

def main():
    """主函数"""
    
    print("🔧 PDF合并工具完整示例")
    print("="*60)
    
    # 基础使用示例
    example_usage()
    
    # 创建测试文件（可选）
    create_test = input("\n是否创建测试PDF文件? (y/N): ").strip().lower()
    if create_test == 'y':
        create_test_files()
    
    # 高级示例
    advanced_example()
    
    print(f"\n{'='*60}")
    print("📝 总结:")
    print("1. 使用 quick_merge(case_id) 合并单个案件")
    print("2. 使用 batch_merge() 批量合并所有案件")
    print("3. 文件命名格式: 案件编号_序号.pdf")
    print("4. 输出文件名: 案件编号.pdf")
    print("5. 支持自定义输入输出目录")

if __name__ == "__main__":
    main()
