<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}案件关系图查看器{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-start { background-color: #e3f2fd; color: #1976d2; }
        .status-download { background-color: #f3e5f5; color: #7b1fa2; }
        .status-ocr { background-color: #e8f5e8; color: #388e3c; }
        .status-complete { background-color: #e8f5e8; color: #2e7d32; }
        .status-error { background-color: #ffebee; color: #d32f2f; }
        .status-unknown { background-color: #f5f5f5; color: #616161; }
        
        .case-card {
            transition: transform 0.2s;
        }
        
        .case-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .mermaid-btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            transition: all 0.3s;
        }
        
        .mermaid-btn:hover {
            background: linear-gradient(45deg, #ff5252, #26a69a);
            transform: scale(1.05);
            color: white;
        }
        
        .mermaid-btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        
        .navbar-brand {
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            max-height: 80vh;
        }

        .compact-table {
            font-size: 12px;
            white-space: nowrap;
        }

        .compact-table th,
        .compact-table td {
            padding: 4px 6px !important;
            vertical-align: middle;
        }

        .compact-table .text-truncate {
            max-width: 80px;
        }

        .field-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 60px;
            height: 120px;
        }

        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .pagination .page-link {
            border-radius: 50px;
            margin: 0 2px;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s;
        }
        
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-diagram-3"></i> 案件关系图查看器
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="bi bi-list-ul"></i> 案件列表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('stats') }}">
                            <i class="bi bi-bar-chart"></i> 统计分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('field_check') }}">
                            <i class="bi bi-check-circle"></i> 字段验证
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('test_code_display') }}">
                            <i class="bi bi-code-slash"></i> 代码测试
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                <i class="bi bi-code-slash"></i> 案件关系图查看器 
                <span class="text-muted">| 基于Flask + MySQL + Mermaid</span>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
