#!/usr/bin/env python3
"""
系统启动脚本
提供友好的交互界面，帮助用户选择运行模式
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_controller import MainController
#from test_system import SystemTester


def print_banner():
    """打印系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                     案件处理系统 v1.0                        ║
║                                                              ║
║  基于AI的案件要素提取和关系图生成系统                         ║
║  支持PDF下载、OCR识别、要素提取、关系图生成                   ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def print_menu():
    """打印主菜单"""
    menu = """
请选择运行模式:

【临时处理模式】
1. 按时间范围处理案件
2. 按时间范围 + 指定案件处理
3. 按案件编号处理

【定时任务模式】
4. 每小时定时任务（处理上一小时数据）
5. 每12小时定时任务（13点和0点执行）
6. 每24小时定时任务（每天0点执行，处理前一天数据）
7. 每24小时定时任务（每天23:30执行，处理当天数据）

【系统管理】
8. 系统测试
9. 查看帮助
10. 退出

请输入选项编号 (1-10): """
    
    return input(menu).strip()


def get_time_input(prompt):
    """获取时间输入"""
    while True:
        time_str = input(prompt).strip()
        if not time_str:
            return None
        
        try:
            # 验证时间格式
            datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            return time_str
        except ValueError:
            print("❌ 时间格式错误，请使用格式: YYYY-MM-DD HH:MM:SS")
            print("   例如: 2025-01-03 09:00:00")


def get_ajbh_input():
    """获取案件编号输入"""
    ajbh = input("请输入案件编号: ").strip()
    if not ajbh:
        print("❌ 案件编号不能为空")
        return None
    return ajbh


async def handle_time_range_task():
    """处理时间范围任务"""
    print("\n=== 按时间范围处理案件 ===")
    
    # 提供快捷选项
    print("\n快捷选项:")
    print("1. 处理昨天的数据")
    print("2. 处理最近1小时的数据")
    print("3. 处理最近24小时的数据")
    print("4. 自定义时间范围")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    now = datetime.now()
    
    if choice == "1":
        # 昨天
        yesterday = now - timedelta(days=1)
        stime = yesterday.replace(hour=0, minute=0, second=0).strftime("%Y-%m-%d %H:%M:%S")
        etime = yesterday.replace(hour=23, minute=59, second=59).strftime("%Y-%m-%d %H:%M:%S")
    elif choice == "2":
        # 最近1小时
        stime = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")
        etime = now.strftime("%Y-%m-%d %H:%M:%S")
    elif choice == "3":
        # 最近24小时
        stime = (now - timedelta(hours=24)).strftime("%Y-%m-%d %H:%M:%S")
        etime = now.strftime("%Y-%m-%d %H:%M:%S")
    elif choice == "4":
        # 自定义
        stime = get_time_input("请输入开始时间 (YYYY-MM-DD HH:MM:SS): ")
        if not stime:
            return
        
        etime = get_time_input("请输入结束时间 (YYYY-MM-DD HH:MM:SS): ")
        if not etime:
            return
    else:
        print("❌ 无效选择")
        return
    
    print(f"\n处理时间范围: {stime} - {etime}")
    
    # 确认执行
    confirm = input("确认执行? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        return
    
    # 执行任务
    controller = MainController()
    print("\n开始执行任务...")
    result = await controller.run_time_range_task(stime, etime)
    
    if result["status"] == "success":
        print(f"✅ 任务执行成功")
        print(f"批次号: {result.get('batch_id', 'N/A')}")
    else:
        print(f"❌ 任务执行失败: {result.get('error', '未知错误')}")


async def handle_time_range_with_ajbh_task():
    """处理时间范围+案件编号任务"""
    print("\n=== 按时间范围 + 指定案件处理 ===")
    
    stime = get_time_input("请输入开始时间 (YYYY-MM-DD HH:MM:SS): ")
    if not stime:
        return
    
    etime = get_time_input("请输入结束时间 (YYYY-MM-DD HH:MM:SS): ")
    if not etime:
        return
    
    ajbh = get_ajbh_input()
    if not ajbh:
        return
    
    print(f"\n处理时间范围: {stime} - {etime}")
    print(f"指定案件: {ajbh}")
    
    # 确认执行
    confirm = input("确认执行? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        return
    
    # 执行任务
    controller = MainController()
    print("\n开始执行任务...")
    result = await controller.run_time_range_task(stime, etime, ajbh)
    
    if result["status"] == "success":
        print(f"✅ 任务执行成功")
        print(f"批次号: {result.get('batch_id', 'N/A')}")
    else:
        print(f"❌ 任务执行失败: {result.get('error', '未知错误')}")


async def handle_ajbh_task():
    """处理案件编号任务"""
    print("\n=== 按案件编号处理 ===")
    
    ajbh = get_ajbh_input()
    if not ajbh:
        return
    
    print(f"\n处理案件: {ajbh}")
    
    # 确认执行
    confirm = input("确认执行? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        return
    
    # 执行任务
    controller = MainController()
    print("\n开始执行任务...")
    result = await controller.run_ajbh_task(ajbh)
    
    if result["status"] == "success":
        print(f"✅ 任务执行成功")
        print(f"批次号: {result.get('batch_id', 'N/A')}")
    else:
        print(f"❌ 任务执行失败: {result.get('error', '未知错误')}")


def handle_scheduled_task(task_type):
    """处理定时任务"""
    task_names = {
        1: "每小时定时任务",
        2: "每12小时定时任务",
        3: "每24小时定时任务",
        4: "每24小时定时任务（处理当天数据）"
    }
    
    print(f"\n=== {task_names[task_type]} ===")
    
    descriptions = {
        1: "每小时执行一次，处理上一小时的数据",
        2: "每天13点和0点执行，分别处理0-12点和13-24点的数据",
        3: "每天0点执行，处理前一天的数据",
        4: "每天23:30执行，处理当天的数据"
    }
    
    print(f"任务说明: {descriptions[task_type]}")
    
    # 确认启动
    confirm = input("\n确认启动定时任务? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        return
    
    print(f"\n启动{task_names[task_type]}...")
    print("按 Ctrl+C 停止任务")
    
    # 启动定时任务
    os.system(f"python main_controller.py {task_type}")


#async def handle_system_test():
#    """处理系统测试"""
#    print("\n=== 系统测试 ===")
#    
#    tester = SystemTester()
#    await tester.run_all_tests()
#    
#    input("\n按回车键返回主菜单...")


def show_help():
    """显示帮助信息"""
    help_text = """
=== 系统帮助 ===

【系统概述】
本系统是基于AI的案件处理系统，支持从数据库自动获取案件数据，
进行PDF下载、OCR识别、AI要素提取和关系图生成的完整流程。

【处理流程】
1. 数据获取：从 ds_case_instrument_his 表筛选案件数据
2. PDF下载：批量下载法律文书到指定目录
3. OCR识别：使用MonkeyOCR进行文书内容识别
4. 要素提取：AI分析提取24个要素字段
5. 关系图生成：生成并验证Mermaid关系图代码
6. 数据入库：将结果保存到数据库表

【运行模式】
- 临时处理：立即执行指定条件的案件处理
- 定时任务：按设定时间间隔自动执行

【状态码说明】
- 0: 开始处理
- 1: PDF下载完成
- 2: OCR识别完成
- 3: AI分析完成
- 4: 处理出错

【注意事项】
1. 确保数据库连接正常
2. 确保MonkeyOCR Docker容器运行
3. 确保有足够的磁盘空间
4. 定期检查日志文件

【命令行使用】
也可以直接使用命令行：
- python main_controller.py 0 "2025-01-03 09:00:00" "2025-01-04 09:00:00"
- python main_controller.py 0 "A4401171601002021036001"
- python main_controller.py 1  # 每小时定时任务

"""
    
    print(help_text)
    input("\n按回车键返回主菜单...")


async def main():
    """主函数"""
    print_banner()
    
    while True:
        try:
            choice = print_menu()
            
            if choice == "1":
                await handle_time_range_task()
            elif choice == "2":
                await handle_time_range_with_ajbh_task()
            elif choice == "3":
                await handle_ajbh_task()
            elif choice == "4":
                handle_scheduled_task(1)
            elif choice == "5":
                handle_scheduled_task(2)
            elif choice == "6":
                handle_scheduled_task(3)
            elif choice == "7":
                handle_scheduled_task(4)
            #elif choice == "8":
            #    await handle_system_test()
            elif choice == "9":
                show_help()
            elif choice == "10":
                print("\n感谢使用案件处理系统！")
                break
            else:
                print("❌ 无效选择，请输入 1-10")
            
            if choice not in ["4", "5", "6", "9"]:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {e}")
            input("按回车键继续...")


if __name__ == "__main__":
    asyncio.run(main())
