# 每24小时定时任务（处理当天数据）说明

## 📋 功能概述

新增了 `task_type = 4` 的每24小时定时任务，专门用于处理当天的数据。

## 🆚 任务类型对比

| 任务类型 | 执行时间 | 处理数据范围 | 适用场景 |
|----------|----------|--------------|----------|
| `task_type = 3` | 每天00:00 | 前一天00:00-23:59 | 日终处理，处理完整的前一天数据 |
| `task_type = 4` | 每天23:30 | 当天00:00-23:59 | 当日汇总，处理当天累积的数据 |

## 🔧 实现细节

### 1. main_controller.py 修改

#### job() 函数新增逻辑
```python
elif task_type == 4:  # 每24小时
    # 处理当天的数据
    start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = now.replace(hour=23, minute=59, second=59, microsecond=999999)
```

#### 定时任务设置
```python
elif task_type == 4:  # 每24小时（处理当天数据）
    schedule.every().day.at("23:30").do(job)  # 每天23:30执行，处理当天数据
    print("每24小时定时任务已启动（处理当天数据）...")
```

#### 任务类型判断更新
```python
elif task_type in [1, 2, 3, 4]:  # 定时任务
```

### 2. start.py 菜单更新

#### 新增菜单选项
```
7. 每24小时定时任务（每天23:30执行，处理当天数据）
```

#### 选项处理
```python
elif choice == "7":
    handle_scheduled_task(4)
```

#### 任务描述
```python
task_names = {
    1: "每小时定时任务",
    2: "每12小时定时任务", 
    3: "每24小时定时任务",
    4: "每24小时定时任务（处理当天数据）"
}

descriptions = {
    1: "每小时执行一次，处理上一小时的数据",
    2: "每天13点和0点执行，分别处理0-12点和13-24点的数据",
    3: "每天0点执行，处理前一天的数据",
    4: "每天23:30执行，处理当天的数据"
}
```

## 🚀 使用方法

### 方法1: 交互式启动
```bash
python start.py
```
然后选择选项 `7`

### 方法2: 直接启动
```bash
python main_controller.py 4
```

### 方法3: 后台启动
```bash
python auto_start_daily.py
```

## 📊 执行示例

假设当前时间为 `2025-08-05 15:30:00`

### 立即执行（启动时）
- **处理时间范围**: `2025-08-05 00:00:00` - `2025-08-05 23:59:59`
- **说明**: 处理当天从00:00到当前时间的所有数据

### 定时执行（23:30）
- **执行时间**: `2025-08-05 23:30:00`
- **处理时间范围**: `2025-08-05 00:00:00` - `2025-08-05 23:59:59`
- **说明**: 处理当天完整的24小时数据

## 🎯 应用场景

### task_type = 3 适用场景
- **日终结算**: 需要处理完整的前一天数据
- **日报生成**: 基于前一天完整数据生成报告
- **数据归档**: 将前一天数据进行归档处理

### task_type = 4 适用场景
- **实时汇总**: 需要处理当天累积的数据
- **当日统计**: 生成当天的统计报告
- **准实时处理**: 在当天结束前处理当天数据

## 📝 日志示例

### 启动日志
```
🚀 自动启动每24小时定时任务（处理当天数据）
工作目录: /data/ai/AJagent-main
日志文件: logs/daily_task_20250805_153000.log
PID文件: logs/daily_task.pid
启动每24小时定时任务（处理当天数据）...
✅ 每24小时定时任务已启动（处理当天数据）
进程ID: 12345

📋 任务说明:
   执行时间: 每天23:30
   处理数据: 当天00:00-23:59的数据
   立即执行: 启动时立即处理一次当天数据
```

### 执行日志
```
2025-08-05 15:30:00,123 - __main__ - INFO - 定时任务开始执行，类型: 4, 时间范围: 2025-08-05 00:00:00 - 2025-08-05 23:59:59
2025-08-05 15:30:00,124 - __main__ - INFO - 开始时间范围任务，时间: 2025-08-05 00:00:00 - 2025-08-05 23:59:59, 案件: 全部
2025-08-05 15:30:00,130 - data_fetcher - INFO - 筛选到 15 条记录，时间范围: 2025-08-05 00:00:00 - 2025-08-05 23:59:59
```

## 🔧 管理命令

### 查看运行状态
```bash
ps aux | grep "main_controller.py 4"
```

### 查看日志
```bash
tail -f logs/daily_task_*.log
```

### 停止任务
```bash
# 如果知道进程ID
kill <PID>

# 或者查找并停止
pkill -f "main_controller.py 4"
```

### 重启任务
```bash
python auto_start_daily.py
```

## ⚠️ 注意事项

1. **时间重叠**: task_type=3 和 task_type=4 不要同时运行，避免数据处理冲突
2. **数据完整性**: task_type=4 在23:30执行时，当天数据可能还在增长
3. **资源使用**: 处理当天数据量可能较大，注意系统资源
4. **错误处理**: 如果23:30执行失败，需要手动重新执行

## 🎉 优势

- ✅ **及时处理**: 当天数据当天处理，减少延迟
- ✅ **灵活调度**: 可以根据业务需求选择不同的执行时间
- ✅ **数据新鲜**: 处理最新的数据，适合实时分析
- ✅ **独立运行**: 与其他定时任务独立，互不影响

现在你可以使用新的 `task_type = 4` 来处理当天的数据了！
