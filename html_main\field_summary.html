<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段显示验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>ds_case_relation 表字段显示验证</h2>
        
        <div class="alert alert-info">
            <h5>✅ 当前"案件关系数据"页面已包含所有18个数据字段</h5>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>表头字段映射 (共19列)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>序号</th>
                                <th>表头显示</th>
                                <th>数据库字段</th>
                                <th>字段说明</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>批次号</td>
                                <td>batchid</td>
                                <td>数据批次号</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>案件编号</td>
                                <td>ajbh</td>
                                <td>案件编号</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>案件名称</td>
                                <td>ajmc</td>
                                <td>案件名称</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>案件类型</td>
                                <td>ajlx</td>
                                <td>案件类型</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>入库时间</td>
                                <td>rksj</td>
                                <td>入库时间</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>同步时间</td>
                                <td>tbrksj</td>
                                <td>同步入库时间</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>文书地址</td>
                                <td>flwsxzdz</td>
                                <td>法律文书下载地址</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>案件内容</td>
                                <td>ajnr</td>
                                <td>案件内容</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>关系图代码</td>
                                <td>code</td>
                                <td>关系图代码</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>最终代码</td>
                                <td>lastcode</td>
                                <td>最终的关系图代码</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td>修改人员</td>
                                <td>updater</td>
                                <td>修改人员</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>修改类型</td>
                                <td>updatetype</td>
                                <td>是否人工修改</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td>状态</td>
                                <td>status</td>
                                <td>AI处理状态</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>开始时间</td>
                                <td>starttime</td>
                                <td>AI开始处理时间</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>完成时间</td>
                                <td>endtime</td>
                                <td>AI完成处理时间</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>16</td>
                                <td>更新时间</td>
                                <td>updatetime</td>
                                <td>更新时间</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>17</td>
                                <td>重跑次数</td>
                                <td>nums</td>
                                <td>AI重跑次数</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>18</td>
                                <td>错误信息</td>
                                <td>error</td>
                                <td>错误信息</td>
                                <td><span class="badge bg-success">✅ 已显示</span></td>
                            </tr>
                            <tr>
                                <td>19</td>
                                <td>操作</td>
                                <td>-</td>
                                <td>操作按钮</td>
                                <td><span class="badge bg-info">ℹ️ 功能列</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5>✅ 验证结果</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>数据库字段统计：</h6>
                        <ul>
                            <li>总字段数：18个</li>
                            <li>已显示字段：18个</li>
                            <li>缺失字段：0个</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>页面显示统计：</h6>
                        <ul>
                            <li>表头列数：19列</li>
                            <li>数据列数：18列</li>
                            <li>功能列数：1列（操作）</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6>🎉 所有字段验证通过！</h6>
                    <p class="mb-0">
                        "案件关系数据"页面已经完整显示了 <code>ds_case_relation</code> 表的所有18个字段。
                        每个字段都有对应的表头和数据显示。
                    </p>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5>🚀 启动验证</h5>
            </div>
            <div class="card-body">
                <p>运行以下命令启动Web应用并验证：</p>
                <pre class="bg-light p-3"><code>cd html_main
python start_web.py</code></pre>
                <p>然后访问 <a href="http://localhost:5000" target="_blank">http://localhost:5000</a> 查看"案件关系数据"页面。</p>
            </div>
        </div>
    </div>
</body>
</html>
