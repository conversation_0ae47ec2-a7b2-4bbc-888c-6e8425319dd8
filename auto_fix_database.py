#!/usr/bin/env python3
"""
自动修复数据库字段脚本
解决 "Unknown column 'llsj' in 'field list'" 错误
"""

import pymysql
import sys
from datetime import datetime

def get_connection():
    """获取数据库连接"""
    try:
        # 从config.py导入数据库配置
        sys.path.append('.')
        from config import DATABASE_CONFIG
        return pymysql.connect(**DATABASE_CONFIG)
    except ImportError:
        # 如果无法导入配置，使用默认配置
        db_config = {
            'host': 'localhost',
            'user': 'root', 
            'password': '123456',
            'database': 'djzs_db',
            'charset': 'utf8mb4'
        }
        return pymysql.connect(**db_config)

def check_column_exists(table_name, column_name):
    """检查字段是否存在"""
    try:
        with get_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute(f"SHOW COLUMNS FROM {table_name} LIKE '{column_name}'")
                return cursor.fetchone() is not None
    except Exception as e:
        print(f"❌ 检查字段失败: {e}")
        return False

def add_missing_column(table_name, column_name, column_definition):
    """添加缺失的字段"""
    try:
        if check_column_exists(table_name, column_name):
            print(f"✅ 字段已存在: {table_name}.{column_name}")
            return True
        
        with get_connection() as connection:
            with connection.cursor() as cursor:
                sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}"
                cursor.execute(sql)
                connection.commit()
                print(f"✅ 成功添加字段: {table_name}.{column_name}")
                return True
                
    except Exception as e:
        print(f"❌ 添加字段失败 {table_name}.{column_name}: {e}")
        return False

def fix_case_relation_table():
    """修复案件关系表"""
    print(f"\n🔧 修复案件关系表: ds_case_relation_graph")
    print("="*50)
    
    table_name = "ds_case_relation_graph"
    
    # 需要添加的字段
    missing_fields = [
        ("llsj", "DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间'"),
        ("is_deleted", "TINYINT(1) DEFAULT 0 COMMENT '是否删除'"),
        ("gxsj_update", "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'")
    ]
    
    success_count = 0
    for column_name, column_def in missing_fields:
        if add_missing_column(table_name, column_name, column_def):
            success_count += 1
    
    print(f"📊 案件关系表修复结果: {success_count}/{len(missing_fields)} 个字段处理成功")
    return success_count == len(missing_fields)

def fix_case_details_table():
    """修复案件详细信息表"""
    print(f"\n🔧 修复案件详细信息表: ds_case_details")
    print("="*50)
    
    table_name = "ds_case_details"
    
    # 需要添加的字段
    missing_fields = [
        ("llsj", "DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间'"),
        ("is_deleted", "TINYINT(1) DEFAULT 0 COMMENT '是否删除'"),
        ("gxsj", "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'")
    ]
    
    success_count = 0
    for column_name, column_def in missing_fields:
        if add_missing_column(table_name, column_name, column_def):
            success_count += 1
    
    print(f"📊 案件详细信息表修复结果: {success_count}/{len(missing_fields)} 个字段处理成功")
    return success_count == len(missing_fields)

def verify_fix():
    """验证修复结果"""
    print(f"\n🔍 验证修复结果")
    print("="*30)
    
    # 检查关键字段
    critical_fields = [
        ("ds_case_relation_graph", "llsj"),
        ("ds_case_relation_graph", "is_deleted"),
        ("ds_case_details", "llsj"),
        ("ds_case_details", "is_deleted")
    ]
    
    all_good = True
    for table_name, column_name in critical_fields:
        if check_column_exists(table_name, column_name):
            print(f"✅ {table_name}.{column_name}")
        else:
            print(f"❌ {table_name}.{column_name}")
            all_good = False
    
    return all_good

def test_insert_operation():
    """测试插入操作"""
    print(f"\n🧪 测试插入操作")
    print("="*30)
    
    try:
        with get_connection() as connection:
            with connection.cursor() as cursor:
                # 测试插入案件关系记录
                test_sql = """
                INSERT INTO ds_case_relation_graph 
                (ajbh, ajmc, ajlx, llsj, is_deleted) 
                VALUES ('TEST001', '测试案件', '测试类型', NOW(), 0)
                """
                cursor.execute(test_sql)
                
                # 立即删除测试记录
                cursor.execute("DELETE FROM ds_case_relation_graph WHERE ajbh = 'TEST001'")
                connection.commit()
                
                print("✅ 插入操作测试成功")
                return True
                
    except Exception as e:
        print(f"❌ 插入操作测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 数据库字段自动修复工具")
    print("="*60)
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 测试数据库连接
        print(f"\n🔍 测试数据库连接...")
        with get_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                print(f"✅ 数据库连接成功，MySQL版本: {version}")
        
        # 修复表结构
        relation_success = fix_case_relation_table()
        details_success = fix_case_details_table()
        
        # 验证修复结果
        verify_success = verify_fix()
        
        # 测试插入操作
        insert_success = test_insert_operation()
        
        # 汇总结果
        print(f"\n📋 修复结果汇总")
        print("="*30)
        print(f"案件关系表修复: {'✅ 成功' if relation_success else '❌ 失败'}")
        print(f"案件详细信息表修复: {'✅ 成功' if details_success else '❌ 失败'}")
        print(f"字段验证: {'✅ 通过' if verify_success else '❌ 失败'}")
        print(f"插入测试: {'✅ 通过' if insert_success else '❌ 失败'}")
        
        if all([relation_success, details_success, verify_success, insert_success]):
            print(f"\n🎉 数据库修复完成！现在可以正常运行主程序了")
            print(f"💡 建议运行: python main_controller.py 4")
            return True
        else:
            print(f"\n⚠️  部分修复失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
