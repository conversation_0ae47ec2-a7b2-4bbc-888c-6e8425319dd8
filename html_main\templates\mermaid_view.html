{% extends "base.html" %}

{% block title %}关系图 - {{ case.ajbh }} - 案件关系图查看器{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
<style>
    .mermaid-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        overflow: auto;
    }
    
    .code-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        border-left: 4px solid #007bff;
    }
    
    .action-buttons {
        position: sticky;
        top: 20px;
        z-index: 1000;
    }
    
    .mermaid svg {
        max-width: 100%;
        height: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 案件信息 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-diagram-3"></i> 案件关系图
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>案件编号：</strong>{{ case.ajbh }}</p>
                        <p><strong>案件名称：</strong>{{ case.ajmc or 'N/A' }}</p>
                    </div>
                    <div class="col-md-6">
                        <div class="action-buttons">
                            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> 返回列表
                            </a>
                            <button class="btn btn-primary" onclick="openInMermaidLive()">
                                <i class="bi bi-box-arrow-up-right"></i> 在Mermaid Live中打开
                            </button>
                            <button class="btn btn-success" onclick="downloadSVG()">
                                <i class="bi bi-download"></i> 下载SVG
                            </button>
                            <button class="btn btn-info" onclick="toggleCode()">
                                <i class="bi bi-code-slash"></i> 查看代码
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mermaid图表 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">关系图可视化</h6>
            </div>
            <div class="card-body">
                <div class="mermaid-container">
                    <div class="mermaid" id="mermaidDiagram">
                        {{ mermaid_code }}
                    </div>
                </div>
            </div>
        </div>

        <!-- 代码显示区域 -->
        <div class="card" id="codeSection" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">Mermaid代码</h6>
            </div>
            <div class="card-body">
                <div class="code-container">
                    <pre><code id="mermaidCode">{{ mermaid_code }}</code></pre>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="copyCode()">
                        <i class="bi bi-clipboard"></i> 复制代码
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 初始化Mermaid
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    securityLevel: 'loose',
    flowchart: {
        useMaxWidth: true,
        htmlLabels: true
    }
});

// 在Mermaid Live中打开
function openInMermaidLive() {
    const encodedCode = '{{ encoded_code }}';
    
    // 尝试本地Mermaid Live Editor
    const localUrl = 'http://localhost:8080/edit#' + encodedCode;
    
    // 先尝试本地版本
    const localWindow = window.open(localUrl, '_blank');
    
    // 如果本地版本无法打开，3秒后尝试在线版本
    setTimeout(() => {
        if (localWindow.closed) {
            const onlineUrl = 'https://mermaid.live/edit#' + encodedCode;
            window.open(onlineUrl, '_blank');
        }
    }, 3000);
}

// 下载SVG
function downloadSVG() {
    try {
        const svg = document.querySelector('#mermaidDiagram svg');
        if (svg) {
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const svgUrl = URL.createObjectURL(svgBlob);
            
            const downloadLink = document.createElement('a');
            downloadLink.href = svgUrl;
            downloadLink.download = '{{ case.ajbh }}_关系图.svg';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            URL.revokeObjectURL(svgUrl);
        } else {
            alert('无法找到SVG图表');
        }
    } catch (error) {
        console.error('下载SVG失败:', error);
        alert('下载失败，请重试');
    }
}

// 切换代码显示
function toggleCode() {
    const codeSection = document.getElementById('codeSection');
    const button = event.target;
    
    if (codeSection.style.display === 'none') {
        codeSection.style.display = 'block';
        button.innerHTML = '<i class="bi bi-eye-slash"></i> 隐藏代码';
        codeSection.scrollIntoView({ behavior: 'smooth' });
    } else {
        codeSection.style.display = 'none';
        button.innerHTML = '<i class="bi bi-code-slash"></i> 查看代码';
    }
}

// 复制代码
function copyCode() {
    const codeElement = document.getElementById('mermaidCode');
    const text = codeElement.textContent;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showToast('代码已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('代码已复制到剪贴板');
        } else {
            showToast('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    }
    
    document.body.removeChild(textArea);
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 错误处理
window.addEventListener('error', function(e) {
    if (e.message.includes('mermaid')) {
        console.error('Mermaid渲染错误:', e);
        document.getElementById('mermaidDiagram').innerHTML = 
            '<div class="alert alert-warning"><i class="bi bi-exclamation-triangle"></i> 图表渲染失败，请检查Mermaid代码语法</div>';
    }
});

// 页面加载完成后的处理
document.addEventListener('DOMContentLoaded', function() {
    // 检查图表是否渲染成功
    setTimeout(() => {
        const mermaidElement = document.getElementById('mermaidDiagram');
        const svg = mermaidElement.querySelector('svg');
        
        if (!svg) {
            mermaidElement.innerHTML = 
                '<div class="alert alert-warning"><i class="bi bi-exclamation-triangle"></i> 图表渲染失败，可能是代码语法错误</div>';
        }
    }, 2000);
});
</script>
{% endblock %}
