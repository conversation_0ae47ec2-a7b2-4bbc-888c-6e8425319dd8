# 🔍 关系图按钮重定向调试

## 问题描述
点击"关系图"按钮后，跳转到 `http://*************:9998/mermaid/A4401171601002021036001`，而不是直接跳转到 Mermaid Live Editor 的 `http://*************:8000/edit#base64:...`

## 根本原因分析

### 1. 按钮链接正确
- 按钮链接到 `{{ url_for('view_mermaid', ajbh=case.ajbh) }}`
- 这会生成 `/mermaid/<案件编号>` 的URL
- 这是**正确的设计**，应该先到我们的服务器处理

### 2. view_mermaid函数应该重定向
- `view_mermaid` 函数应该：
  1. 从数据库获取代码
  2. 生成Mermaid Live Editor URL
  3. **重定向**到生成的URL

### 3. 当前实现状态
- ✅ 数据库查询正确
- ✅ URL生成逻辑正确（与streamlit_app.py一致）
- ✅ 重定向代码存在
- ❓ 重定向可能没有正常工作

## 调试步骤

### 步骤1: 访问测试页面
访问: `http://localhost:5000/mermaid/A4401171601002021036001`

### 步骤2: 查看页面内容
页面应该显示：
- 案件编号
- 生成的目标URL
- 自动跳转倒计时
- 手动点击链接

### 步骤3: 验证URL格式
目标URL应该类似：
```
http://*************:8000/edit#base64:eyJjb2RlIjoi...
```

### 步骤4: 测试手动跳转
如果自动跳转不工作，点击页面上的链接手动跳转

## 可能的问题和解决方案

### 问题1: Flask重定向被阻止
**原因**: 浏览器可能阻止跨域重定向
**解决方案**: 使用JavaScript重定向（已实现）

### 问题2: URL过长
**原因**: 生成的URL可能过长导致浏览器拒绝
**解决方案**: 使用pako压缩（已实现）

### 问题3: 目标服务器不可访问
**原因**: `http://*************:8000` 可能无法访问
**解决方案**: 
- 检查服务器状态
- 使用在线版本作为备选

### 问题4: 网络配置
**原因**: 防火墙或网络配置阻止访问
**解决方案**: 检查网络连接和防火墙设置

## 测试结果

### 连接测试
- ✅ `http://*************:8000` 可访问
- ✅ `/edit` 页面可访问
- ✅ URL格式正确

### URL生成测试
- ✅ 基础URL生成正确
- ✅ 高级URL生成正确
- ✅ 双斜杠问题已修复

### 重定向测试
- 🔄 正在测试中...

## 当前解决方案

### 临时方案：显示跳转页面
```python
# 显示包含目标URL的页面，支持自动和手动跳转
return f"""
<html>
<head>
    <meta http-equiv="refresh" content="2;url={edit_url}">
</head>
<body>
    <h2>正在跳转到Mermaid Live Editor...</h2>
    <p>目标URL: <a href="{edit_url}" target="_blank">{edit_url}</a></p>
    <script>
        setTimeout(function() {{
            window.location.href = "{edit_url}";
        }}, 2000);
    </script>
</body>
</html>
"""
```

### 最终方案：直接重定向
```python
# 如果跳转页面工作正常，改回直接重定向
return redirect(edit_url)
```

## 验证清单

- [ ] 访问 `http://localhost:5000/mermaid/A4401171601002021036001`
- [ ] 确认页面显示目标URL
- [ ] 验证目标URL格式正确
- [ ] 测试自动跳转是否工作
- [ ] 测试手动点击链接是否工作
- [ ] 确认在Mermaid Live Editor中能看到正确的图表

## 下一步

1. **如果跳转页面正常工作**：改回使用 `return redirect(edit_url)`
2. **如果仍有问题**：检查网络连接和目标服务器状态
3. **如果目标服务器不可用**：添加在线版本作为备选方案

---

## 🎯 预期结果

点击"关系图"按钮后，应该：
1. 跳转到 `http://localhost:5000/mermaid/<案件编号>`
2. 服务器处理请求，生成Mermaid Live Editor URL
3. 自动重定向到 `http://*************:8000/edit#base64:...`
4. 在Mermaid Live Editor中显示正确的关系图
