#!/usr/bin/env python3
"""
单案件要素提取测试
"""

import asyncio
import sys
import os
import pymysql
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from case_extraction_agent import CaseExtractionAgent
from config import config


async def test_single_case():
    """测试单个案件的要素提取"""
    
    print("=" * 60)
    print("单案件要素提取测试")
    print("=" * 60)
    
    try:
        # 1. 获取一个STATUS=2的案件
        db_config = config.get_db_config()
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        table_config = config.get_table_config()
        case_relation_table = table_config['case_relation_table']
        
        # 查询包含所有必要字段
        query = f"""
        SELECT ajbh, batchid, ajnr, ajmc
        FROM `{case_relation_table}`
        WHERE status = '2'
        ORDER BY updatetime DESC
        LIMIT 1
        """
        
        cursor.execute(query)
        test_case = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if not test_case:
            print("❌ 没有找到STATUS=2的案件")
            return
        
        print(f"📋 测试案件:")
        print(f"  案件编号: {test_case['ajbh']}")
        print(f"  批次号: {test_case['batchid']}")
        print(f"  案件名称: {test_case.get('ajmc', '未知')}")
        print(f"  内容长度: {len(test_case['ajnr']) if test_case['ajnr'] else 0} 字符")
        
        # 2. 初始化智能体
        print(f"\n🤖 初始化要素提取智能体...")
        agent = CaseExtractionAgent()
        print(f"  ✅ 智能体初始化成功")
        
        # 3. 执行要素提取
        print(f"\n🚀 开始要素提取...")
        print(f"  开始时间: {datetime.now()}")
        
        start_time = datetime.now()
        
        # 确保case_info包含所有必要字段
        case_info = {
            'ajbh': test_case['ajbh'],
            'batchid': test_case['batchid'],
            'ajnr': test_case['ajnr'] or '',
            'ajmc': test_case.get('ajmc') or f"案件_{test_case['ajbh']}"
        }
        
        print(f"  案件信息准备完成")
        
        try:
            result = await agent.process_single_case_complete(case_info)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"\n📊 处理结果:")
            print(f"  处理时间: {duration:.1f} 秒")
            print(f"  状态: {result.get('status', 'unknown')}")
            
            if result.get("status") == "success":
                print(f"  ✅ 要素提取成功!")
                
                # 检查提取的数据
                extracted_data = result.get('extracted_data', {})
                if extracted_data:
                    print(f"  📝 提取的要素:")
                    for key, value in extracted_data.items():
                        if isinstance(value, str) and len(value) > 100:
                            print(f"    {key}: {value[:100]}...")
                        else:
                            print(f"    {key}: {value}")
                
                # 检查数据库状态
                connection = pymysql.connect(**db_config)
                cursor = connection.cursor()
                
                cursor.execute(f"SELECT status FROM `{case_relation_table}` WHERE ajbh = %s", (test_case['ajbh'],))
                current_status = cursor.fetchone()
                
                if current_status:
                    status_value = current_status[0]
                    print(f"  📊 数据库状态: STATUS={status_value}")
                    
                    if str(status_value) == '3':
                        print(f"  🎉 数据库状态更新成功! 案件已完成要素提取")
                    else:
                        print(f"  ⚠️  数据库状态未更新，仍为STATUS={status_value}")
                else:
                    print(f"  ❌ 无法查询数据库状态")
                
                cursor.close()
                connection.close()
                
            else:
                print(f"  ❌ 要素提取失败!")
                error_msg = result.get('error', '未知错误')
                print(f"  错误信息: {error_msg}")
                
                # 打印更详细的错误信息
                if 'traceback' in result:
                    print(f"  详细错误: {result['traceback']}")
            
            return result
            
        except Exception as e:
            print(f"  ❌ 执行异常: {e}")
            import traceback
            traceback.print_exc()
            return {"status": "error", "error": str(e)}
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}


async def main():
    """主函数"""
    print("分片批处理系统 - 单案件要素提取测试")
    print(f"运行时间: {datetime.now()}")
    
    result = await test_single_case()
    
    print(f"\n" + "=" * 60)
    
    if result and result.get("status") == "success":
        print("🎉 单案件测试成功!")
        print("💡 可以继续批量处理其他案件")
        print("🚀 运行命令: python batch_extract_all.py")
    else:
        print("❌ 单案件测试失败")
        if result:
            print(f"错误: {result.get('error', '未知错误')}")
        print("🔧 请检查系统配置和大模型连接")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
