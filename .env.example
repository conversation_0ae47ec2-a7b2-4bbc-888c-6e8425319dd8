# 案件处理系统环境配置示例
# 复制此文件为 .env 并修改相应配置

# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_DATABASE=djzs_db
DB_CHARSET=utf8mb4

# 大模型配置
MODEL_NAME=Qwen3-32B
MODEL_BASE_URL=http://*************:8011/v1
MODEL_API_KEY=jc888

# 系统配置
MAX_CONCURRENT=10
MAX_REPAIR_ATTEMPTS=3
DOWNLOAD_TIMEOUT=60

# OCR配置
#OCR_BASE_PATH=/bigai/ai/MonkeyOCR
OCR_BASE_PATH=/data/ai/MonkeyOCR
DOCKER_CONTAINER=monkeyocr

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

# 文件路径配置
PROMPT_FILE=案件要素提取关系图提示词.md

# Docker镜像配置
MERMAID_CLI_IMAGE=minlag/mermaid-cli

# 定时任务配置
ENABLE_HOURLY_TASK=true
ENABLE_DAILY_TASK=false
ENABLE_WEEKLY_TASK=false

# 监控配置
ENABLE_MONITORING=false
MONITORING_PORT=8080

# 备份配置
ENABLE_BACKUP=false
BACKUP_DIR=backups
BACKUP_RETENTION_DAYS=30
