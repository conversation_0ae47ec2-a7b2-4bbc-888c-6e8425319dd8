#!/usr/bin/env python3
"""
验证每24小时定时任务（处理当天数据）功能
"""

import os

def main():
    print("🔍 验证每24小时定时任务（处理当天数据）功能")
    print("="*60)
    
    # 检查main_controller.py
    if os.path.exists("main_controller.py"):
        with open("main_controller.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("task_type=4逻辑", "elif task_type == 4:"),
            ("处理当天数据", "处理当天的数据"),
            ("时间范围设置", "start_time = now.replace(hour=0"),
            ("定时任务包含", "task_type in [1, 2, 3, 4]"),
            ("23:30执行时间", "23:30")
        ]
        
        print("检查main_controller.py:")
        all_passed = True
        for name, pattern in checks:
            if pattern in content:
                print(f"   ✅ {name}: 找到")
            else:
                print(f"   ❌ {name}: 未找到")
                all_passed = False
    else:
        print("❌ main_controller.py 文件不存在")
        all_passed = False
    
    # 检查start.py
    if os.path.exists("start.py"):
        with open("start.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("菜单选项7", "7. 每24小时定时任务（每天23:30执行，处理当天数据）"),
            ("输入范围", "(1-10)"),
            ("选项处理", "elif choice == \"7\":"),
            ("任务类型4", "handle_scheduled_task(4)")
        ]
        
        print(f"\n检查start.py:")
        for name, pattern in checks:
            if pattern in content:
                print(f"   ✅ {name}: 找到")
            else:
                print(f"   ❌ {name}: 未找到")
                all_passed = False
    else:
        print("❌ start.py 文件不存在")
        all_passed = False
    
    # 检查自动启动脚本
    if os.path.exists("auto_start_daily.py"):
        print(f"\n✅ 自动启动脚本存在: auto_start_daily.py")
    else:
        print(f"\n❌ 自动启动脚本不存在: auto_start_daily.py")
        all_passed = False
    
    if all_passed:
        print(f"\n🎉 验证通过！task_type=4功能已正确实现")
        print(f"\n使用方法:")
        print(f"1. 交互式: python start.py (选择选项7)")
        print(f"2. 直接启动: python main_controller.py 4")
        print(f"3. 后台启动: python auto_start_daily.py")
        print(f"\n功能说明:")
        print(f"- 执行时间: 每天23:30")
        print(f"- 处理数据: 当天00:00-23:59")
        print(f"- 立即执行: 启动时立即处理一次")
    else:
        print(f"\n❌ 验证失败，请检查修改")

if __name__ == "__main__":
    main()
