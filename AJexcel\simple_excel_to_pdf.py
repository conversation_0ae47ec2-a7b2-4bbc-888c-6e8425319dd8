#!/usr/bin/env python3
"""
简化版Excel转PDF工具
解决Linux环境下的字体问题
"""

import pandas as pd
import os
import sys
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer

def clean_text(text):
    """清理文本，避免XML解析错误"""
    if not text:
        return ""
    
    text = str(text)
    # 替换可能导致XML解析错误的字符
    text = text.replace('&', '&amp;')
    text = text.replace('<', '&lt;')
    text = text.replace('>', '&gt;')
    # 处理换行符
    text = text.replace('\n', '<br/>')
    text = text.replace('\r', '')
    
    return text

def clean_filename(filename):
    """清理文件名中的非法字符"""
    illegal_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    for char in illegal_chars:
        filename = filename.replace(char, '_')
    return filename.strip()

def generate_pdf(row_data, pdf_path):
    """生成单个PDF文件"""
    try:
        # 创建PDF文档
        doc = SimpleDocTemplate(pdf_path, pagesize=A4)
        
        # 获取默认样式
        styles = getSampleStyleSheet()
        
        # 构建PDF内容
        story = []
        
        # 添加标题
        case_number = clean_text(str(row_data.get('案件编号', '')))
        version_number = clean_text(str(row_data.get('数据版本号', '')))
        
        title = f"Case: {case_number} | Version: {version_number}"
        story.append(Paragraph(title, styles['Title']))
        story.append(Spacer(1, 20))
        
        # 内容字段
        content_fields = ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明']
        
        # 字段映射（使用英文标题避免字体问题）
        field_mapping = {
            '正文内容': 'Main Content',
            '到案情况': 'Case Status',
            '依法侦查查明': 'Investigation',
            '犯罪证据': 'Evidence',
            '综上所述': 'Summary',
            '其他说明': 'Notes'
        }
        
        # 添加内容字段
        for field in content_fields:
            content = row_data.get(field, '')
            
            if pd.notna(content) and str(content).strip():
                # 添加章节标题
                section_title = field_mapping.get(field, field)
                story.append(Paragraph(f"<b>{section_title}</b>", styles['Heading2']))
                
                # 添加内容
                content_text = clean_text(content)
                # 限制内容长度避免过长
                if len(content_text) > 2000:
                    content_text = content_text[:2000] + "..."
                
                story.append(Paragraph(content_text, styles['Normal']))
                story.append(Spacer(1, 10))
        
        # 添加生成时间
        story.append(Spacer(1, 20))
        generate_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        story.append(Paragraph(f"Generated: {generate_time}", styles['Normal']))
        
        # 生成PDF
        doc.build(story)
        return True
        
    except Exception as e:
        print(f"生成PDF失败 {pdf_path}: {e}")
        return False

def main():
    """主函数"""
    
    print("📄 简化版Excel转PDF工具")
    print("="*40)
    
    # 获取参数
    if len(sys.argv) >= 3:
        excel_path = sys.argv[1]
        output_dir = sys.argv[2]
    else:
        print("用法: python simple_excel_to_pdf.py <excel_file> <output_dir>")
        return False
    
    # 检查Excel文件
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📁 Excel文件: {excel_path}")
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 读取Excel文件
        print("🔄 读取Excel文件...")
        df = pd.read_excel(excel_path)
        print(f"✅ 读取成功，共 {len(df)} 行数据")
        
        # 检查必需字段
        required_fields = ['案件编号', '数据版本号']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
            return False
        
        # 转换数据
        success_count = 0
        error_count = 0
        
        print("🔄 开始转换...")
        
        for index, row in df.iterrows():
            try:
                # 获取文件名
                case_number = clean_filename(str(row.get('案件编号', '')).strip())
                version_number = clean_filename(str(row.get('数据版本号', '')).strip())
                
                if not case_number or not version_number:
                    print(f"⚠️  第 {index+1} 行缺少案件编号或版本号，跳过")
                    error_count += 1
                    continue
                
                pdf_filename = f"{case_number}_{version_number}.pdf"
                pdf_path = os.path.join(output_dir, pdf_filename)
                
                # 生成PDF
                if generate_pdf(row, pdf_path):
                    success_count += 1
                    if success_count % 10 == 0:  # 每10个显示一次进度
                        print(f"✅ 已完成 {success_count} 个PDF")
                else:
                    error_count += 1
                    
            except Exception as e:
                print(f"❌ 处理第 {index+1} 行失败: {e}")
                error_count += 1
        
        print(f"\n🎉 转换完成！")
        print(f"✅ 成功: {success_count}")
        print(f"❌ 失败: {error_count}")
        print(f"📁 PDF文件保存在: {output_dir}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

if __name__ == "__main__":
    main()
