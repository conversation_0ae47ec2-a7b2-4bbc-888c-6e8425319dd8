#!/usr/bin/env python3
"""
Excel转PDF配置文件
可以在这里修改字段配置和其他设置
"""

# Excel字段配置
EXCEL_CONFIG = {
    # PDF文件名字段（用于生成PDF文件名）
    'pdf_name_fields': ['案件编号', '数据版本号'],
    
    # 内容字段（这些字段的内容会被拼接到PDF中）
    'content_fields': ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明'],
    
    # 可选：字段显示名称映射（如果Excel中的字段名与显示名称不同）
    'field_display_names': {
        '正文内容': '正文内容',
        '到案情况': '到案情况', 
        '依法侦查查明': '依法侦查查明',
        '犯罪证据': '犯罪证据',
        '综上所述': '综上所述',
        '其他说明': '其他说明'
    }
}

# PDF生成配置
PDF_CONFIG = {
    # 页面设置
    'page_size': 'A4',  # A4, LETTER, LEGAL
    'margins': {
        'top': 72,
        'bottom': 18,
        'left': 72,
        'right': 72
    },
    
    # 字体设置
    'fonts': {
        'title_size': 16,
        'section_size': 14,
        'body_size': 12
    },
    
    # 样式设置
    'styles': {
        'title_alignment': 1,  # 0=左对齐, 1=居中, 2=右对齐
        'add_generate_time': True,  # 是否添加生成时间
        'section_spacing': 15,  # 章节间距
        'paragraph_spacing': 12  # 段落间距
    }
}

# 文件处理配置
FILE_CONFIG = {
    # 支持的Excel文件格式
    'supported_formats': ['.xlsx', '.xls'],
    
    # 文件名清理规则
    'filename_replacements': {
        '<': '_', '>': '_', ':': '_', '"': '_',
        '/': '_', '\\': '_', '|': '_', '?': '_', '*': '_'
    },
    
    # 日志配置
    'log_file': 'excel_to_pdf.log',
    'log_level': 'INFO'  # DEBUG, INFO, WARNING, ERROR
}

# 默认路径配置
PATH_CONFIG = {
    'default_output_dir': './pdf_output',
    'log_dir': './logs'
}

def get_config():
    """获取完整配置"""
    return {
        'excel': EXCEL_CONFIG,
        'pdf': PDF_CONFIG,
        'file': FILE_CONFIG,
        'path': PATH_CONFIG
    }

def validate_config():
    """验证配置有效性"""
    errors = []
    
    # 检查必需字段
    if not EXCEL_CONFIG['pdf_name_fields']:
        errors.append("pdf_name_fields 不能为空")
    
    if not EXCEL_CONFIG['content_fields']:
        errors.append("content_fields 不能为空")
    
    # 检查字体大小
    if PDF_CONFIG['fonts']['title_size'] <= 0:
        errors.append("title_size 必须大于0")
    
    return errors

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
    else:
        print("✅ 配置验证通过")
        
    # 显示当前配置
    config = get_config()
    print(f"\n📋 当前配置:")
    print(f"PDF文件名字段: {config['excel']['pdf_name_fields']}")
    print(f"内容字段: {config['excel']['content_fields']}")
    print(f"默认输出目录: {config['path']['default_output_dir']}")
