#!/usr/bin/env python3
"""
检查要素提取状态脚本
"""

import sys
import os
import pymysql
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from case_extraction_agent import CaseExtractionAgent


def check_extraction_status():
    """检查要素提取状态"""
    print("=" * 80)
    print("要素提取状态检查")
    print("=" * 80)
    
    try:
        # 1. 检查数据库中待提取的案件
        db_config = config.get_db_config()
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        table_config = config.get_table_config()
        case_relation_table = table_config['case_relation_table']
        
        # 查询STATUS=2的案件（OCR完成，待要素提取）
        query = f"""
        SELECT batchid, ajbh, ajnr, updatetime
        FROM `{case_relation_table}`
        WHERE status = '2'
        ORDER BY batchid DESC, updatetime DESC
        LIMIT 10
        """
        
        cursor.execute(query)
        pending_cases = cursor.fetchall()
        
        print(f"1. 待要素提取案件 (STATUS=2):")
        print("-" * 50)
        
        if pending_cases:
            print(f"找到 {len(pending_cases)} 个待提取案件（显示前10个）:")
            
            batch_counts = {}
            for case in pending_cases:
                batchid = case['batchid']
                batch_counts[batchid] = batch_counts.get(batchid, 0) + 1
                
                print(f"  案件: {case['ajbh']}")
                print(f"    批次: {batchid}")
                print(f"    OCR完成时间: {case['updatetime']}")
                print(f"    内容长度: {len(case['ajnr']) if case['ajnr'] else 0} 字符")
                print()
            
            print(f"按批次统计:")
            for batchid, count in batch_counts.items():
                print(f"  批次 {batchid}: {count} 个案件")
                
        else:
            print("  ✅ 没有待提取案件，所有案件都已处理完成!")
        
        # 2. 检查已完成要素提取的案件
        completed_query = f"""
        SELECT batchid, COUNT(*) as count, MAX(updatetime) as last_completed
        FROM `{case_relation_table}`
        WHERE status = '3'
        GROUP BY batchid
        ORDER BY last_completed DESC
        LIMIT 5
        """
        
        cursor.execute(completed_query)
        completed_results = cursor.fetchall()
        
        print(f"\n2. 已完成要素提取案件 (STATUS=3):")
        print("-" * 50)
        
        if completed_results:
            for row in completed_results:
                print(f"  批次 {row['batchid']}: {row['count']} 个案件")
                print(f"    最后完成时间: {row['last_completed']}")
        else:
            print("  暂无已完成的要素提取案件")
        
        cursor.close()
        connection.close()
        
        # 3. 测试要素提取功能
        if pending_cases:
            print(f"\n3. 要素提取功能测试:")
            print("-" * 50)
            
            try:
                agent = CaseExtractionAgent()
                
                # 获取最新批次的待提取案件
                latest_batch = pending_cases[0]['batchid']
                extraction_cases = agent.get_cases_for_extraction(latest_batch)
                
                print(f"批次 {latest_batch} 待提取案件数: {len(extraction_cases)}")
                
                if extraction_cases:
                    print(f"示例案件信息:")
                    sample_case = extraction_cases[0]
                    print(f"  案件编号: {sample_case.get('ajbh', 'N/A')}")
                    print(f"  批次号: {sample_case.get('batchid', 'N/A')}")
                    print(f"  内容长度: {len(sample_case.get('ajnr', '')) if sample_case.get('ajnr') else 0} 字符")
                    
                    # 检查大模型连接
                    print(f"\n4. 大模型连接测试:")
                    print("-" * 50)
                    
                    try:
                        model_client = config.get_model_client()
                        print(f"  ✅ 大模型客户端初始化成功")
                        print(f"  模型: {model_client._model}")
                        print(f"  API地址: {model_client._base_url}")
                    except Exception as e:
                        print(f"  ❌ 大模型连接失败: {e}")
                
            except Exception as e:
                print(f"  ❌ 要素提取功能测试失败: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n" + "=" * 80)
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()


async def trigger_extraction_for_batch(batch_id):
    """手动触发指定批次的要素提取"""
    print(f"\n🚀 手动触发批次 {batch_id} 的要素提取...")
    
    try:
        agent = CaseExtractionAgent()
        
        # 获取待提取案件
        extraction_cases = agent.get_cases_for_extraction(batch_id)
        
        if not extraction_cases:
            print(f"  批次 {batch_id} 没有待提取案件")
            return
        
        print(f"  找到 {len(extraction_cases)} 个待提取案件")
        
        # 处理前几个案件作为测试
        test_cases = extraction_cases[:3]  # 只处理前3个作为测试
        
        print(f"  开始处理前 {len(test_cases)} 个案件...")
        
        for i, case_info in enumerate(test_cases, 1):
            ajbh = case_info['ajbh']
            print(f"  [{i}/{len(test_cases)}] 处理案件: {ajbh}")
            
            try:
                result = await agent.process_single_case_complete(case_info)
                
                if result.get("status") == "success":
                    print(f"    ✅ 成功")
                else:
                    print(f"    ❌ 失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"    ❌ 异常: {e}")
        
        print(f"  测试完成!")
        
    except Exception as e:
        print(f"  触发失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    check_extraction_status()
    
    # 询问是否手动触发要素提取
    print(f"\n💡 建议操作:")
    print(f"1. 如果要素提取没有自动进行，可能是因为:")
    print(f"   - 主控制器进程已结束")
    print(f"   - 大模型API连接问题")
    print(f"   - 系统资源不足")
    
    print(f"\n2. 可以手动触发要素提取:")
    print(f"   python -c \"")
    print(f"   import asyncio")
    print(f"   from check_extraction_status import trigger_extraction_for_batch")
    print(f"   asyncio.run(trigger_extraction_for_batch('2025081115490801'))\"")
    
    print(f"\n3. 或者重新运行完整流程:")
    print(f"   python main.py --mode time_range --stime '2025-08-11 15:00:00' --etime '2025-08-11 16:00:00'")


if __name__ == "__main__":
    main()
