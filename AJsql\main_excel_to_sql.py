#!/usr/bin/env python3
"""
Excel数据导入MySQL脚本
从指定Excel表中提取字段并插入到MySQL数据库
"""

import pandas as pd
import pymysql
import os
import sys
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_to_sql.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ExcelToSQLImporter:
    """Excel数据导入MySQL工具"""
    
    def __init__(self):
        """初始化导入器"""
        self.logger = logging.getLogger(__name__)
        
        # 数据库配置
        '''
        self.db_config = {
            'host': '*************',
            'user': 'root',
            'password': '123456',
            'database': 'djzs_db',
            'charset': 'utf8mb4'
        }
        '''
        self.db_config = { 
            'host': '************',
            'user': 'root',
            'password': 'root@Nexwise',
            'database': 'ds_prov_base_pro', 
            'charset': 'utf8mb4'
        }
 
        
        # 字段配置
        self.excel_fields = {
            'case_number': '案件编号',      # 对应 ajbh
            'version_number': '数据版本号',  # 对应 xxzjbh
            'content_fields': ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明']  # 拼接后对应 flwsnr
        }
        
        # PDF链接模板
        self.pdf_url_template = "http://*************:9999/api/v1/pdfHandler/getPdf?filename={}&action=view"
        
        # 目标表名
        self.target_table = 'ds_case_instrument_his_ai'
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def read_excel(self, excel_path):
        """读取Excel文件"""
        try:
            self.logger.info(f"开始读取Excel文件: {excel_path}")
            
            # 读取Excel文件
            df = pd.read_excel(excel_path)
            
            self.logger.info(f"成功读取Excel，共 {len(df)} 行数据")
            self.logger.info(f"Excel列名: {list(df.columns)}")
            
            # 检查必需字段是否存在
            required_fields = [self.excel_fields['case_number'], self.excel_fields['version_number']]
            required_fields.extend(self.excel_fields['content_fields'])
            
            missing_fields = []
            for field in required_fields:
                if field not in df.columns:
                    missing_fields.append(field)
            
            if missing_fields:
                self.logger.error(f"Excel中缺少必需字段: {missing_fields}")
                return None
            
            return df
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            return None
    
    def prepare_data(self, df):
        """准备插入数据"""
        try:
            self.logger.info("开始准备插入数据...")
            
            prepared_data = []
            current_time = datetime.now()
            tomorrow_time = current_time + timedelta(days=1)
            
            for index, row in df.iterrows():
                try:
                    # 获取案件编号和数据版本号
                    case_number = str(row.get(self.excel_fields['case_number'], '')).strip()
                    version_number = str(row.get(self.excel_fields['version_number'], '')).strip()
                    
                    if not case_number or not version_number:
                        self.logger.warning(f"第 {index+1} 行数据缺少案件编号或数据版本号，跳过")
                        continue
                    
                    # 拼接内容字段
                    content_parts = []
                    for field in self.excel_fields['content_fields']:
                        content = row.get(field, '')
                        if pd.notna(content) and str(content).strip():
                            content_parts.append(f"{field}: {str(content).strip()}")
                    
                    flwsnr = '\n\n'.join(content_parts) if content_parts else ''
                    
                    # 生成PDF文件名和链接
                    pdf_filename = f"{case_number}_{version_number}.pdf"
                    flwslldz = self.pdf_url_template.format(pdf_filename)
                    flwsxzdz = flwslldz  # 两个字段使用相同的链接
                    
                    # 准备插入数据
                    record = {
                        'ajbh': case_number,                    # 案件编号
                        'xxzjbh': version_number,               # 数据版本号
                        'flwsnr': flwsnr,                       # 法律文书内容（拼接后的内容）
                        'flwslldz': flwslldz,                   # 法律文书链路地址
                        'flwsxzdz': flwsxzdz,                   # 法律文书下载地址
                        'ajlx': 'AUTO_IMPORT',                  # 案件类型（当前时间相关标识）
                        'tbrksj': current_time,                 # 同步入库时间（当前时间）
                        'tbgxsj': tomorrow_time,                # 同步更新时间（明天时间）
                        'llsj': current_time,                   # 录入时间
                        'flwszldm': '02060403',                 # 法律文书种类代码（默认值）
                        'ajmc': f"案件_{case_number}",          # 案件名称
                    }
                    
                    prepared_data.append(record)
                    
                except Exception as e:
                    self.logger.error(f"处理第 {index+1} 行数据失败: {e}")
                    continue
            
            self.logger.info(f"数据准备完成，共 {len(prepared_data)} 条有效记录")
            return prepared_data
            
        except Exception as e:
            self.logger.error(f"数据准备失败: {e}")
            return []
    
    def insert_data(self, data_list):
        """批量插入数据到数据库"""
        if not data_list:
            self.logger.warning("没有数据需要插入")
            return False
        
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    
                    # 构建插入SQL语句
                    insert_sql = f"""
                    INSERT INTO `{self.target_table}` 
                    (ajbh, xxzjbh, flwsnr, flwslldz, flwsxzdz, ajlx, tbrksj, tbgxsj, llsj, flwszldm, ajmc)
                    VALUES (%(ajbh)s, %(xxzjbh)s, %(flwsnr)s, %(flwslldz)s, %(flwsxzdz)s, 
                            %(ajlx)s, %(tbrksj)s, %(tbgxsj)s, %(llsj)s, %(flwszldm)s, %(ajmc)s)
                    ON DUPLICATE KEY UPDATE
                    flwsnr = VALUES(flwsnr),
                    flwslldz = VALUES(flwslldz),
                    flwsxzdz = VALUES(flwsxzdz),
                    tbgxsj = VALUES(tbgxsj),
                    llsj = VALUES(llsj)
                    """
                    
                    # 批量插入
                    success_count = 0
                    error_count = 0
                    
                    for i, record in enumerate(data_list):
                        try:
                            cursor.execute(insert_sql, record)
                            success_count += 1
                            
                            # 每100条提交一次
                            if (i + 1) % 100 == 0:
                                connection.commit()
                                self.logger.info(f"已插入 {i + 1} 条记录")
                                
                        except Exception as e:
                            self.logger.error(f"插入第 {i+1} 条记录失败: {e}")
                            self.logger.error(f"失败记录: {record}")
                            error_count += 1
                            continue
                    
                    # 最终提交
                    connection.commit()
                    
                    self.logger.info(f"数据插入完成！成功: {success_count}, 失败: {error_count}")
                    return success_count > 0
                    
        except Exception as e:
            self.logger.error(f"数据库插入失败: {e}")
            return False
    
    def check_table_structure(self):
        """检查目标表结构"""
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    cursor.execute(f"DESCRIBE `{self.target_table}`")
                    columns = cursor.fetchall()
                    
                    self.logger.info(f"目标表 {self.target_table} 结构:")
                    for column in columns:
                        self.logger.info(f"  {column}")
                    
                    return True
                    
        except Exception as e:
            self.logger.error(f"检查表结构失败: {e}")
            return False
    
    def import_excel_to_sql(self, excel_path):
        """执行完整的导入流程"""
        try:
            self.logger.info("开始Excel到SQL的导入流程")
            
            # 检查Excel文件
            if not os.path.exists(excel_path):
                self.logger.error(f"Excel文件不存在: {excel_path}")
                return False
            
            # 检查表结构
            if not self.check_table_structure():
                return False
            
            # 读取Excel数据
            df = self.read_excel(excel_path)
            if df is None:
                return False
            
            # 准备数据
            prepared_data = self.prepare_data(df)
            if not prepared_data:
                self.logger.error("没有有效数据可以插入")
                return False
            
            # 插入数据
            success = self.insert_data(prepared_data)
            
            if success:
                self.logger.info("🎉 Excel数据导入MySQL完成！")
            else:
                self.logger.error("❌ Excel数据导入失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"导入流程失败: {e}")
            return False

def main():
    """主函数"""
    
    print("📊 Excel数据导入MySQL工具")
    print("="*50)
    
    # 获取Excel文件路径
    if len(sys.argv) >= 2:
        excel_path = sys.argv[1]
    else:
        excel_path = input("请输入Excel文件路径: ").strip()
    
    if not excel_path:
        print("❌ 请提供Excel文件路径")
        return False
    
    print(f"📁 Excel文件: {excel_path}")
    print(f"🎯 目标数据库: djzs_db.ds_case_instrument_his_ai")
    
    # 创建导入器并执行导入
    importer = ExcelToSQLImporter()
    
    print(f"\n🔄 开始导入...")
    success = importer.import_excel_to_sql(excel_path)
    
    if success:
        print(f"\n🎉 导入完成！数据已成功插入到数据库")
        print(f"📝 详细日志请查看: excel_to_sql.log")
    else:
        print(f"\n❌ 导入失败，请检查日志文件")
    
    return success

if __name__ == "__main__":
    main()
