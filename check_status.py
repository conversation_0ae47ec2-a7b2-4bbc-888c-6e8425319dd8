#!/usr/bin/env python3
"""
检查后台任务状态
"""

import os
import subprocess
import time
from datetime import datetime

def check_process_status():
    """检查进程状态"""
    print("🔍 检查进程状态")
    print("="*50)
    
    # 检查PID文件
    pid_files = [
        "logs/hourly_task.pid",
        "logs/system.pid"
    ]
    
    for pid_file in pid_files:
        if os.path.exists(pid_file):
            try:
                with open(pid_file, 'r') as f:
                    pid = f.read().strip()
                
                print(f"📄 找到PID文件: {pid_file}")
                print(f"🆔 进程ID: {pid}")
                
                # 检查进程是否运行
                try:
                    result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
                    if pid in result.stdout:
                        print(f"✅ 进程 {pid} 正在运行")
                        
                        # 显示进程详情
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if pid in line and 'python' in line:
                                print(f"📋 进程详情: {line}")
                                break
                    else:
                        print(f"❌ 进程 {pid} 未运行")
                        
                except Exception as e:
                    print(f"⚠️  检查进程失败: {e}")
                    
            except Exception as e:
                print(f"❌ 读取PID文件失败: {e}")
        else:
            print(f"⚠️  PID文件不存在: {pid_file}")
    
    # 查找所有相关Python进程
    print(f"\n🔍 查找所有相关Python进程:")
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        found_processes = []
        
        for line in lines:
            if 'python' in line and ('start.py' in line or 'main_controller.py' in line):
                found_processes.append(line)
                print(f"🐍 {line}")
        
        if not found_processes:
            print("❌ 未找到相关Python进程")
            
    except Exception as e:
        print(f"❌ 查找进程失败: {e}")

def check_log_files():
    """检查日志文件"""
    print(f"\n📝 检查日志文件")
    print("="*50)
    
    # 检查可能的日志目录
    log_dirs = [
        "logs",
        "/bigai/ai/AJagent-main/logs",
        "/data/ai/AJagent-main/logs"
    ]
    
    for log_dir in log_dirs:
        if os.path.exists(log_dir):
            print(f"📁 日志目录: {log_dir}")
            try:
                files = os.listdir(log_dir)
                if files:
                    print(f"📄 日志文件:")
                    for file in sorted(files):
                        if file.endswith('.log') or file.endswith('.pid'):
                            file_path = os.path.join(log_dir, file)
                            size = os.path.getsize(file_path)
                            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                            print(f"   {file} ({size} bytes, {mtime.strftime('%Y-%m-%d %H:%M:%S')})")
                else:
                    print(f"   (空目录)")
            except Exception as e:
                print(f"   ❌ 读取目录失败: {e}")
        else:
            print(f"⚠️  日志目录不存在: {log_dir}")

def show_recent_logs():
    """显示最近的日志"""
    print(f"\n📖 显示最近的日志")
    print("="*50)
    
    # 查找最新的日志文件
    log_patterns = [
        "logs/hourly_task_*.log",
        "logs/system_*.log",
        "/bigai/ai/AJagent-main/logs/hourly_task_*.log"
    ]
    
    import glob
    
    latest_log = None
    latest_time = 0
    
    for pattern in log_patterns:
        try:
            files = glob.glob(pattern)
            for file in files:
                mtime = os.path.getmtime(file)
                if mtime > latest_time:
                    latest_time = mtime
                    latest_log = file
        except:
            continue
    
    if latest_log:
        print(f"📄 最新日志文件: {latest_log}")
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"📊 日志行数: {len(lines)}")
                
                # 显示最后20行
                print(f"📖 最后20行:")
                for line in lines[-20:]:
                    print(f"   {line.rstrip()}")
                    
        except Exception as e:
            print(f"❌ 读取日志失败: {e}")
    else:
        print("⚠️  未找到日志文件")

def show_management_commands():
    """显示管理命令"""
    print(f"\n🔧 管理命令")
    print("="*50)
    
    print("查看进程:")
    print("   ps aux | grep python")
    print("   ps aux | grep 65408")
    
    print(f"\n停止任务:")
    print("   kill 65408")
    print("   pkill -f main_controller.py")
    
    print(f"\n查看日志:")
    print("   tail -f logs/hourly_task_*.log")
    print("   tail -f /bigai/ai/AJagent-main/logs/hourly_task_*.log")
    
    print(f"\n重启任务:")
    print("   python auto_start_hourly.py")

def main():
    """主函数"""
    print("📊 后台任务状态检查")
    print("="*60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查进程状态
    check_process_status()
    
    # 检查日志文件
    check_log_files()
    
    # 显示最近日志
    show_recent_logs()
    
    # 显示管理命令
    show_management_commands()
    
    print(f"\n{'='*60}")
    print("检查完成")

if __name__ == "__main__":
    main()
