@echo off
chcp 65001 >nul
echo ========================================
echo Excel转PDF工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查必要的Python包
echo 🔍 检查依赖包...
python -c "import pandas, reportlab" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要的依赖包，正在安装...
    pip install pandas openpyxl reportlab
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
)

REM 检查主脚本是否存在
if not exist "main_excel_to_pdf.py" (
    echo ❌ 错误: 未找到 main_excel_to_pdf.py 文件
    pause
    exit /b 1
)

echo.
echo 📋 使用说明:
echo 1. 请准备好Excel文件，确保包含以下字段：
echo    - 案件编号
echo    - 数据版本号  
echo    - 正文内容
echo    - 到案情况
echo    - 依法侦查查明
echo    - 犯罪证据
echo    - 综上所述
echo    - 其他说明
echo.
echo 2. 生成的PDF文件将以"案件编号_数据版本号.pdf"命名
echo.

REM 获取Excel文件路径
set /p excel_path="请输入Excel文件路径（拖拽文件到此窗口）: "

REM 去除路径两端的引号
set excel_path=%excel_path:"=%

REM 检查Excel文件是否存在
if not exist "%excel_path%" (
    echo ❌ 错误: Excel文件不存在: %excel_path%
    pause
    exit /b 1
)

REM 获取输出目录
set /p output_dir="请输入PDF输出目录（默认: ./pdf_output）: "

REM 如果没有输入输出目录，使用默认值
if "%output_dir%"=="" set output_dir=./pdf_output

echo.
echo 🔄 开始转换...
echo Excel文件: %excel_path%
echo 输出目录: %output_dir%
echo.

REM 执行转换
python main_excel_to_pdf.py "%excel_path%" "%output_dir%"

if errorlevel 1 (
    echo.
    echo ❌ 转换失败，请检查：
    echo 1. Excel文件格式是否正确
    echo 2. 是否包含所有必需字段
    echo 3. 查看日志文件 excel_to_pdf.log
) else (
    echo.
    echo 🎉 转换完成！
    echo 📁 PDF文件已保存到: %output_dir%
    
    REM 询问是否打开输出目录
    set /p open_dir="是否打开输出目录? (y/N): "
    if /i "%open_dir%"=="y" (
        explorer "%output_dir%"
    )
)

echo.
pause
