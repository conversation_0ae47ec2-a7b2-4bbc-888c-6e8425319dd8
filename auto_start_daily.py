#!/usr/bin/env python3
"""
自动启动每24小时定时任务（处理当天数据）
用于后台运行，自动选择每24小时定时任务选项（task_type=4）
"""

import sys
import os
import time
import subprocess
from datetime import datetime

def auto_start_daily_task():
    """自动启动每24小时定时任务（处理当天数据）"""
    
    print("🚀 自动启动每24小时定时任务（处理当天数据）")
    print("="*60)
    
    # 设置工作目录
    os.chdir('/data/ai/AJagent-main')
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"logs/daily_task_{timestamp}.log"
    pid_file = "logs/daily_task.pid"
    
    print(f"工作目录: {os.getcwd()}")
    print(f"日志文件: {log_file}")
    print(f"PID文件: {pid_file}")
    
    try:
        # 直接调用main_controller.py的每24小时定时任务（task_type=4）
        print("启动每24小时定时任务（处理当天数据）...")
        
        # 使用subprocess启动后台进程
        with open(log_file, 'w') as f:
            process = subprocess.Popen(
                [sys.executable, 'main_controller.py', '4'],
                stdout=f,
                stderr=subprocess.STDOUT,
                cwd='/data/ai/AJagent-main'
            )
        
        # 保存进程ID
        with open(pid_file, 'w') as f:
            f.write(str(process.pid))
        
        print(f"✅ 每24小时定时任务已启动（处理当天数据）")
        print(f"进程ID: {process.pid}")
        print(f"日志文件: {log_file}")
        
        print(f"\n📋 任务说明:")
        print(f"   执行时间: 每天23:30")
        print(f"   处理数据: 当天00:00-23:59的数据")
        print(f"   立即执行: 启动时立即处理一次当天数据")
        
        print(f"\n📋 管理命令:")
        print(f"查看日志: tail -f {log_file}")
        print(f"查看进程: ps aux | grep {process.pid}")
        print(f"停止任务: kill {process.pid}")
        
        return process.pid
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def main():
    """主函数"""
    
    # 检查是否已有任务在运行
    pid_file = "logs/daily_task.pid"
    if os.path.exists(pid_file):
        with open(pid_file, 'r') as f:
            old_pid = f.read().strip()
        
        # 检查进程是否还在运行
        try:
            os.kill(int(old_pid), 0)  # 检查进程是否存在
            print(f"⚠️  检测到已有每24小时任务在运行 (PID: {old_pid})")
            choice = input("是否停止现有任务并启动新任务? (y/N): ").strip().lower()
            if choice == 'y':
                os.kill(int(old_pid), 15)  # 发送SIGTERM信号
                time.sleep(3)
                os.remove(pid_file)
                print("✅ 已停止现有任务")
            else:
                print("已取消")
                return
        except (OSError, ValueError):
            # 进程不存在，清理PID文件
            os.remove(pid_file)
    
    # 启动新任务
    pid = auto_start_daily_task()
    
    if pid:
        print(f"\n🎉 每24小时定时任务已成功启动！")
        print(f"系统将立即执行一次任务（处理当天数据），然后每天23:30执行。")
        print(f"使用 Ctrl+C 或 kill {pid} 停止任务。")
        
        print(f"\n📊 执行计划:")
        now = datetime.now()
        print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"立即执行: 处理 {now.strftime('%Y-%m-%d')} 00:00:00 - 23:59:59 的数据")
        print(f"下次执行: {now.strftime('%Y-%m-%d')} 23:30:00")
        
    else:
        print(f"\n❌ 启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
