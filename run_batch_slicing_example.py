#!/usr/bin/env python3
"""
分片批处理系统使用示例
演示如何使用新的分片批处理功能
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_controller import MainController
from config import config


async def run_batch_slicing_example():
    """运行分片批处理示例"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    print("=" * 80)
    print("分片批处理系统使用示例")
    print("=" * 80)
    
    try:
        # 初始化主控制器
        controller = MainController()
        logger.info("主控制器初始化成功")
        
        # 示例1: 处理指定时间范围的案件（使用分片批处理）
        print(f"\n示例1: 时间范围任务（分片批处理）")
        print("-" * 50)
        
        # 设置时间范围（最近7天）
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        
        stime = start_time.strftime('%Y-%m-%d %H:%M:%S')
        etime = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"时间范围: {stime} 到 {etime}")
        print(f"分片批大小: {config.get_system_config()['batch_slice_size']}")
        
        # 注意：这里只是演示调用方式，实际运行需要数据库连接
        print(f"\n调用方式:")
        print(f"result = await controller.run_time_range_task('{stime}', '{etime}')")
        
        # 如果要实际运行，取消下面的注释
        # result = await controller.run_time_range_task(stime, etime)
        # print(f"处理结果: {result}")
        
        # 示例2: 处理特定案件编号（使用分片批处理）
        print(f"\n示例2: 案件编号任务（分片批处理）")
        print("-" * 50)
        
        ajbh = "TEST123456"
        print(f"案件编号: {ajbh}")
        
        print(f"\n调用方式:")
        print(f"result = await controller.run_ajbh_task('{ajbh}')")
        
        # 如果要实际运行，取消下面的注释
        # result = await controller.run_ajbh_task(ajbh)
        # print(f"处理结果: {result}")
        
        # 示例3: 直接使用分片批处理方法
        print(f"\n示例3: 直接分片批处理")
        print("-" * 50)
        
        # 模拟案件数据
        mock_cases = []
        for i in range(1, 24):  # 23个案件
            case = {
                'ajbh': f'TEST{i:06d}',
                'wsmc': f'测试文书{i}',
                'wslj': f'http://example.com/doc{i}.pdf'
            }
            mock_cases.append(case)
        
        batch_id = f"example_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        print(f"批次号: {batch_id}")
        print(f"案件数量: {len(mock_cases)}")
        print(f"预期分片批数: {(len(mock_cases) + config.get_system_config()['batch_slice_size'] - 1) // config.get_system_config()['batch_slice_size']}")
        
        print(f"\n调用方式:")
        print(f"result = await controller.process_batch_with_slicing('{batch_id}', mock_cases)")
        
        # 如果要实际运行，取消下面的注释
        # result = await controller.process_batch_with_slicing(batch_id, mock_cases)
        # print(f"处理结果: {result}")
        
        print(f"\n✅ 示例演示完成")
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


def show_configuration_info():
    """显示配置信息"""
    print("\n" + "=" * 80)
    print("当前配置信息")
    print("=" * 80)
    
    system_config = config.get_system_config()
    
    print(f"分片批处理配置:")
    print(f"  分片批大小: {system_config['batch_slice_size']} 个PDF/片批")
    print(f"  OCR基础路径: {system_config['ocr_base_path']}")
    print(f"  Docker容器名: {system_config['docker_container']}")
    print(f"  最大并发数: {system_config['max_concurrent']}")
    print(f"  下载超时时间: {system_config['download_timeout']} 秒")
    
    print(f"\n数据库配置:")
    db_config = config.get_db_config()
    print(f"  主机: {db_config['host']}")
    print(f"  数据库: {db_config['database']}")
    print(f"  用户: {db_config['user']}")
    
    print(f"\n表名配置:")
    table_config = config.get_table_config()
    print(f"  案件关系表: {table_config['case_relation_table']}")
    print(f"  案件详情表: {table_config['case_details_table']}")
    print(f"  源数据表: {table_config['source_table']}")


def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 80)
    print("使用说明")
    print("=" * 80)
    
    print(f"1. 配置调整:")
    print(f"   在 config.py 中修改 'batch_slice_size' 来调整分片批大小")
    print(f"   建议值: 10-20 (稳定性优先), 30-50 (性能优先)")
    
    print(f"\n2. 运行方式:")
    print(f"   # 时间范围任务")
    print(f"   python main.py --mode time_range --stime '2025-08-01 00:00:00' --etime '2025-08-11 23:59:59'")
    print(f"   ")
    print(f"   # 案件编号任务")
    print(f"   python main.py --mode ajbh --ajbh 'CASE123456'")
    
    print(f"\n3. 监控方式:")
    print(f"   # 查看处理日志")
    print(f"   tail -f logs/main_controller_*.log")
    print(f"   ")
    print(f"   # 查看数据库状态")
    print(f"   SELECT batchid, status, COUNT(*) as count FROM ds_case_relation GROUP BY batchid, status;")
    
    print(f"\n4. 故障恢复:")
    print(f"   如果某个片批处理失败，可以手动重新处理:")
    print(f"   python -c \"")
    print(f"   from ocr_processor import OCRProcessor")
    print(f"   processor = OCRProcessor()")
    print(f"   result = processor.process_batch_slice('batch_id', ppid, ajbh_list)")
    print(f"   print(result)")
    print(f"   \"")


def main():
    """主函数"""
    print("分片批处理系统使用示例")
    print(f"运行时间: {datetime.now()}")
    
    # 显示配置信息
    show_configuration_info()
    
    # 显示使用说明
    show_usage_instructions()
    
    # 运行示例
    print(f"\n是否运行示例代码？(实际运行需要数据库连接)")
    print(f"当前只演示调用方式，不会实际执行")
    
    try:
        asyncio.run(run_batch_slicing_example())
    except KeyboardInterrupt:
        print(f"\n用户中断")
    except Exception as e:
        print(f"\n运行失败: {e}")


if __name__ == "__main__":
    main()
