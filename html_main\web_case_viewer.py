#!/usr/bin/env python3
"""
案件关系图查看器 - Web版本
基于Flask的网页应用，用于查看案件关系数据和Mermaid关系图
"""

from flask import Flask, render_template, request, redirect, url_for, jsonify
import pymysql
import base64
import urllib.parse
from datetime import datetime
import logging
import json
import zlib
import os
import sys

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import config

app = Flask(__name__)

# 数据库配置
DB_CONFIG = config.get_db_config()

# 表名配置
TABLE_CONFIG = config.get_table_config()

def get_table_name(table_key):
    """获取表名"""
    return TABLE_CONFIG.get(table_key, table_key)

# Mermaid Live Editor配置 - 与streamlit_app.py保持完全一致
#MERMAID_LIVE_EDITOR_URL = "http://***********:8008"  # 本地Mermaid Live Editor地址
MERMAID_LIVE_EDITOR_URL = "http://*************:8000"  # 本地Mermaid Live Editor地址
def create_mermaid_live_editor_url(mermaid_code: str, base_url: str = "http://*************:8000/") -> str:
#def create_mermaid_live_editor_url(mermaid_code: str, base_url: str = "http://***********:8008/") -> str:
    """创建Mermaid Live Editor的URL - 使用正确的JSON格式（完全复制streamlit_app.py）"""
    try:
        # 清理Mermaid代码
        cleaned_code = mermaid_code.strip()

        if not cleaned_code:
            return f"{base_url}"

        # 创建正确的JSON配置对象
        mermaid_config = {
            "code": cleaned_code,
            "mermaid": {"theme": "default"},
            "autoSync": True,
            "updateDiagram": False,
            "editorMode": "code"
        }

        # 转换为JSON字符串
        json_string = json.dumps(mermaid_config)

        # Base64编码
        encoded_json = base64.b64encode(json_string.encode('utf-8')).decode('ascii')

        # 替换URL不安全字符
        safe_encoded = encoded_json.replace('+', '-').replace('/', '_')

        # 生成URL - 确保没有双斜杠
        base_clean = base_url.rstrip('/')
        url = f"{base_clean}/edit#base64:{safe_encoded}"

        return url

    except Exception as e:
        logging.error(f"创建Mermaid URL失败: {e}")
        return f"{base_url}"
def create_mermaid_live_editor_url_advanced(mermaid_code: str, base_url: str = "http://*************:8000/") -> dict:
#def create_mermaid_live_editor_url_advanced(mermaid_code: str, base_url: str = "http://***********:8008/") -> dict:
    """创建多种格式的Mermaid Live Editor URL用于测试（完全复制streamlit_app.py）"""
    urls = {}

    try:
        cleaned_code = mermaid_code.strip()

        # 创建标准的JSON配置对象
        mermaid_config = {
            "code": cleaned_code,
            "mermaid": {"theme": "default"},
            "autoSync": True,
            "updateDiagram": False,
            "editorMode": "code"
        }
        json_string = json.dumps(mermaid_config)

        # 确保base_url没有末尾斜杠，避免双斜杠问题
        base_clean = base_url.rstrip('/')

        # 方法1: Base64编码JSON（推荐）
        try:
            encoded_b64 = base64.b64encode(json_string.encode('utf-8')).decode('ascii')
            safe_b64 = encoded_b64.replace('+', '-').replace('/', '_')
            urls["base64_json"] = f"{base_clean}/edit#base64:{safe_b64}"
        except Exception as e:
            urls["base64_json"] = f"Error: {e}"

        # 方法2: Pako压缩JSON（最优）
        try:
            # 使用zlib压缩（兼容pako）
            compress = zlib.compressobj(9, zlib.DEFLATED, 15, 8, zlib.Z_DEFAULT_STRATEGY)
            compressed_data = compress.compress(json_string.encode('utf-8'))
            compressed_data += compress.flush()

            # Base64编码
            encoded_pako = base64.b64encode(compressed_data).decode('ascii')
            safe_pako = encoded_pako.replace('+', '-').replace('/', '_')
            urls["pako_json"] = f"{base_clean}/edit#pako:{safe_pako}"
        except Exception as e:
            urls["pako_json"] = f"Error: {e}"

        # 方法3: 简化JSON格式
        try:
            simple_config = {"code": cleaned_code}
            simple_json = json.dumps(simple_config)
            encoded_simple = base64.b64encode(simple_json.encode('utf-8')).decode('ascii')
            safe_simple = encoded_simple.replace('+', '-').replace('/', '_')
            urls["simple_json"] = f"{base_clean}/edit#base64:{safe_simple}"
        except Exception as e:
            urls["simple_json"] = f"Error: {e}"

        # 方法4: 直接URL编码（可能不工作）
        try:
            encoded_direct = urllib.parse.quote(cleaned_code, safe='')
            urls["direct_code"] = f"{base_clean}/edit#{encoded_direct}"
        except Exception as e:
            urls["direct_code"] = f"Error: {e}"

    except Exception as e:
        logging.error(f"生成高级URL失败: {e}")

    return urls

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        logging.error(f"数据库连接失败: {e}")
        raise

def format_datetime(dt):
    """格式化日期时间"""
    if dt is None:
        return "N/A"
    if isinstance(dt, datetime):
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    return str(dt)

def get_status_text(status):
    """获取状态文本"""
    status_map = {
        '0': '开始处理',
        '1': 'PDF下载完成',
        '2': 'OCR识别完成',
        '3': 'AI分析完成',
        '4': '处理出错'
    }
    return status_map.get(str(status), f'未知状态({status})')

def get_status_class(status):
    """获取状态CSS类"""
    status_class_map = {
        '0': 'status-start',
        '1': 'status-download',
        '2': 'status-ocr',
        '3': 'status-complete',
        '4': 'status-error'
    }
    return status_class_map.get(str(status), 'status-unknown')

def get_codesource_text(codesource):
    """获取图代码来源文本"""
    if codesource == '0':
        return 'AI生成'
    elif codesource == '1':
        return '人工修改'
    else:
        return 'N/A'

@app.route('/')
def index():
    """主页 - 页签式布局"""
    # 获取当前页签参数
    tab = request.args.get('tab', 'relations')  # 默认显示案件关系数据

    if tab == 'details':
        # 重定向到案件详细信息页面
        return redirect(url_for('case_details_list'))
    else:
        # 显示案件关系数据
        return redirect(url_for('case_relations_list'))

@app.route('/relations')
def case_relations_list():
    """案件关系数据页面"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        status_filter = request.args.get('status', '')
        search_term = request.args.get('search', '')
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if status_filter:
            where_conditions.append("status = %s")
            params.append(status_filter)
        
        if search_term:
            where_conditions.append("(ajbh LIKE %s OR ajmc LIKE %s OR batchid LIKE %s)")
            params.extend([f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'])
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 计算偏移量
        offset = (page - 1) * per_page
        
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取总记录数 - 只查询有效记录
                count_sql = f"""
                SELECT COUNT(*) as total FROM {get_table_name('case_relation_table')}
                WHERE (isdelete IS NULL OR isdelete = '0')
                {' AND ' + ' AND '.join(where_conditions) if where_conditions else ''}
                """
                cursor.execute(count_sql, params)
                total_records = cursor.fetchone()['total']

                # 获取分页数据 - 查询所有字段包括新增字段
                data_sql = f"""
                SELECT batchid, ajbh, ajmc, llsj, counts, xxzjbh, ajlx, flwsxzdz, tbrksj, tbgxsj,
                       ajnr, code, lastcode, updater, codesource, status, starttime, endtime,
                       updatetime, nums, error, isdelete
                FROM {get_table_name('case_relation_table')}
                WHERE (isdelete IS NULL OR isdelete = '0')
                {' AND ' + ' AND '.join(where_conditions) if where_conditions else ''}
                ORDER BY updatetime DESC, starttime DESC
                LIMIT %s OFFSET %s
                """
                cursor.execute(data_sql, params + [per_page, offset])
                cases = cursor.fetchall()
        
        # 计算分页信息
        total_pages = (total_records + per_page - 1) // per_page

        # 计算要展示的页码列表，避免在Jinja中使用max/min/range
        start_p = 1 if page - 2 < 1 else page - 2
        end_p = total_pages if page + 2 > total_pages else page + 2
        pages_to_show = list(range(start_p, end_p + 1))

        # 处理数据格式化
        for case in cases:
            case['status_text'] = get_status_text(case['status'])
            case['status_class'] = get_status_class(case['status'])
            case['codesource_text'] = get_codesource_text(case['codesource'])

            # 格式化所有日期时间字段
            case['llsj_formatted'] = format_datetime(case.get('llsj'))
            case['tbgxsj_formatted'] = format_datetime(case.get('tbgxsj'))
            case['tbrksj_formatted'] = format_datetime(case['tbrksj'])
            case['starttime_formatted'] = format_datetime(case['starttime'])
            case['endtime_formatted'] = format_datetime(case['endtime'])
            case['updatetime_formatted'] = format_datetime(case['updatetime'])

            # 格式化新增字段
            case['counts'] = case.get('counts', 0) or 0
            case['xxzjbh_display'] = case.get('xxzjbh', '') or ''
            case['isdelete_text'] = '已删除' if case.get('isdelete') == '1' else '有效'

            # 检查是否有关系图 - 优先lastcode，其次code，加强NULL值处理
            def safe_check_code(code_field):
                """安全检查代码字段"""
                if code_field is None:
                    return False
                if not isinstance(code_field, str):
                    return False
                return bool(code_field.strip())

            case['has_mermaid'] = (
                safe_check_code(case.get('lastcode')) or
                safe_check_code(case.get('code'))
            )
            case['has_original_code'] = safe_check_code(case.get('code'))

            # 截断长文本用于显示
            if case['ajnr']:
                case['ajnr_short'] = case['ajnr'][:100] + '...' if len(case['ajnr']) > 100 else case['ajnr']
            else:
                case['ajnr_short'] = 'N/A'

            if case['flwsxzdz']:
                case['flwsxzdz_short'] = case['flwsxzdz'][:50] + '...' if len(case['flwsxzdz']) > 50 else case['flwsxzdz']
            else:
                case['flwsxzdz_short'] = 'N/A'
        
        return render_template('case_relations.html',
                             cases=cases,
                             page=page,
                             per_page=per_page,
                             total_records=total_records,
                             total_pages=total_pages,
                             pages_to_show=pages_to_show,
                             status_filter=status_filter,
                             search_term=search_term,
                             current_tab='relations')
        
    except Exception as e:
        logging.error(f"查询案件数据失败: {e}")
        return f"查询失败: {str(e)}", 500

@app.route('/case/<ajbh>')
def case_detail(ajbh):
    """案件详情页面"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"""
                SELECT batchid, ajbh, ajmc,  ajlx, flwsxzdz, tbrksj, ajnr, code, lastcode,
                       updater, codesource, status, starttime, endtime, updatetime, nums, error
                FROM {get_table_name('case_relation_table')}
                WHERE ajbh = %s
                """
                cursor.execute(sql, (ajbh,))
                case = cursor.fetchone()

        if not case:
            return "案件不存在", 404

        # 格式化数据
        case['status_text'] = get_status_text(case['status'])
        case['status_class'] = get_status_class(case['status'])
        case['codesource_text'] = get_codesource_text(case['codesource'])

        # 格式化日期时间
        case['llsj_formatted'] = format_datetime(case.get('llsj'))
        case['tbgxsj_formatted'] = format_datetime(case.get('tbgxsj'))
        case['tbrksj_formatted'] = format_datetime(case['tbrksj'])
        case['starttime_formatted'] = format_datetime(case['starttime'])
        case['endtime_formatted'] = format_datetime(case['endtime'])
        case['updatetime_formatted'] = format_datetime(case['updatetime'])

        # 格式化新增字段
        case['counts'] = case.get('counts', 0) or 0
        case['xxzjbh_display'] = case.get('xxzjbh', '') or ''
        case['isdelete_text'] = '已删除' if case.get('isdelete') == '1' else '有效'

        # 检查关系图 - 优先lastcode，其次code，加强NULL值处理
        def safe_check_code(code_field):
            """安全检查代码字段"""
            if code_field is None:
                return False
            if not isinstance(code_field, str):
                return False
            return bool(code_field.strip())

        case['has_mermaid'] = (
            safe_check_code(case.get('lastcode')) or
            safe_check_code(case.get('code'))
        )
        case['has_original_code'] = safe_check_code(case.get('code'))

        return render_template('case_detail.html', case=case)

    except Exception as e:
        logging.error(f"查询案件详情失败: {e}")
        return f"查询失败: {str(e)}", 500

@app.route('/mermaid/<ajbh>')
def view_mermaid(ajbh):
    """查看Mermaid关系图 - 使用与streamlit_app.py相同的逻辑"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 查询所有相关字段用于调试
                sql = f"SELECT batchid, ajbh, ajmc, code, lastcode FROM {get_table_name('case_relation_table')} WHERE ajbh = %s"
                cursor.execute(sql, (ajbh,))
                case = cursor.fetchone()

        if not case:
            logging.error(f"案件不存在: {ajbh}")
            return f"案件 {ajbh} 不存在", 404

        # 优先使用lastcode，如果没有则使用code，加强NULL值处理
        mermaid_code = None

        # 安全检查函数
        def safe_get_code(code_field):
            """安全获取代码字段"""
            if code_field is None:
                return None
            if not isinstance(code_field, str):
                return None
            stripped = code_field.strip()
            return stripped if stripped else None

        # 详细调试信息
        lastcode_safe = safe_get_code(case.get('lastcode'))
        code_safe = safe_get_code(case.get('code'))

        logging.info(f"案件 {ajbh} 数据检查:")
        logging.info(f"  lastcode原始: {case.get('lastcode') is not None}")
        logging.info(f"  lastcode安全: {lastcode_safe is not None}")
        logging.info(f"  lastcode长度: {len(lastcode_safe) if lastcode_safe else 0}")
        logging.info(f"  code原始: {case.get('code') is not None}")
        logging.info(f"  code安全: {code_safe is not None}")
        logging.info(f"  code长度: {len(code_safe) if code_safe else 0}")

        if lastcode_safe:
            mermaid_code = lastcode_safe
            logging.info(f"✅ 使用lastcode字段，长度: {len(mermaid_code)}")
        elif code_safe:
            mermaid_code = code_safe
            logging.info(f"✅ 使用code字段，长度: {len(mermaid_code)}")
        else:
            logging.error(f"❌ 两个字段都不可用")

        if not mermaid_code:
            logging.error(f"案件 {ajbh} 没有关系图代码 - lastcode: {bool(case['lastcode'])}, code: {bool(case['code'])}")
            return f"""
            <html>
            <head><title>无关系图代码</title></head>
            <body>
            <h2>该案件没有关系图代码</h2>
            <p>案件编号: {ajbh}</p>
            <p>案件名称: {case['ajmc']}</p>
            <p>lastcode字段: {'存在' if case['lastcode'] else '不存在'} (长度: {len(case['lastcode']) if case['lastcode'] else 0})</p>
            <p>code字段: {'存在' if case['code'] else '不存在'} (长度: {len(case['code']) if case['code'] else 0})</p>
            <p><a href="/debug_case/{ajbh}">查看调试信息</a></p>
            <p><a href="/">返回主页</a></p>
            </body>
            </html>
            """, 404
        
        # 使用与streamlit_app.py相同的URL创建方式
        try:
            logging.info(f"🚀 开始创建URL，代码长度: {len(mermaid_code)}")

            # 创建高级URL（优先使用pako压缩）
            advanced_urls = create_mermaid_live_editor_url_advanced(mermaid_code)
            logging.info(f"高级URL创建结果: {list(advanced_urls.keys())}")

            # 获取最佳编辑URL（优先级：pako_json > base64_json > simple_json）
            edit_url = None
            if advanced_urls.get("pako_json") and not advanced_urls["pako_json"].startswith("Error"):
                edit_url = advanced_urls["pako_json"]
                logging.info(f"✅ 使用pako压缩URL: {edit_url[:100]}...")
            elif advanced_urls.get("base64_json") and not advanced_urls["base64_json"].startswith("Error"):
                edit_url = advanced_urls["base64_json"]
                logging.info(f"✅ 使用base64 JSON URL: {edit_url[:100]}...")
            elif advanced_urls.get("simple_json") and not advanced_urls["simple_json"].startswith("Error"):
                edit_url = advanced_urls["simple_json"]
                logging.info(f"✅ 使用简化JSON URL: {edit_url[:100]}...")
            else:
                # 回退到基础URL创建方式
                edit_url = create_mermaid_live_editor_url(mermaid_code)
                logging.info(f"✅ 使用基础URL: {edit_url[:100]}...")

            if edit_url:
                logging.info(f"🎯 准备重定向到: {edit_url}")

                # 为了调试，先返回一个包含重定向链接的页面
                return f"""
                <html>
                <head>
                    <title>重定向到Mermaid Live Editor</title>
                    <meta http-equiv="refresh" content="2;url={edit_url}">
                </head>
                <body>
                    <h2>正在跳转到Mermaid Live Editor...</h2>
                    <p>案件编号: {ajbh}</p>
                    <p>目标URL: <a href="{edit_url}" target="_blank">{edit_url[:100]}...</a></p>
                    <p>如果没有自动跳转，请点击上面的链接</p>
                    <script>
                        setTimeout(function() {{
                            window.location.href = "{edit_url}";
                        }}, 2000);
                    </script>
                </body>
                </html>
                """
            else:
                logging.error("无法创建编辑器URL")
                return "无法创建编辑器URL", 500

        except Exception as e:
            logging.error(f"创建Mermaid编辑器URL失败: {e}")
            # 最后的回退方案：使用在线版本
            try:
                simple_encoded = base64.b64encode(mermaid_code.encode('utf-8')).decode('utf-8')
                online_url = f"https://mermaid.live/edit#{simple_encoded}"
                logging.warning(f"回退到在线版本: {online_url[:100]}...")
                return redirect(online_url)
            except Exception as e2:
                logging.error(f"回退到在线版本也失败: {e2}")
                return f"无法创建编辑器链接: {str(e)}", 500
        
    except Exception as e:
        logging.error(f"查看Mermaid关系图失败: {e}")
        return f"查看失败: {str(e)}", 500

@app.route('/mermaid_debug/<ajbh>')
def mermaid_debug(ajbh):
    """调试Mermaid URL创建过程"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"SELECT batchid, ajbh, ajmc, lastcode FROM {get_table_name('case_relation_table')} WHERE ajbh = %s"
                cursor.execute(sql, (ajbh,))
                case = cursor.fetchone()

        if not case:
            return "案件不存在", 404

        if not case['lastcode'] or not case['lastcode'].strip():
            return "该案件没有关系图代码", 404

        mermaid_code = case['lastcode'].strip()

        # 创建所有可能的URL
        basic_url = create_mermaid_live_editor_url(mermaid_code)
        advanced_urls = create_mermaid_live_editor_url_advanced(mermaid_code)

        debug_info = {
            'case': case,
            'mermaid_code_length': len(mermaid_code),
            'mermaid_code_preview': mermaid_code[:200] + '...' if len(mermaid_code) > 200 else mermaid_code,
            'basic_url': basic_url,
            'advanced_urls': advanced_urls,
            'base_url': MERMAID_LIVE_EDITOR_URL
        }

        return render_template('mermaid_debug.html', debug_info=debug_info)

    except Exception as e:
        logging.error(f"调试Mermaid URL失败: {e}")
        return f"调试失败: {str(e)}", 500

@app.route('/mermaid_view/<ajbh>')
def mermaid_view(ajbh):
    """内嵌查看Mermaid关系图"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"SELECT batchid, ajbh, ajmc, lastcode, code FROM {get_table_name('case_relation_table')} WHERE ajbh = %s"
                cursor.execute(sql, (ajbh,))
                case = cursor.fetchone()

        if not case:
            return "案件不存在", 404

        mermaid_code = case['lastcode'] or case['code'] or ""
        if not mermaid_code.strip():
            return "该案件没有关系图代码", 404

        # 编码用于URL跳转
        encoded_code = base64.b64encode(mermaid_code.encode('utf-8')).decode('utf-8')

        return render_template('mermaid_view.html',
                             case=case,
                             mermaid_code=mermaid_code,
                             encoded_code=encoded_code)

    except Exception as e:
        logging.error(f"查看Mermaid关系图失败: {e}")
        return f"查看失败: {str(e)}", 500

@app.route('/api/case/<ajbh>')
def api_get_case(ajbh):
    """API接口 - 获取单个案件信息"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"""
                SELECT batchid, ajbh, ajmc, llsj, counts, xxzjbh, ajlx, flwsxzdz, tbrksj, tbgxsj,
                       ajnr, code, lastcode, updater, codesource, status, starttime, endtime,
                       updatetime, nums, error, isdelete
                FROM {get_table_name('case_relation_table')}
                WHERE ajbh = %s AND (isdelete IS NULL OR isdelete = '0')
                """
                cursor.execute(sql, (ajbh,))
                case = cursor.fetchone()
        
        if not case:
            return jsonify({"error": "案件不存在"}), 404
        
        # 格式化日期时间
        for key in ['tfsj', 'xgsj', 'tbrksj', 'starttime', 'endtime', 'updatetime']:
            if case.get(key):
                case[key] = case[key].isoformat() if hasattr(case[key], 'isoformat') else str(case[key])

        case['status_text'] = get_status_text(case['status'])
        case['codesource_text'] = get_codesource_text(case['codesource'])
        
        return jsonify(case)
        
    except Exception as e:
        logging.error(f"API查询案件失败: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/stats')
def stats():
    """统计页面"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 状态统计
                cursor.execute(f"""
                    SELECT status, COUNT(*) as count
                    FROM {get_table_name('case_relation_table')}
                    GROUP BY status
                    ORDER BY status
                """)
                status_stats = cursor.fetchall()

                # 更新类型统计
                cursor.execute(f"""
                    SELECT codesource, COUNT(*) as count
                    FROM {get_table_name('case_relation_table')}
                    GROUP BY codesource
                    ORDER BY codesource
                """)
                codesource_stats = cursor.fetchall()

                # 每日处理统计
                cursor.execute(f"""
                    SELECT DATE(starttime) as date, COUNT(*) as count
                    FROM {get_table_name('case_relation_table')}
                    WHERE starttime IS NOT NULL
                    GROUP BY DATE(starttime)
                    ORDER BY date DESC
                    LIMIT 30
                """)
                daily_stats = cursor.fetchall()

                # 批次统计
                cursor.execute(f"""
                    SELECT batchid, COUNT(*) as count,
                           MIN(starttime) as start_time,
                           MAX(updatetime) as end_time,
                           SUM(CASE WHEN status = '3' THEN 1 ELSE 0 END) as completed,
                           SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as failed,
                           SUM(CASE WHEN error IS NOT NULL AND error != '' THEN 1 ELSE 0 END) as error_count
                    FROM {get_table_name('case_relation_table')}
                    GROUP BY batchid
                    ORDER BY start_time DESC
                    LIMIT 20
                """)
                batch_stats = cursor.fetchall()
                
                # 批次统计
                cursor.execute(f"""
                    SELECT batchid, COUNT(*) as count,
                           MIN(starttime) as start_time,
                           MAX(updatetime) as end_time,
                           SUM(CASE WHEN status = '3' THEN 1 ELSE 0 END) as completed,
                           SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as failed
                    FROM {get_table_name('case_relation_table')}
                    GROUP BY batchid
                    ORDER BY start_time DESC
                    LIMIT 20
                """)
                batch_stats = cursor.fetchall()
        
        # 格式化数据
        for stat in status_stats:
            stat['status_text'] = get_status_text(stat['status'])

        for stat in codesource_stats:
            stat['codesource_text'] = get_codesource_text(stat['codesource'])

        # 格式化批次统计
        for stat in batch_stats:
            stat['start_time_formatted'] = format_datetime(stat['start_time'])
            stat['end_time_formatted'] = format_datetime(stat['end_time'])
            stat['success_rate'] = (stat['completed'] / stat['count'] * 100) if stat['count'] > 0 else 0
            stat['error_rate'] = (stat['error_count'] / stat['count'] * 100) if stat['count'] > 0 else 0
        
        return render_template('stats.html',
                             status_stats=status_stats,
                             codesource_stats=codesource_stats,
                             daily_stats=daily_stats,
                             batch_stats=batch_stats)
        
    except Exception as e:
        logging.error(f"查询统计数据失败: {e}")
        return f"查询失败: {str(e)}", 500

@app.route('/field_check')
def field_check():
    """字段显示验证页面"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取表结构
                cursor.execute("DESCRIBE ds_case_relation")
                table_structure = cursor.fetchall()

                # 获取样本数据
                cursor.execute(f"""
                    SELECT batchid, ajbh, ajmc, ajlx, flwsxzdz, tbrksj, ajnr, code, lastcode,
                           updater, codesource, status, starttime, endtime, updatetime, nums, error
                    FROM {get_table_name('case_relation_table')}
                    ORDER BY updatetime DESC
                    LIMIT 1
                """)
                sample_data = cursor.fetchone()

                # 字段映射
                field_mapping = [
                    {'field': 'batchid', 'name': '批次号', 'description': '数据批次号'},
                    {'field': 'ajbh', 'name': '案件编号', 'description': '案件编号'},
                    {'field': 'ajmc', 'name': '案件名称', 'description': '案件名称'}, 
                    {'field': 'ajlx', 'name': '案件类型', 'description': '案件类型'},
                    {'field': 'flwsxzdz', 'name': '文书地址', 'description': '法律文书下载地址'},
                    {'field': 'tbrksj', 'name': '同步时间', 'description': '同步入库时间'},
                    {'field': 'ajnr', 'name': '案件内容', 'description': '案件内容'},
                    {'field': 'code', 'name': '关系图代码', 'description': '关系图代码'},
                    {'field': 'lastcode', 'name': '最终代码', 'description': '最终的关系图代码'},
                    {'field': 'updater', 'name': '修改人员', 'description': '修改人员'},
                    {'field': 'codesource', 'name': '图代码来源', 'description': '图代码来源'},
                    {'field': 'status', 'name': '状态', 'description': 'AI处理状态'},
                    {'field': 'starttime', 'name': '开始时间', 'description': 'AI开始处理时间'},
                    {'field': 'endtime', 'name': '完成时间', 'description': 'AI完成处理时间'},
                    {'field': 'updatetime', 'name': '更新时间', 'description': '更新时间'},
                    {'field': 'nums', 'name': '重跑次数', 'description': 'AI重跑次数'},
                    {'field': 'error', 'name': '错误信息', 'description': '错误信息'}
                ]

                return render_template('field_check.html',
                                     table_structure=table_structure,
                                     sample_data=sample_data,
                                     field_mapping=field_mapping)

    except Exception as e:
        logging.error(f"字段检查失败: {e}")
        return f"字段检查失败: {str(e)}", 500

@app.route('/test_code_display')
def test_code_display():
    """测试代码字段显示"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取有代码的案件
                cursor.execute(f"""
                    SELECT batchid, ajbh, ajmc, code, lastcode
                    FROM {get_table_name('case_relation_table')}
                    WHERE (code IS NOT NULL AND code != '')
                       OR (lastcode IS NOT NULL AND lastcode != '')
                    ORDER BY updatetime DESC
                    LIMIT 5
                """)
                cases_with_code = cursor.fetchall()

                # 处理数据
                for case in cases_with_code:
                    case['has_original_code'] = bool(case['code'] and case['code'].strip())
                    case['has_mermaid'] = bool(case['lastcode'] and case['lastcode'].strip())

                    # 截断代码用于显示
                    if case['code']:
                        case['code_short'] = case['code'][:50] + '...' if len(case['code']) > 50 else case['code']
                    else:
                        case['code_short'] = None

                    if case['lastcode']:
                        case['lastcode_short'] = case['lastcode'][:50] + '...' if len(case['lastcode']) > 50 else case['lastcode']
                    else:
                        case['lastcode_short'] = None

                return render_template('test_code_display.html', cases=cases_with_code)

    except Exception as e:
        logging.error(f"测试代码显示失败: {e}")
        return f"测试失败: {str(e)}", 500

@app.route('/debug_case/<ajbh>')
def debug_case(ajbh):
    """调试案件数据"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"SELECT batchid, ajbh, ajmc, code, lastcode FROM {get_table_name('case_relation_table')} WHERE ajbh = %s"
                cursor.execute(sql, (ajbh,))
                case = cursor.fetchone()

        if not case:
            return f"案件 {ajbh} 不存在", 404

        # 调试信息
        debug_info = {
            'ajbh': case['ajbh'],
            'ajmc': case['ajmc'],
            'code_exists': case['code'] is not None,
            'code_length': len(case['code']) if case['code'] else 0,
            'code_stripped_length': len(case['code'].strip()) if case['code'] else 0,
            'lastcode_exists': case['lastcode'] is not None,
            'lastcode_length': len(case['lastcode']) if case['lastcode'] else 0,
            'lastcode_stripped_length': len(case['lastcode'].strip()) if case['lastcode'] else 0,
            'has_original_code': bool(case['code'] and case['code'].strip()),
            'has_mermaid': bool(case['lastcode'] and case['lastcode'].strip()),
            'code_preview': case['code'][:100] if case['code'] else None,
            'lastcode_preview': case['lastcode'][:100] if case['lastcode'] else None
        }

        return f"""
        <html>
        <head><title>调试案件 {ajbh}</title></head>
        <body>
        <h2>案件 {ajbh} 调试信息</h2>
        <table border="1" style="border-collapse: collapse;">
        <tr><th>字段</th><th>值</th></tr>
        <tr><td>案件编号</td><td>{debug_info['ajbh']}</td></tr>
        <tr><td>案件名称</td><td>{debug_info['ajmc']}</td></tr>
        <tr><td>code字段存在</td><td>{debug_info['code_exists']}</td></tr>
        <tr><td>code字段长度</td><td>{debug_info['code_length']}</td></tr>
        <tr><td>code去空格后长度</td><td>{debug_info['code_stripped_length']}</td></tr>
        <tr><td>lastcode字段存在</td><td>{debug_info['lastcode_exists']}</td></tr>
        <tr><td>lastcode字段长度</td><td>{debug_info['lastcode_length']}</td></tr>
        <tr><td>lastcode去空格后长度</td><td>{debug_info['lastcode_stripped_length']}</td></tr>
        <tr><td>has_original_code</td><td>{debug_info['has_original_code']}</td></tr>
        <tr><td>has_mermaid</td><td>{debug_info['has_mermaid']}</td></tr>
        </table>

        <h3>code字段预览:</h3>
        <pre style="background: #f0f0f0; padding: 10px;">{debug_info['code_preview'] or 'NULL'}</pre>

        <h3>lastcode字段预览:</h3>
        <pre style="background: #f0f0f0; padding: 10px;">{debug_info['lastcode_preview'] or 'NULL'}</pre>

        <p><a href="/">返回主页</a></p>
        </body>
        </html>
        """

    except Exception as e:
        return f"调试失败: {str(e)}", 500

@app.route('/test_redirect')
def test_redirect():
    """测试重定向功能"""
    test_url = "http://*************:8000/edit#base64:eyJjb2RlIjoiZ3JhcGggVERcbiAgICBBW3Rlc3RdIC0tPiBCW3Rlc3QyXSIsIm1lcm1haWQiOnsidGhlbWUiOiJkZWZhdWx0In0sImF1dG9TeW5jIjp0cnVlLCJ1cGRhdGVEaWFncmFtIjpmYWxzZSwiZWRpdG9yTW9kZSI6ImNvZGUifQ"
    logging.info(f"测试重定向到: {test_url}")
    return redirect(test_url)

@app.route('/case_details')
def case_details_list():
    """案件详细信息列表页面"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        search_term = request.args.get('search', '')
        batchid_filter = request.args.get('batchid', '')
        ajbh_filter = request.args.get('ajbh', '')

        # 构建查询条件
        where_conditions = ["(isdelete IS NULL OR isdelete = '0')"]
        params = []

        if search_term:
            where_conditions.append("(ajbh LIKE %s OR ajmc LIKE %s OR name_code LIKE %s OR batchid LIKE %s)")
            params.extend([f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'])

        if batchid_filter:
            where_conditions.append("batchid = %s")
            params.append(batchid_filter)

        if ajbh_filter:
            where_conditions.append("ajbh = %s")
            params.append(ajbh_filter)

        where_clause = "WHERE " + " AND ".join(where_conditions)

        # 计算偏移量
        offset = (page - 1) * per_page

        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取总记录数
                count_sql = f"SELECT COUNT(*) as total FROM ds_case_details {where_clause}"
                cursor.execute(count_sql, params)
                total_records = cursor.fetchone()['total']

                # 获取分页数据 - 查询所有字段
                data_sql = f"""
                SELECT id, batchid, ajbh, ajmc, entity_type, name_code, gender, age, id_card,
                       residence, education, direct_superior, company, organization, role,
                       responsibilities, peers_name, peers, vertical_name, vertical,
                       related_tools, related_items, related_actions, related_locations,
                       measures, judicial_result, economic, criminal, data_time, update_time, isdelete
                FROM {get_table_name('case_details_table')}
                {where_clause}
                ORDER BY data_time DESC, id DESC
                LIMIT %s OFFSET %s
                """
                cursor.execute(data_sql, params + [per_page, offset])
                details = cursor.fetchall()

        # 计算分页信息
        total_pages = (total_records + per_page - 1) // per_page

        # 计算要展示的页码列表，避免在Jinja中使用max/min/range
        start_p = 1 if page - 2 < 1 else page - 2
        end_p = total_pages if page + 2 > total_pages else page + 2
        pages_to_show = list(range(start_p, end_p + 1))

        # 处理数据格式化
        for detail in details:
            # 格式化日期时间字段
            detail['data_time_formatted'] = format_datetime(detail.get('data_time'))
            detail['update_time_formatted'] = format_datetime(detail.get('update_time'))
            detail['isdelete_text'] = '已删除' if detail.get('isdelete') == '1' else '有效'

        return render_template('case_details.html',
                             details=details,
                             page=page,
                             per_page=per_page,
                             total_pages=total_pages,
                             total_records=total_records,
                             pages_to_show=pages_to_show,
                             search_term=search_term,
                             batchid_filter=batchid_filter,
                             ajbh_filter=ajbh_filter,
                             current_tab='details')

    except Exception as e:
        return f"查询失败: {str(e)}", 500

@app.route('/case_details/<int:detail_id>')
def case_detail_view(detail_id):
    """案件详细信息详情页面"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"""
                SELECT id, batchid, ajbh, ajmc, entity_type, name_code, gender, age, id_card,
                       residence, education, direct_superior, company, organization, role,
                       responsibilities, peers_name, peers, vertical_name, vertical,
                       related_tools, related_items, related_actions, related_locations,
                       measures, judicial_result, economic, criminal, data_time, update_time, isdelete
                FROM {get_table_name('case_details_table')}
                WHERE id = %s AND (isdelete IS NULL OR isdelete = '0')
                """
                cursor.execute(sql, (detail_id,))
                detail = cursor.fetchone()

        if not detail:
            return "详细信息未找到", 404

        # 格式化日期时间
        detail['data_time_formatted'] = format_datetime(detail.get('data_time'))
        detail['update_time_formatted'] = format_datetime(detail.get('update_time'))
        detail['isdelete_text'] = '已删除' if detail.get('isdelete') == '1' else '有效'

        return render_template('case_detail_view.html', detail=detail)

    except Exception as e:
        return f"查询失败: {str(e)}", 500

if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行应用
    app.run(host='0.0.0.0', port=9998, debug=True)
