#!/usr/bin/env python3
"""
分片批处理系统快速启动脚本
提供简单的命令行界面来使用分片批处理功能
"""

import asyncio
import argparse
import logging
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_controller import MainController
from config import config


def setup_logging():
    """设置日志"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    log_file = f"{log_dir}/batch_slicing_{datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


async def run_time_range_task(stime: str, etime: str, ajbh: str = None):
    """运行时间范围任务"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始时间范围任务（分片批处理）")
        logger.info(f"时间范围: {stime} - {etime}")
        logger.info(f"案件编号过滤: {ajbh or '无'}")
        logger.info(f"分片批大小: {config.get_system_config()['batch_slice_size']}")
        
        controller = MainController()
        result = await controller.run_time_range_task(stime, etime, ajbh)
        
        if result["status"] == "success":
            logger.info(f"✅ 任务完成成功")
            
            batch_result = result.get("batch_result", {})
            if "slice_results" in batch_result:
                # 分片批处理结果
                slice_results = batch_result["slice_results"]
                logger.info(f"分片批处理汇总:")
                logger.info(f"  总分片批数: {len(slice_results)}")
                logger.info(f"  总成功案件: {batch_result.get('total_success', 0)}")
                logger.info(f"  总失败案件: {batch_result.get('total_error', 0)}")
                
                for slice_result in slice_results:
                    ppid = slice_result["ppid"]
                    ocr_result = slice_result["ocr_result"]
                    logger.info(f"  片批{ppid}: 成功 {ocr_result.get('success_count', 0)}, 失败 {ocr_result.get('error_count', 0)}")
            
        else:
            logger.error(f"❌ 任务失败: {result.get('error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"任务执行异常: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}


async def run_ajbh_task(ajbh: str):
    """运行案件编号任务"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始案件编号任务（分片批处理）")
        logger.info(f"案件编号: {ajbh}")
        logger.info(f"分片批大小: {config.get_system_config()['batch_slice_size']}")
        
        controller = MainController()
        result = await controller.run_ajbh_task(ajbh)
        
        if result["status"] == "success":
            logger.info(f"✅ 任务完成成功")
        else:
            logger.error(f"❌ 任务失败: {result.get('error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"任务执行异常: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}


def show_system_info():
    """显示系统信息"""
    print("=" * 80)
    print("分片批处理系统信息")
    print("=" * 80)
    
    system_config = config.get_system_config()
    
    print(f"配置信息:")
    print(f"  分片批大小: {system_config['batch_slice_size']} 个PDF/片批")
    print(f"  OCR基础路径: {system_config['ocr_base_path']}")
    print(f"  Docker容器: {system_config['docker_container']}")
    print(f"  最大并发数: {system_config['max_concurrent']}")
    
    print(f"\n优势特性:")
    print(f"  ✓ 解决大批量OCR超时问题")
    print(f"  ✓ 支持增量处理和错误恢复")
    print(f"  ✓ 提高系统稳定性和效率")
    print(f"  ✓ 完全向后兼容")
    
    print(f"\n示例场景:")
    test_cases = [50, 100, 300, 600]
    for case_count in test_cases:
        slice_count = (case_count + system_config['batch_slice_size'] - 1) // system_config['batch_slice_size']
        print(f"  {case_count} 个案件 → {slice_count} 个分片批")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分片批处理系统快速启动')
    parser.add_argument('--mode', choices=['time_range', 'ajbh', 'info'], required=True,
                        help='运行模式: time_range(时间范围), ajbh(案件编号), info(显示信息)')
    parser.add_argument('--stime', help='开始时间 (格式: YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--etime', help='结束时间 (格式: YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--ajbh', help='案件编号')
    parser.add_argument('--filter-ajbh', help='时间范围任务中的案件编号过滤')
    
    args = parser.parse_args()
    
    if args.mode == 'info':
        show_system_info()
        return
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info(f"分片批处理系统启动")
    logger.info(f"运行模式: {args.mode}")
    
    try:
        if args.mode == 'time_range':
            if not args.stime or not args.etime:
                print("❌ 时间范围模式需要提供 --stime 和 --etime 参数")
                return
            
            result = asyncio.run(run_time_range_task(args.stime, args.etime, args.filter_ajbh))
            
        elif args.mode == 'ajbh':
            if not args.ajbh:
                print("❌ 案件编号模式需要提供 --ajbh 参数")
                return
            
            result = asyncio.run(run_ajbh_task(args.ajbh))
        
        # 显示最终结果
        if result["status"] == "success":
            print(f"\n🎉 任务执行成功！")
        else:
            print(f"\n❌ 任务执行失败: {result.get('error')}")
            
    except KeyboardInterrupt:
        logger.info("用户中断任务")
    except Exception as e:
        logger.error(f"任务执行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
