#!/usr/bin/env python3
"""
诊断数据库表结构脚本
检查案件关系表和案件详细信息表的字段
"""

import pymysql
import sys
from datetime import datetime

def get_connection():
    """获取数据库连接"""
    try:
        # 从config.py导入数据库配置
        sys.path.append('.')
        from config import DATABASE_CONFIG
        return pymysql.connect(**DATABASE_CONFIG)
    except ImportError:
        # 如果无法导入配置，使用默认配置
        db_config = {
            'host': 'localhost',
            'user': 'root', 
            'password': '123456',
            'database': 'djzs_db',
            'charset': 'utf8mb4'
        }
        return pymysql.connect(**db_config)

def check_table_structure(table_name):
    """检查表结构"""
    print(f"\n🔍 检查表结构: {table_name}")
    print("="*50)
    
    try:
        with get_connection() as connection:
            with connection.cursor() as cursor:
                # 检查表是否存在
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if not cursor.fetchone():
                    print(f"❌ 表不存在: {table_name}")
                    return False
                
                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                
                print(f"✅ 表存在，共 {len(columns)} 个字段:")
                print(f"{'字段名':<20} {'类型':<20} {'允许NULL':<10} {'键':<10} {'默认值':<15}")
                print("-" * 80)
                
                field_names = []
                for column in columns:
                    field_name = column[0]
                    field_type = column[1]
                    null_allowed = column[2]
                    key_type = column[3]
                    default_value = column[4] if column[4] is not None else 'NULL'
                    
                    field_names.append(field_name)
                    print(f"{field_name:<20} {field_type:<20} {null_allowed:<10} {key_type:<10} {str(default_value):<15}")
                
                return field_names
                
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False

def check_missing_fields():
    """检查缺失的字段"""
    print(f"\n🔍 检查关键字段是否存在")
    print("="*50)
    
    # 预期的字段列表
    expected_fields = {
        'ds_case_relation_graph': [
            'id', 'ajbh', 'ajmc', 'ajlx', 'ajzt', 'ajly', 'ajsj', 'ajdd', 'ajms',
            'dsr', 'dsr_sfzh', 'dsr_lxfs', 'bhr', 'bhr_sfzh', 'bhr_lxfs',
            'ajgx', 'gxms', 'gxqd', 'gxjg', 'gxsj', 'gxdd', 'gxr', 'gxr_sfzh',
            'gxr_lxfs', 'ajjg', 'jgms', 'jgsj', 'jgdd', 'jgr', 'jgr_sfzh',
            'jgr_lxfs', 'cjsj', 'gxsj_update', 'is_deleted', 'llsj'
        ],
        'ds_case_details': [
            'id', 'ajbh', 'ajmc', 'ajlx', 'ajzt', 'ajly', 'ajsj', 'ajdd', 'ajms',
            'dsr', 'dsr_sfzh', 'dsr_lxfs', 'bhr', 'bhr_sfzh', 'bhr_lxfs',
            'cjsj', 'gxsj', 'is_deleted', 'llsj'
        ]
    }
    
    for table_name, expected in expected_fields.items():
        print(f"\n📋 检查表: {table_name}")
        actual_fields = check_table_structure(table_name)
        
        if actual_fields:
            missing = [field for field in expected if field not in actual_fields]
            extra = [field for field in actual_fields if field not in expected]
            
            if missing:
                print(f"\n❌ 缺失字段: {missing}")
            else:
                print(f"\n✅ 所有预期字段都存在")
            
            if extra:
                print(f"ℹ️  额外字段: {extra}")

def generate_fix_sql():
    """生成修复SQL语句"""
    print(f"\n🛠️ 生成修复SQL语句")
    print("="*50)
    
    # 检查哪些字段缺失
    missing_fields_sql = []
    
    # 为案件关系表添加缺失字段
    relation_table_fixes = [
        "ALTER TABLE ds_case_relation_graph ADD COLUMN llsj DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间';",
        "ALTER TABLE ds_case_relation_graph ADD COLUMN is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除';",
        "ALTER TABLE ds_case_relation_graph ADD COLUMN gxsj_update DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';"
    ]
    
    # 为案件详细信息表添加缺失字段
    details_table_fixes = [
        "ALTER TABLE ds_case_details ADD COLUMN llsj DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间';",
        "ALTER TABLE ds_case_details ADD COLUMN is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除';",
        "ALTER TABLE ds_case_details ADD COLUMN gxsj DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';"
    ]
    
    print("-- 案件关系表修复SQL:")
    for sql in relation_table_fixes:
        print(sql)
    
    print("\n-- 案件详细信息表修复SQL:")
    for sql in details_table_fixes:
        print(sql)
    
    # 保存到文件
    with open('fix_missing_fields.sql', 'w', encoding='utf-8') as f:
        f.write("-- 修复缺失字段的SQL语句\n")
        f.write(f"-- 生成时间: {datetime.now()}\n\n")
        
        f.write("-- 案件关系表修复\n")
        for sql in relation_table_fixes:
            f.write(sql + "\n")
        
        f.write("\n-- 案件详细信息表修复\n")
        for sql in details_table_fixes:
            f.write(sql + "\n")
    
    print(f"\n✅ SQL语句已保存到: fix_missing_fields.sql")

def test_connection():
    """测试数据库连接"""
    print(f"🔍 测试数据库连接")
    print("="*30)
    
    try:
        with get_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                print(f"✅ 数据库连接成功")
                print(f"MySQL版本: {version}")
                
                cursor.execute("SELECT DATABASE()")
                database = cursor.fetchone()[0]
                print(f"当前数据库: {database}")
                
                return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 数据库表结构诊断工具")
    print("="*60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试连接
    if not test_connection():
        return False
    
    # 检查表结构
    tables_to_check = ['ds_case_relation_graph', 'ds_case_details']
    
    for table in tables_to_check:
        check_table_structure(table)
    
    # 检查缺失字段
    check_missing_fields()
    
    # 生成修复SQL
    generate_fix_sql()
    
    print(f"\n📋 诊断完成")
    print("="*30)
    print("如果发现缺失字段，请执行以下步骤:")
    print("1. 查看生成的 fix_missing_fields.sql 文件")
    print("2. 在MySQL中执行相应的ALTER TABLE语句")
    print("3. 重新运行主程序")
    
    return True

if __name__ == "__main__":
    main()
