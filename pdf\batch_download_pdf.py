#!/usr/bin/env python3
"""
批量PDF下载脚本
支持下载多个PDF文件和配置不同的服务器
"""

import os
import sys
import requests
import webbrowser
from datetime import datetime
import json
import time

class BatchPDFDownloader:
    def __init__(self, config_file="pdf_download_config.json"):
        self.config_file = config_file
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "server_base_url": "http://***********:9999",
            "download_dir": "/bigai/ai/AJagent-main/download",
            "pdf_files": [
                {
                    "filename": "test.pdf",
                    "description": "测试PDF文件"
                }
            ],
            "settings": {
                "timeout": 30,
                "chunk_size": 8192,
                "auto_open_browser": False,
                "add_timestamp_if_exists": True
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"✅ 加载配置文件: {self.config_file}")
            else:
                self.config = default_config
                self.save_config()
                print(f"✅ 创建默认配置文件: {self.config_file}")
        except Exception as e:
            print(f"⚠️ 加载配置失败，使用默认配置: {e}")
            self.config = default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ 保存配置失败: {e}")
    
    def ensure_download_dir(self):
        """确保下载目录存在"""
        download_dir = self.config["download_dir"]
        try:
            if not os.path.exists(download_dir):
                os.makedirs(download_dir, exist_ok=True)
                print(f"✅ 创建下载目录: {download_dir}")
            return True
        except Exception as e:
            print(f"❌ 创建下载目录失败: {e}")
            return False
    
    def build_url(self, filename, action="download"):
        """构建PDF访问URL"""
        base_url = self.config["server_base_url"]
        return f"{base_url}/api/v1/pdfHandler/getPdf?filename={filename}&action={action}"
    
    def download_single_pdf(self, pdf_info):
        """下载单个PDF文件"""
        filename = pdf_info["filename"]
        description = pdf_info.get("description", filename)
        
        print(f"\n📄 下载: {description} ({filename})")
        print("-" * 50)
        
        try:
            # 构建URL
            download_url = self.build_url(filename, "download")
            view_url = self.build_url(filename, "view")
            
            print(f"🔗 下载URL: {download_url}")
            
            # 可选：在浏览器中打开
            if self.config["settings"]["auto_open_browser"]:
                print(f"🌐 在浏览器中打开...")
                webbrowser.open(view_url)
                time.sleep(1)
            
            # 发送请求
            response = self.session.get(
                download_url, 
                stream=True, 
                timeout=self.config["settings"]["timeout"]
            )
            response.raise_for_status()
            
            # 构建文件路径
            download_dir = self.config["download_dir"]
            filepath = os.path.join(download_dir, filename)
            
            # 如果文件已存在且设置了添加时间戳
            if os.path.exists(filepath) and self.config["settings"]["add_timestamp_if_exists"]:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name, ext = os.path.splitext(filename)
                new_filename = f"{name}_{timestamp}{ext}"
                filepath = os.path.join(download_dir, new_filename)
                print(f"⚠️ 文件已存在，重命名为: {new_filename}")
            
            # 下载文件
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            chunk_size = self.config["settings"]["chunk_size"]
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 显示进度
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r📥 下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
                        else:
                            print(f"\r📥 已下载: {downloaded_size} bytes", end='')
            
            print(f"\n✅ 下载完成: {os.path.basename(filepath)}")
            print(f"📊 文件大小: {downloaded_size} bytes")
            
            return {
                "success": True,
                "filename": filename,
                "filepath": filepath,
                "size": downloaded_size
            }
            
        except requests.exceptions.RequestException as e:
            print(f"\n❌ 网络请求失败: {e}")
            return {"success": False, "filename": filename, "error": str(e)}
        except Exception as e:
            print(f"\n❌ 下载失败: {e}")
            return {"success": False, "filename": filename, "error": str(e)}
    
    def download_all(self):
        """下载所有PDF文件"""
        print("🚀 批量PDF下载器启动")
        print("=" * 60)
        
        # 显示配置信息
        print(f"🌐 服务器: {self.config['server_base_url']}")
        print(f"📁 下载目录: {self.config['download_dir']}")
        print(f"📄 文件数量: {len(self.config['pdf_files'])}")
        
        # 确保下载目录存在
        if not self.ensure_download_dir():
            return False
        
        # 下载统计
        results = []
        success_count = 0
        total_size = 0
        
        # 逐个下载
        for i, pdf_info in enumerate(self.config["pdf_files"], 1):
            print(f"\n📋 [{i}/{len(self.config['pdf_files'])}]", end=" ")
            
            result = self.download_single_pdf(pdf_info)
            results.append(result)
            
            if result["success"]:
                success_count += 1
                total_size += result["size"]
            
            # 短暂延迟，避免请求过快
            if i < len(self.config["pdf_files"]):
                time.sleep(0.5)
        
        # 显示总结
        print(f"\n🎉 批量下载完成!")
        print("=" * 60)
        print(f"✅ 成功: {success_count}/{len(self.config['pdf_files'])}")
        print(f"📊 总大小: {total_size} bytes ({total_size/1024/1024:.2f} MB)")
        print(f"📁 保存目录: {self.config['download_dir']}")
        
        # 显示详细结果
        print(f"\n📋 详细结果:")
        for result in results:
            if result["success"]:
                print(f"  ✅ {result['filename']} - {result['size']} bytes")
            else:
                print(f"  ❌ {result['filename']} - {result['error']}")
        
        return success_count == len(self.config["pdf_files"])
    
    def add_pdf_file(self, filename, description=""):
        """添加PDF文件到下载列表"""
        pdf_info = {
            "filename": filename,
            "description": description or filename
        }
        
        # 检查是否已存在
        for existing in self.config["pdf_files"]:
            if existing["filename"] == filename:
                print(f"⚠️ 文件已存在于下载列表: {filename}")
                return False
        
        self.config["pdf_files"].append(pdf_info)
        self.save_config()
        print(f"✅ 添加文件到下载列表: {filename}")
        return True
    
    def list_pdf_files(self):
        """列出所有PDF文件"""
        print("📋 当前下载列表:")
        for i, pdf_info in enumerate(self.config["pdf_files"], 1):
            print(f"  {i}. {pdf_info['filename']} - {pdf_info['description']}")

def main():
    downloader = BatchPDFDownloader()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "add" and len(sys.argv) >= 3:
            filename = sys.argv[2]
            description = sys.argv[3] if len(sys.argv) > 3 else ""
            downloader.add_pdf_file(filename, description)
        
        elif command == "list":
            downloader.list_pdf_files()
        
        elif command == "download":
            downloader.download_all()
        
        else:
            print("用法:")
            print("  python batch_download_pdf.py download    # 下载所有文件")
            print("  python batch_download_pdf.py list        # 列出文件列表")
            print("  python batch_download_pdf.py add <filename> [description]  # 添加文件")
    else:
        # 默认执行下载
        downloader.download_all()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
