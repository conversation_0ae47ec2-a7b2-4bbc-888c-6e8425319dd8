<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件详细信息列表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }
        .title {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .nav-links {
            display: flex;
            gap: 15px;
        }
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            padding: 8px 16px;
            border: 1px solid #007bff;
            border-radius: 4px;
            transition: all 0.3s;
        }
        .nav-links a:hover {
            background-color: #007bff;
            color: white;
        }
        .search-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        .search-form input, .search-form select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .search-form button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .search-form button:hover {
            background-color: #0056b3;
        }
        .stats {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
            word-wrap: break-word;
            max-width: 150px;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #e8f4f8;
        }
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .text-full {
            white-space: normal;
            word-break: break-all;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-decoration: none;
            color: #333;
            border-radius: 4px;
        }
        .pagination a:hover {
            background-color: #007bff;
            color: white;
        }
        .pagination .current {
            background-color: #007bff;
            color: white;
        }
        .detail-link {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .detail-link:hover {
            text-decoration: underline;
        }
        .toggle-btn {
            background: none;
            border: none;
            color: #007bff;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #007bff;
        }
        .toggle-btn:hover {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">案件详细信息列表</h1>
            <div class="nav-links">
                <a href="/">案件关系图</a>
                <a href="/case_details">详细信息</a>
            </div>
        </div>

        <!-- 搜索表单 -->
        <form class="search-form" method="GET">
            <input type="text" name="search" placeholder="搜索案件编号、案件名称、姓名..." 
                   value="{{ search_term }}" style="min-width: 200px;">
            <input type="text" name="batchid" placeholder="批次号" 
                   value="{{ batchid_filter }}" style="min-width: 120px;">
            <input type="text" name="ajbh" placeholder="案件编号" 
                   value="{{ ajbh_filter }}" style="min-width: 120px;">
            <select name="per_page">
                <option value="20" {% if per_page == 20 %}selected{% endif %}>每页20条</option>
                <option value="50" {% if per_page == 50 %}selected{% endif %}>每页50条</option>
                <option value="100" {% if per_page == 100 %}selected{% endif %}>每页100条</option>
            </select>
            <button type="submit">搜索</button>
            <a href="/case_details" style="margin-left: 10px;">清空</a>
        </form>

        <!-- 统计信息 -->
        <div class="stats">
            <strong>统计信息：</strong>
            共找到 <span style="color: #007bff; font-weight: bold;">{{ total_records }}</span> 条详细信息记录，
            当前第 <span style="color: #007bff; font-weight: bold;">{{ page }}</span> 页，
            共 <span style="color: #007bff; font-weight: bold;">{{ total_pages }}</span> 页
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>批次号</th>
                        <th>案件编号</th>
                        <th>案件名称</th>
                        <th>实体类型</th>
                        <th>姓名/代号</th>
                        <th>性别</th>
                        <th>年龄</th>
                        <th>身份证号</th>
                        <th>户籍地</th>
                        <th>文化程度</th>
                        <th>直接上级</th>
                        <th>所属公司</th>
                        <th>所属组织</th>
                        <th>分工角色</th>
                        <th>主要职责</th>
                        <th>横向关联人物</th>
                        <th>横向关联关系</th>
                        <th>纵向关联人物</th>
                        <th>纵向关联关系</th>
                        <th>关联工具</th>
                        <th>关联物品</th>
                        <th>关联犯罪行为</th>
                        <th>关联场所</th>
                        <th>强制措施</th>
                        <th>司法处置结果</th>
                        <th>经济收益</th>
                        <th>前科</th>
                        <th>数据时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in details %}
                    <tr>
                        <td>{{ detail.id }}</td>
                        <td class="text-truncate" title="{{ detail.batchid }}">{{ detail.batchid }}</td>
                        <td class="text-truncate" title="{{ detail.ajbh }}">{{ detail.ajbh }}</td>
                        <td class="text-truncate" title="{{ detail.ajmc or '' }}">{{ detail.ajmc or '' }}</td>
                        <td>{{ detail.entity_type or '' }}</td>
                        <td>{{ detail.name_code or '' }}</td>
                        <td>{{ detail.gender or '' }}</td>
                        <td>{{ detail.age or '' }}</td>
                        <td class="text-truncate" title="{{ detail.id_card or '' }}">{{ detail.id_card or '' }}</td>
                        <td class="text-truncate" title="{{ detail.residence or '' }}">{{ detail.residence or '' }}</td>
                        <td>{{ detail.education or '' }}</td>
                        <td>{{ detail.direct_superior or '' }}</td>
                        <td class="text-truncate" title="{{ detail.company or '' }}">{{ detail.company or '' }}</td>
                        <td class="text-truncate" title="{{ detail.organization or '' }}">{{ detail.organization or '' }}</td>
                        <td>{{ detail.role or '' }}</td>
                        <td>{{ detail.responsibilities or '' }}</td>
                        <td>{{ detail.peers_name or '' }}</td>
                        <td>{{ detail.peers or '' }}</td>
                        <td>{{ detail.vertical_name or '' }}</td>
                        <td>{{ detail.vertical or '' }}</td>
                        <td class="text-truncate" title="{{ detail.related_tools or '' }}">{{ detail.related_tools or '' }}</td>
                        <td class="text-truncate" title="{{ detail.related_items or '' }}">{{ detail.related_items or '' }}</td>
                        <td class="text-truncate" title="{{ detail.related_actions or '' }}">{{ detail.related_actions or '' }}</td>
                        <td class="text-truncate" title="{{ detail.related_locations or '' }}">{{ detail.related_locations or '' }}</td>
                        <td>{{ detail.measures or '' }}</td>
                        <td>{{ detail.judicial_result or '' }}</td>
                        <td>{{ detail.economic or '' }}</td>
                        <td>{{ detail.criminal or '' }}</td>
                        <td class="text-truncate" title="{{ detail.data_time_formatted }}">{{ detail.data_time_formatted }}</td>
                        <td>{{ detail.isdelete_text }}</td>
                        <td>
                            <a href="/case_details/{{ detail.id }}" class="detail-link">查看详情</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if total_pages > 1 %}
        <div class="pagination">
            {% if page > 1 %}
                <a href="?page=1&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">首页</a>
                <a href="?page={{ page - 1 }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">上一页</a>
            {% endif %}
            
            {% for p in range(max(1, page - 2), min(total_pages + 1, page + 3)) %}
                {% if p == page %}
                    <span class="current">{{ p }}</span>
                {% else %}
                    <a href="?page={{ p }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">{{ p }}</a>
                {% endif %}
            {% endfor %}
            
            {% if page < total_pages %}
                <a href="?page={{ page + 1 }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">下一页</a>
                <a href="?page={{ total_pages }}&per_page={{ per_page }}&search={{ search_term }}&batchid={{ batchid_filter }}&ajbh={{ ajbh_filter }}">末页</a>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <script>
        // 切换文本显示模式
        function toggleText(element) {
            const cell = element.parentElement;
            const isExpanded = cell.classList.contains('text-full');
            
            if (isExpanded) {
                cell.classList.remove('text-full');
                cell.classList.add('text-truncate');
                element.textContent = '展开';
            } else {
                cell.classList.remove('text-truncate');
                cell.classList.add('text-full');
                element.textContent = '收起';
            }
        }
    </script>
</body>
</html>
