{% extends "base.html" %}

{% block title %}案件详情 - {{ case.ajbh }} - 案件关系图查看器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 返回按钮 -->
        <div class="mb-3">
            <a href="/relations" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回案件关系数据
            </a>
            <a href="/case_details" class="btn btn-outline-info ms-2">
                <i class="bi bi-table"></i> 查看详细信息
            </a>
        </div>

        <!-- 案件基本信息 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-file-text"></i> 案件基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="120">批次号:</td>
                                <td>{{ case.batchid or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">案件编号:</td>
                                <td>{{ case.ajbh }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">案件名称:</td>
                                <td>{{ case.ajmc or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">案件类型:</td>
                                <td>
                                    <span class="badge bg-info">{{ case.ajlx or 'N/A' }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">同步时间:</td>
                                <td>{{ case.tbrksj_formatted }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="120">处理状态:</td>
                                <td>
                                    <span class="badge {{ case.status_class }}">
                                        {{ case.status_text }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">修改人员:</td>
                                <td>{{ case.updater or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">修改类型:</td>
                                <td>
                                    <span class="badge {% if case.updatetype == '1' %}bg-success{% else %}bg-secondary{% endif %}">
                                        {{ case.updatetype_text }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">重跑次数:</td>
                                <td>
                                    {% if case.nums %}
                                    <span class="badge bg-warning">{{ case.nums }}</span>
                                    {% else %}
                                    <span class="text-muted">0</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间信息 -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-clock"></i> 处理时间信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="bi bi-play-circle display-6 text-primary"></i>
                            <h6 class="mt-2">开始时间</h6>
                            <p class="text-muted">{{ case.starttime_formatted }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="bi bi-check-circle display-6 text-success"></i>
                            <h6 class="mt-2">完成时间</h6>
                            <p class="text-muted">{{ case.endtime_formatted }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="bi bi-arrow-clockwise display-6 text-warning"></i>
                            <h6 class="mt-2">更新时间</h6>
                            <p class="text-muted">{{ case.updatetime_formatted }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文书下载地址 -->
        {% if case.flwsxzdz %}
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="bi bi-download"></i> 法律文书下载地址
                </h6>
            </div>
            <div class="card-body">
                <div class="input-group">
                    <input type="text" class="form-control" value="{{ case.flwsxzdz }}" readonly>
                    <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('{{ case.flwsxzdz }}')">
                        <i class="bi bi-clipboard"></i> 复制
                    </button>
                    <a href="{{ case.flwsxzdz }}" class="btn btn-primary" target="_blank">
                        <i class="bi bi-box-arrow-up-right"></i> 打开
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 错误信息 -->
        {% if case.error %}
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i> 错误信息
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <pre style="white-space: pre-wrap; font-family: inherit;">{{ case.error }}</pre>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 案件内容 -->
        {% if case.ajnr %}
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="bi bi-file-earmark-text"></i> 案件内容
                </h6>
            </div>
            <div class="card-body">
                <div class="border rounded p-3" style="background-color: #f8f9fa; max-height: 400px; overflow-y: auto;">
                    <pre style="white-space: pre-wrap; font-family: inherit;">{{ case.ajnr }}</pre>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 关系图代码 -->
        <div class="row">
            {% if case.has_mermaid %}
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="bi bi-diagram-3"></i> 最终关系图代码
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <small class="text-muted">lastcode</small>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="copyCode('lastcode')">
                                    <i class="bi bi-clipboard"></i> 复制
                                </button>
                                <a href="{{ url_for('view_mermaid', ajbh=case.ajbh) }}"
                                   class="btn btn-sm btn-primary" target="_blank"
                                   title="使用与streamlit_app.py相同的URL创建方式">
                                    <i class="bi bi-diagram-3"></i> 查看图表
                                </a>
                                <a href="{{ url_for('mermaid_debug', ajbh=case.ajbh) }}"
                                   class="btn btn-sm btn-outline-info" target="_blank"
                                   title="调试URL创建过程">
                                    <i class="bi bi-bug"></i> 调试
                                </a>
                            </div>
                        </div>
                        <div class="border rounded p-2" style="background-color: #f8f9fa; max-height: 300px; overflow-y: auto;">
                            <pre id="lastcode" style="white-space: pre-wrap; font-size: 12px;">{{ case.lastcode }}</pre>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% if case.has_original_code %}
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="bi bi-code-slash"></i> 原始关系图代码
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <small class="text-muted">code</small>
                            <button class="btn btn-sm btn-outline-primary" onclick="copyCode('code')">
                                <i class="bi bi-clipboard"></i> 复制
                            </button>
                        </div>
                        <div class="border rounded p-2" style="background-color: #f8f9fa; max-height: 300px; overflow-y: auto;">
                            <pre id="code" style="white-space: pre-wrap; font-size: 12px;">{{ case.code }}</pre>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        {% if not case.has_mermaid and not case.has_original_code %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-diagram-3 display-1 text-muted"></i>
                <h5 class="text-muted mt-3">暂无关系图代码</h5>
                <p class="text-muted">该案件尚未生成关系图代码</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showToast('已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// 复制代码
function copyCode(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    copyToClipboard(text);
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('已复制到剪贴板');
        } else {
            showToast('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    }
    
    document.body.removeChild(textArea);
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
