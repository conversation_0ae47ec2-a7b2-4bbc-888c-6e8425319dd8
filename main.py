import sys
import logging
import streamlit.web.cli as stcli
from pathlib import Path
import os

def setup_logging():
    """设置日志配置"""
    # 创建logs目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "case_analysis.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=== 案件信息提取分析助手启动 ===")
    return logger

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'streamlit',
        'pandas',
        'pymysql',
        'openpyxl',  # 用于读取Excel文件
        'matplotlib',
        'docker',    # 用于Mermaid图表生成
        'autogen-agentchat',
        'autogen-ext'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def create_required_directories():
    """创建必需的目录"""
    directories = ["outputs", "logs", "temp"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
    print("📁 创建必需目录完成")

def main():
    """主函数"""
    print("🚀 正在启动多案件信息提取分析助手...")

    # 设置日志
    logger = setup_logging()

    # 检查依赖
    if not check_dependencies():
        return 1

    # 创建目录
    create_required_directories()

    # 检查Docker环境
    try:
        import docker
        client = docker.from_env()
        client.ping()
        print("✅ Docker环境检查通过")
    except Exception as e:
        print(f"⚠️ Docker环境检查失败: {e}")
        print("   关系图生成将使用备用方案")

    # 设置环境变量
    os.environ["STREAMLIT_SERVER_PORT"] = "8502"
    os.environ["STREAMLIT_SERVER_ADDRESS"] = "0.0.0.0"
    
    try:
        # 启动Streamlit应用
        logger.info("启动多案件分析Streamlit Web应用...")
        print("📊 多案件信息提取分析助手")
        print("   - 支持 xlsx/xls/csv 文件批量处理")
        print("   - 支持最大20个案件并发处理")
        print("   - 自动生成人物关系图和分析报告")
        print("   - 支持批量数据库导入")
        print(f"   - 访问地址: http://localhost:8502")

        # 构建Streamlit命令行参数
        sys.argv = [
            "streamlit",
            "run",
            "streamlit_app.py",
            "--server.port=8502",
            "--server.address=0.0.0.0",
            "--server.headless=true",
            "--server.enableCORS=false",
            "--server.enableXsrfProtection=false",
            "--browser.gatherUsageStats=false"
        ]

        # 启动应用
        stcli.main()
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭应用...")
        print("\n👋 应用已关闭")
        return 0
    
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
