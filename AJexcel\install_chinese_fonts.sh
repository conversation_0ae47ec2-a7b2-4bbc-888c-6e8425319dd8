#!/bin/bash

echo "🔤 安装中文字体支持"
echo "===================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  建议以root用户运行此脚本以安装系统字体"
    echo "   sudo bash install_chinese_fonts.sh"
    echo ""
fi

# 检测系统类型
if [ -f /etc/redhat-release ]; then
    SYSTEM="centos"
    echo "🐧 检测到CentOS/RHEL系统"
elif [ -f /etc/debian_version ]; then
    SYSTEM="ubuntu"
    echo "🐧 检测到Ubuntu/Debian系统"
else
    SYSTEM="unknown"
    echo "🐧 未知系统类型，尝试通用安装方法"
fi

# 安装中文字体
install_fonts() {
    echo ""
    echo "📦 安装中文字体包..."
    
    case $SYSTEM in
        "centos")
            # CentOS/RHEL
            yum install -y wqy-microhei-fonts wqy-zenhei-fonts
            if [ $? -eq 0 ]; then
                echo "✅ CentOS字体安装完成"
            else
                echo "❌ CentOS字体安装失败"
                return 1
            fi
            ;;
        "ubuntu")
            # Ubuntu/Debian
            apt-get update
            apt-get install -y fonts-wqy-microhei fonts-wqy-zenhei
            if [ $? -eq 0 ]; then
                echo "✅ Ubuntu字体安装完成"
            else
                echo "❌ Ubuntu字体安装失败"
                return 1
            fi
            ;;
        *)
            echo "⚠️  未知系统，请手动安装中文字体"
            return 1
            ;;
    esac
}

# 创建用户字体目录并下载字体
install_user_fonts() {
    echo ""
    echo "📁 在用户目录安装字体..."
    
    # 创建用户字体目录
    FONT_DIR="$HOME/.fonts"
    mkdir -p "$FONT_DIR"
    
    # 下载常用中文字体（如果可以访问）
    cd "$FONT_DIR"
    
    # 尝试下载文泉驿字体
    echo "🔄 尝试下载文泉驿微米黑字体..."
    if command -v wget >/dev/null 2>&1; then
        wget -q --timeout=10 "https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansCN.zip" -O SourceHanSansCN.zip 2>/dev/null
        if [ $? -eq 0 ] && [ -f SourceHanSansCN.zip ]; then
            unzip -q SourceHanSansCN.zip
            rm SourceHanSansCN.zip
            echo "✅ 思源黑体下载完成"
        else
            echo "⚠️  网络下载失败，跳过在线字体下载"
        fi
    fi
    
    # 更新字体缓存
    if command -v fc-cache >/dev/null 2>&1; then
        fc-cache -fv "$FONT_DIR" >/dev/null 2>&1
        echo "✅ 字体缓存已更新"
    fi
}

# 检查字体安装情况
check_fonts() {
    echo ""
    echo "🔍 检查已安装的中文字体..."
    
    if command -v fc-list >/dev/null 2>&1; then
        # 检查常见中文字体
        fonts_found=0
        
        if fc-list | grep -i "wqy" >/dev/null 2>&1; then
            echo "✅ 找到文泉驿字体"
            fonts_found=$((fonts_found + 1))
        fi
        
        if fc-list | grep -i "source.*han" >/dev/null 2>&1; then
            echo "✅ 找到思源字体"
            fonts_found=$((fonts_found + 1))
        fi
        
        if fc-list | grep -i "noto.*cjk" >/dev/null 2>&1; then
            echo "✅ 找到Noto CJK字体"
            fonts_found=$((fonts_found + 1))
        fi
        
        if [ $fonts_found -eq 0 ]; then
            echo "⚠️  未找到中文字体，PDF可能显示乱码"
            return 1
        else
            echo "✅ 找到 $fonts_found 种中文字体"
            return 0
        fi
    else
        echo "⚠️  无法检查字体（fc-list命令不可用）"
        return 1
    fi
}

# 创建简单的测试字体文件
create_test_font() {
    echo ""
    echo "📝 创建测试字体文件..."
    
    # 在当前目录创建一个简单的字体映射文件
    cat > font_test.py << 'EOF'
#!/usr/bin/env python3
import os
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

def test_fonts():
    """测试可用字体"""
    font_paths = [
        '/usr/share/fonts/wqy-microhei/wqy-microhei.ttc',
        '/usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc',
        '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
        '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
        os.path.expanduser('~/.fonts/SourceHanSansCN-Regular.otf'),
    ]
    
    print("🔤 测试字体文件:")
    found_fonts = []
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                pdfmetrics.registerFont(TTFont('TestFont', font_path))
                print(f"✅ {font_path}")
                found_fonts.append(font_path)
            except Exception as e:
                print(f"❌ {font_path}: {e}")
        else:
            print(f"⚠️  {font_path}: 文件不存在")
    
    if found_fonts:
        print(f"\n🎉 找到 {len(found_fonts)} 个可用字体文件")
        return True
    else:
        print(f"\n❌ 未找到可用的字体文件")
        return False

if __name__ == "__main__":
    test_fonts()
EOF
    
    chmod +x font_test.py
    echo "✅ 字体测试脚本已创建: font_test.py"
}

# 主函数
main() {
    echo ""
    echo "开始安装中文字体支持..."
    
    # 尝试系统级安装
    if [ "$EUID" -eq 0 ]; then
        install_fonts
    else
        echo "⚠️  非root用户，跳过系统字体安装"
    fi
    
    # 用户级安装
    install_user_fonts
    
    # 检查安装结果
    check_fonts
    font_check_result=$?
    
    # 创建测试脚本
    create_test_font
    
    echo ""
    echo "📋 安装完成总结:"
    if [ $font_check_result -eq 0 ]; then
        echo "✅ 中文字体安装成功"
        echo "✅ PDF工具应该可以正常显示中文"
    else
        echo "⚠️  中文字体安装可能不完整"
        echo "⚠️  PDF可能使用默认字体（英文）"
    fi
    
    echo ""
    echo "🧪 测试字体:"
    echo "   python font_test.py"
    echo ""
    echo "🚀 运行PDF工具:"
    echo "   python main_excel_to_pdf.py your_file.xlsx output_dir"
}

# 执行主函数
main
