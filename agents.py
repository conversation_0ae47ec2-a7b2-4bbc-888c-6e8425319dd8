################agents.py##########
import asyncio
import logging
import json
import uuid
import re
import base64
import io
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional
import pandas as pd
import pymysql
from pathlib import Path
import docx
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform 
 
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken

import docker
import os
import tempfile
import shutil
import re
import time

# 设置matplotlib中文字体
def setup_matplotlib_font():
    """设置matplotlib中文字体"""
    try:
        system = platform.system()
        if system == "Windows":
            # Windows系统
            font_names = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
        elif system == "Darwin":  # macOS
            font_names = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
        else:  # Linux
            font_names = ['WenQuanYi Micro Hei', 'SimSun', 'DejaVu Sans']
        
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        
        for font_name in font_names:
            if font_name in available_fonts:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                break
        else:
            # 如果没有找到中文字体，使用默认字体并设置unicode处理
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
    except Exception as e:
        # 如果设置字体失败，使用默认设置
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

# 初始化字体设置
setup_matplotlib_font()

class SessionManager:
    """会话管理器"""
    
    def __init__(self, base_dir: str = "sessions"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
    
    def create_session_dir(self, session_id: str) -> Path:
        """创建会话目录"""
        session_dir = self.base_dir / session_id
        session_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (session_dir / "uploads").mkdir(exist_ok=True)
        (session_dir / "outputs").mkdir(exist_ok=True)
        (session_dir / "reports").mkdir(exist_ok=True)
        (session_dir / "data").mkdir(exist_ok=True)
        
        return session_dir
    
    def get_session_dir(self, session_id: str) -> Path:
        """获取会话目录"""
        session_dir = self.base_dir / session_id
        if not session_dir.exists():
            return self.create_session_dir(session_id)
        return session_dir
    
    def save_uploaded_file(self, session_id: str, file_content: bytes, original_filename: str) -> str:
        """保存上传的文件，保持原始文件名"""
        session_dir = self.get_session_dir(session_id)
        file_path = session_dir / "uploads" / original_filename
        
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        return str(file_path)
    
    def save_session_data(self, session_id: str, data: Dict[str, Any]):
        """保存会话数据"""
        session_dir = self.get_session_dir(session_id)
        data_file = session_dir / "data" / "session_data.json"
        
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def load_session_data(self, session_id: str) -> Dict[str, Any]:
        """加载会话数据"""
        session_dir = self.get_session_dir(session_id)
        data_file = session_dir / "data" / "session_data.json"
        
        if data_file.exists():
            with open(data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self):
        self.config = {
            'host': '*************',
            'user': 'root',
            'password': '123456',
            'database': 'mydb',
            'charset': 'utf8mb4'
        }
        
        # 设置logging级别，减少不必要的日志
        logging.getLogger('pymysql').setLevel(logging.WARNING)
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.config)
            return connection
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            raise
    
    def execute_query(self, sql: str) -> Dict[str, Any]:
        """执行SQL查询"""
        try:
            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql)
                    if sql.strip().upper().startswith('INSERT'):
                        connection.commit()
                        return {
                            "status": "success",
                            "message": f"成功插入 {cursor.rowcount} 条记录",
                            "affected_rows": cursor.rowcount,
                            "sql": sql
                        }
                    else:
                        results = cursor.fetchall()
                        return {
                            "status": "success",
                            "data": results,
                            "sql": sql,
                            "count": len(results)
                        }
        except Exception as e:
            logging.error(f"SQL执行失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "sql": sql,
                "data": None
            }

class ModelManager:
    """大模型管理器"""
    
    def __init__(self):
        # 设置OpenAI客户端日志级别
        logging.getLogger('openai._base_client').setLevel(logging.WARNING)
        
        self.model_client = OpenAIChatCompletionClient(
            model="Qwen3-32B",
            base_url="http://192.168.2.240:8011/v1",
            api_key="jc888",
            model_info=ModelInfo(
                family="openai",
                vision=True,
                function_calling=True,
                json_output=True
            )
        ) 
    
    def get_client(self):
        return self.model_client

class ExcelFileProcessor:
    """Excel/CSV文件处理器 - 支持多案件文件处理"""

    @staticmethod
    def read_excel_file(file_path: str) -> tuple[pd.DataFrame, Dict[str, Any]]:
        """读取Excel/CSV文件并返回DataFrame和文件信息"""
        file_path = Path(file_path)
        file_info = {
            "original_name": file_path.name,
            "file_size": file_path.stat().st_size,
            "file_type": file_path.suffix.lower().lstrip('.'),
            "modified_time": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
        }

        try:
            # 根据文件类型读取数据
            if file_info["file_type"] in ['xlsx', 'xls']:
                df = pd.read_excel(file_path)
            elif file_info["file_type"] == 'csv':
                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                if df is None:
                    raise ValueError("无法读取CSV文件，编码格式不支持")
            else:
                raise ValueError(f"不支持的文件类型: {file_info['file_type']}")

            # 添加数据统计信息
            file_info["total_rows"] = len(df)
            file_info["total_columns"] = len(df.columns)
            file_info["columns"] = df.columns.tolist()

            return df, file_info

        except Exception as e:
            logging.error(f"文件读取失败: {e}")
            raise

    @staticmethod
    def preprocess_case_data(df: pd.DataFrame) -> Dict[str, Any]:
        """预处理案件数据：去重、合并等"""
        try:
            original_count = len(df)

            # 1. 数据去重：每个字段的数据都相同的情况下，取其中一行
            df_deduplicated = df.drop_duplicates()
            deduplicated_count = len(df_deduplicated)

            # 2. 按案件编号分组处理
            if '案件编号' not in df_deduplicated.columns:
                raise ValueError("数据中缺少'案件编号'字段")

            # 需要合并的内容字段
            content_fields = ['正文内容', '到案情况', '依法侦查查明', '犯罪证据', '综上所述', '其他说明']
            # 需要取最大版本号的字段
            version_fields = ['案件名称', '承办单位']

            processed_cases = []

            for case_id, group in df_deduplicated.groupby('案件编号'):
                if len(group) == 1:
                    # 只有一条记录，直接使用
                    case_data = group.iloc[0].to_dict()
                else:
                    # 多条记录需要合并
                    # 获取数据版本号最大的记录作为基础
                    if '数据版本号' in group.columns:
                        base_record = group.loc[group['数据版本号'].idxmax()]
                    else:
                        base_record = group.iloc[0]

                    case_data = base_record.to_dict()

                    # 合并内容字段
                    merged_content = []
                    for field in content_fields:
                        if field in group.columns:
                            field_contents = group[field].dropna().astype(str)
                            field_contents = field_contents[field_contents != 'nan']
                            if len(field_contents) > 0:
                                merged_content.extend(field_contents.tolist())

                    case_data['案件内容'] = '\n\n'.join(merged_content) if merged_content else ''

                # 确保必要字段存在
                if '案件内容' not in case_data:
                    # 如果没有合并的案件内容，尝试从现有字段构建
                    content_parts = []
                    for field in content_fields:
                        if field in case_data and pd.notna(case_data[field]):
                            content_parts.append(str(case_data[field]))
                    case_data['案件内容'] = '\n\n'.join(content_parts)

                processed_cases.append(case_data)

            # 转换为DataFrame
            processed_df = pd.DataFrame(processed_cases)

            # 确保必要的字段存在
            required_fields = ['案件编号', '案件名称', '承办单位', '案件内容']
            for field in required_fields:
                if field not in processed_df.columns:
                    processed_df[field] = ''

            return {
                "status": "success",
                "processed_data": processed_df,
                "original_count": original_count,
                "deduplicated_count": deduplicated_count,
                "final_count": len(processed_df),
                "processing_summary": f"原始数据{original_count}行 -> 去重后{deduplicated_count}行 -> 合并后{len(processed_df)}个案件"
            }

        except Exception as e:
            logging.error(f"数据预处理失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

class FileProcessor:
    """文件处理器 - 保持向后兼容"""

    @staticmethod
    def read_file(file_path: str) -> tuple[str, Dict[str, Any]]:
        """读取文件内容并返回文件信息"""
        file_path = Path(file_path)
        file_info = {
            "original_name": file_path.name,
            "file_size": file_path.stat().st_size,
            "file_type": file_path.suffix.lower().lstrip('.'),
            "modified_time": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
        }

        try:
            if file_info["file_type"] == 'docx':
                doc = docx.Document(file_path)
                content = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        content.append(paragraph.text.strip())
                file_content = '\n'.join(content)
            elif file_info["file_type"] == 'txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()
            else:
                raise ValueError(f"不支持的文件类型: {file_info['file_type']}")

            file_info["char_count"] = len(file_content)
            file_info["line_count"] = len(file_content.split('\n'))

            return file_content, file_info
        except Exception as e:
            logging.error(f"文件读取失败: {e}")
            raise

class BatchCaseExtractionAgent:
    """批量案件要素提取智能体 - 支持并发处理多个案件"""

    def __init__(self, model_manager: ModelManager, session_manager: SessionManager, max_concurrent: int = 10):
        self.model_client = model_manager.get_client()
        self.session_manager = session_manager
        self.max_concurrent = max_concurrent
        self.agent = AssistantAgent(
            name="batch_case_extractor",
            model_client=self.model_client,
            system_message=self._get_system_message()
        )

    async def extract_multiple_cases(self, cases_data: List[Dict[str, Any]], batch_id: str, session_id: str,
                                   progress_callback=None) -> Dict[str, Any]:
        """并发提取多个案件的信息"""
        try:
            total_cases = len(cases_data)
            if total_cases == 0:
                return {
                    "status": "error",
                    "message": "没有案件数据需要处理"
                }

            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(self.max_concurrent)

            # 创建任务列表
            tasks = []
            for i, case_data in enumerate(cases_data):
                task = self._extract_single_case_with_semaphore(
                    semaphore, case_data, batch_id, session_id, i, progress_callback
                )
                tasks.append(task)

            # 并发执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            successful_results = []
            failed_results = []

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_results.append({
                        "case_index": i,
                        "case_id": cases_data[i].get('案件编号', f'案件{i+1}'),
                        "error": str(result)
                    })
                elif result.get("status") == "success":
                    successful_results.append(result)
                else:
                    failed_results.append({
                        "case_index": i,
                        "case_id": cases_data[i].get('案件编号', f'案件{i+1}'),
                        "error": result.get("error", "未知错误")
                    })

            # 合并所有成功的CSV数据
            all_csv_data = []
            all_mermaid_codes = {}
            all_relationship_images = {}

            for result in successful_results:
                if result.get("csv_data"):
                    # 解析CSV数据并添加到总列表
                    csv_lines = result["csv_data"].strip().split('\n')
                    if len(csv_lines) > 1:  # 跳过表头
                        all_csv_data.extend(csv_lines[1:])

                # 保存每个案件的Mermaid代码和关系图
                case_id = result.get("case_id", "")
                if result.get("mermaid_code"):
                    all_mermaid_codes[case_id] = result["mermaid_code"]
                if result.get("relationship_image"):
                    all_relationship_images[case_id] = result["relationship_image"]

            # 生成合并的CSV数据
            if all_csv_data:
                # 使用第一个成功结果的表头
                header = "batch_id,host_org,case_id,case_name,实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）"
                merged_csv = header + '\n' + '\n'.join(all_csv_data)
            else:
                merged_csv = ""

            return {
                "status": "success",
                "total_cases": total_cases,
                "successful_count": len(successful_results),
                "failed_count": len(failed_results),
                "merged_csv_data": merged_csv,
                "individual_results": successful_results,
                "failed_cases": failed_results,
                "mermaid_codes": all_mermaid_codes,
                "relationship_images": all_relationship_images,
                "batch_id": batch_id,
                "processing_summary": f"成功处理 {len(successful_results)}/{total_cases} 个案件"
            }

        except Exception as e:
            logging.error(f"批量案件提取失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def _extract_single_case_with_semaphore(self, semaphore: asyncio.Semaphore,
                                                case_data: Dict[str, Any], batch_id: str,
                                                session_id: str, case_index: int,
                                                progress_callback=None) -> Dict[str, Any]:
        """使用信号量控制的单个案件提取"""
        async with semaphore:
            try:
                # 调用进度回调
                if progress_callback:
                    await progress_callback(case_index, "processing", f"正在处理案件: {case_data.get('案件编号', f'案件{case_index+1}')}")

                result = await self._extract_single_case(case_data, batch_id, session_id)

                # 调用进度回调
                if progress_callback:
                    status = "completed" if result.get("status") == "success" else "failed"
                    await progress_callback(case_index, status, f"案件处理{'成功' if status == 'completed' else '失败'}")

                return result

            except Exception as e:
                if progress_callback:
                    await progress_callback(case_index, "failed", f"案件处理异常: {str(e)}")
                raise e

class CaseExtractionAgent:
    """案件要素提取智能体 - 保持向后兼容"""

    def __init__(self, model_manager: ModelManager, session_manager: SessionManager):
        self.model_client = model_manager.get_client()
        self.session_manager = session_manager
        self.agent = AssistantAgent(
            name="case_extractor",
            model_client=self.model_client,
            system_message=self._get_system_message()
        )
    
    def _get_system_message(self) -> str:
        return """你是一位走私案件资深分析专家，需要完成案件分析和要素提取。

任务目标：
1. 组织架构解析（主犯/从犯认定、分工逻辑）
2. 结构化数据提取（CSV格式，14列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的组织和所有提及到的人物信息：实体类型（人员或组织等），姓名/代号，性别，年龄，身份证号，户籍地/现居地，文化程度，直接上级，所属组织，组织层级，分工角色，关联工具/行为，司法处置结果，经济收益（元）

输出CSV格式（14列）：
实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织之间的多层级关系，生成Mermaid格式的关系图代码。

Mermaid中文文本格式要求：
1. 节点文本格式化：
   - 每行文本不超过6-8个中文字符
   - 姓名单独一行
   - 年龄、学历、状态等信息分行显示
   - 金额信息使用万元、千元等简化表示
   - 使用<br/>标签换行，确保文本在节点中正确换行

2. 节点格式示例（注意换行格式）：
   A["何海富<br/>42岁<br/>初中<br/>逮捕"] -->|指挥| B["李某某<br/>运输员<br/>28岁<br/>在逃"]

3. 完整示例：
   graph TD
       A["张某某<br/>组织者<br/>35岁<br/>已判刑"] -->|指挥| B["李某某<br/>运输员<br/>28岁<br/>在逃"]

4. 重要提醒：
   - 必须使用<br/>标签换行，不能使用\\n或\n
   - 节点内容必须用双引号包围
   - 示例正确格式："罗添文<br/>运输假烟司机<br/>29岁<br/>逮捕"
   - 示例错误格式："罗添文\\n运输假烟司机\\n29岁\\n逮捕" (绝对禁止)

   style A fill:#FFB6C1,stroke:#333
   style B fill:#87CEEB,stroke:#333

4. 样式设置：
   - 主犯：style A fill:#FFB6C1,stroke:#333
   - 从犯：style B fill:#87CEEB,stroke:#333
   - 物品：style C fill:#98FB98,stroke:#333
   - 场所：style D fill:#FFD700,stroke:#333
   - 工具：style E fill:#DDA0DD,stroke:#333

返回格式：
{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据",
    "mermaid_code": "Mermaid关系图代码"
}
"""
    
    async def extract_case_info(self, file_content: str, file_info: Dict[str, Any], batch_id: str, session_id: str) -> Dict[str, Any]:
        """提取案件信息"""
        try:
            case_name = Path(file_info["original_name"]).stem
            
            prompt = f"""
案件名称：{case_name}
批次号：{batch_id}
文件信息：{file_info["original_name"]} ({file_info['char_count']}字符)

案件内容：
{file_content}

请按照系统要求分析并提取案件信息。
"""
            
            response = await self.agent.on_messages(
                [TextMessage(content=prompt, source="user")],
                CancellationToken()
            )
            
            model_response = response.chat_message.content

            # 解析JSON响应
            try:
                # 先清理JSON字符串中的控制字符
                cleaned_response = self.clean_json_string(model_response)
                result = json.loads(cleaned_response)
            except json.JSONDecodeError:
                # 尝试提取JSON
                json_match = re.search(r'\{.*\}', model_response, re.DOTALL)
                if json_match:
                    try:
                        # 清理提取的JSON字符串
                        cleaned_json = self.clean_json_string(json_match.group(0))
                        result = json.loads(cleaned_json)
                    except json.JSONDecodeError:
                        # 如果清理后仍然解析失败，使用手动解析
                        result = self._parse_response_manually(model_response)
                else:
                    # 手动解析
                    result = self._parse_response_manually(model_response)
            
            # 处理CSV数据，添加批次号和案件名称
            csv_data = result.get("csv_data", "")
            processed_csv = self._process_csv_data(csv_data, batch_id, case_name)
            
            # 保存提取结果到会话目录
            extraction_result = {
                "status": "success",
                "analysis": result.get("analysis", ""),
                "csv_data": processed_csv,
                "mermaid_code": result.get("mermaid_code", ""),
                "batch_id": batch_id,
                "case_name": case_name,
                "file_info": file_info,
                "file_content": file_content,  # 添加源文件内容
                "extraction_time": datetime.now().isoformat(),
                "imported_to_db": False,  # 添加数据库导入状态
                "data_edited": False,  # 新增：标记数据是否被编辑过
                "original_csv_data": processed_csv  # 新增：保存原始CSV数据
            }
            
            # 保存CSV数据到文件
            session_dir = self.session_manager.get_session_dir(session_id)
            csv_file_path = session_dir / "data" / f"{case_name}_extracted_data.csv"
            with open(csv_file_path, 'w', encoding='utf-8') as f:
                f.write(processed_csv)
            
            print('--------extraction_result-----------') 
            print(extraction_result)
            
            return extraction_result
            
        except Exception as e:
            logging.error(f"案件信息提取失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _process_csv_data(self, csv_data: str, batch_id: str, case_name: str) -> str:
        """处理CSV数据，添加批次号和案件名称"""
        lines = csv_data.strip().split('\n')
        if not lines:
            return ""
        
        # 处理表头
        header = lines[0]
        if not header.startswith("batch_id"):
            header = f"batch_id,case_name,{header}"
        
        # 处理数据行
        processed_lines = [header]
        for line in lines[1:]:
            if line.strip():
                processed_line = f"{batch_id},{case_name},{line}"
                processed_lines.append(processed_line)
        
        return '\n'.join(processed_lines)
    
    def _parse_response_manually(self, response: str) -> Dict[str, Any]:
        """手动解析响应"""
        # 简单的手动解析逻辑
        return {
            "analysis": "分析内容提取中...",
            "csv_data": "实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）",
            "mermaid_code": "graph TD\n    A[案件分析] --> B[人物关系]"
        }

class DatabaseInsertAgent:
    """数据库插入智能体"""
    
    def __init__(self, db_manager: DatabaseManager, session_manager: SessionManager):
        self.db_manager = db_manager
        self.session_manager = session_manager
    
    def parse_csv_data(self, csv_data: str) -> List[tuple]:
        """解析CSV数据为插入记录列表"""
        lines = csv_data.strip().split('\n')
        if len(lines) < 2:
            raise ValueError("CSV数据格式错误，缺少表头或数据行")
        
        # 跳过表头，处理数据行
        records = []
        for line_num, line in enumerate(lines[1:], start=2):
            if not line.strip():
                continue
                
            try:
                # 解析CSV行，考虑可能包含逗号的字段
                fields = self._parse_csv_line(line)
                
                if len(fields) < 16:  # 至少需要16个字段
                    logging.warning(f"第{line_num}行字段数量不足，跳过: {line}")
                    continue
                
                # 提取字段值
                batch_id = fields[0].strip()
                case_name = fields[1].strip()
                entity_type = fields[2].strip()
                name_code = fields[3].strip()
                gender = fields[4].strip()
                age_str = fields[5].strip()
                id_card = fields[6].strip()
                residence = fields[7].strip()
                education = fields[8].strip()
                direct_superior = fields[9].strip()
                organization = fields[10].strip()
                org_level = fields[11].strip()
                role = fields[12].strip()
                related_actions = fields[13].strip()
                judicial_result = fields[14].strip()
                economic_str = fields[15].strip()
                
                # 数据类型转换和清理
                age = self._convert_to_int(age_str)
                economic = self._convert_to_decimal(economic_str)
                
                # 空值处理
                def clean_field(value):
                    if not value or value.lower() in ['null', 'none', 'n/a', '无', '']:
                        return None
                    return value
                
                record = (
                    clean_field(batch_id),
                    clean_field(case_name), 
                    clean_field(entity_type),
                    clean_field(name_code),
                    clean_field(gender),
                    age,
                    clean_field(id_card),
                    clean_field(residence),
                    clean_field(education),
                    clean_field(direct_superior),
                    clean_field(organization),
                    clean_field(org_level),
                    clean_field(role),
                    clean_field(related_actions),
                    clean_field(judicial_result),
                    economic
                )
                
                records.append(record)
                
            except Exception as e:
                logging.error(f"解析第{line_num}行数据失败: {line}, 错误: {e}")
                continue
        
        return records
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行，处理包含逗号的字段"""
        import csv
        import io
        
        # 使用Python的csv模块解析
        reader = csv.reader(io.StringIO(line))
        try:
            return next(reader)
        except StopIteration:
            return line.split(',')
    
    def _convert_to_int(self, value: str) -> Optional[int]:
        """将字符串转换为整数"""
        if not value or value.lower() in ['null', 'none', 'n/a', '无', '']:
            return None
        
        try:
            # 提取数字部分
            import re
            numbers = re.findall(r'\d+', value)
            if numbers:
                return int(numbers[0])
            return None
        except:
            return None
    
    def _convert_to_decimal(self, value: str) -> Optional[float]:
        """将字符串转换为数字（经济收益）"""
        if not value or value.lower() in ['null', 'none', 'n/a', '无', '']:
            return None
        
        try:
            # 移除中文字符和符号，提取数字
            import re
            # 移除"元"、"万"、"千"等单位
            cleaned = re.sub(r'[^\d\.\-]', '', value)
            if cleaned:
                result = float(cleaned)
                # 处理单位转换
                if '万' in value:
                    result *= 10000
                elif '千' in value:
                    result *= 1000
                return result
            return None
        except:
            return None
    
    def execute_batch_insert(self, csv_data: str, session_id: str) -> Dict[str, Any]:
        """批量插入数据到数据库"""
        try:
            # 解析CSV数据
            records = self.parse_csv_data(csv_data)
            
            if not records:
                return {
                    "status": "error",
                    "message": "没有有效的数据记录可插入"
                }
            
            # 准备插入语句
            insert_sql = """
                INSERT INTO mydb.case_details (
                    batch_id, case_name, entity_type, name_code, gender, age,
                    id_card, residence, education, direct_superior,
                    organization, org_level, role, related_actions,
                    judicial_result, economic, data_time
                ) VALUES (
                    %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, NOW()
                )
            """
            
            # 执行批量插入
            connection = self.db_manager.get_connection()
            try:
                with connection.cursor() as cursor:
                    # 批量插入
                    affected_rows = cursor.executemany(insert_sql, records)
                    connection.commit()
                    
                    # 保存插入记录到会话目录
                    session_dir = self.session_manager.get_session_dir(session_id)
                    insert_log_file = session_dir / "data" / "insert_log.json"
                    
                    log_data = {
                        "insert_time": datetime.now().isoformat(),
                        "total_records": len(records),
                        "affected_rows": affected_rows,
                        "sql": insert_sql,
                        "status": "success"
                    }
                    
                    with open(insert_log_file, 'w', encoding='utf-8') as f:
                        json.dump(log_data, f, ensure_ascii=False, indent=2)
                    
                    return {
                        "status": "success",
                        "message": f"成功插入 {len(records)} 条记录",
                        "total_records": len(records),
                        "affected_rows": affected_rows,
                        "sql": insert_sql
                    }
                    
            except Exception as e:
                connection.rollback()
                logging.error(f"数据库插入失败: {e}")
                return {
                    "status": "error",
                    "message": f"数据库插入失败: {str(e)}",
                    "total_records": len(records)
                }
            finally:
                connection.close()
                
        except Exception as e:
            logging.error(f"批量插入处理失败: {e}")
            return {
                "status": "error", 
                "message": f"批量插入处理失败: {str(e)}"
            }
    
    def validate_data_before_insert(self, csv_data: str) -> Dict[str, Any]:
        """插入前验证数据"""
        try:
            records = self.parse_csv_data(csv_data)
            
            validation_result = {
                "total_rows": len(records),
                "valid_rows": 0,
                "invalid_rows": 0,
                "warnings": [],
                "errors": []
            }
            
            for i, record in enumerate(records):
                try:
                    # 检查必填字段
                    if not record[0]:  # batch_id
                        validation_result["errors"].append(f"第{i+1}行: 批次ID不能为空")
                        validation_result["invalid_rows"] += 1
                        continue
                        
                    if not record[1]:  # case_name 
                        validation_result["errors"].append(f"第{i+1}行: 案件名称不能为空")
                        validation_result["invalid_rows"] += 1
                        continue
                        
                    if not record[3]:  # name_code
                        validation_result["warnings"].append(f"第{i+1}行: 姓名/代号为空")
                    
                    validation_result["valid_rows"] += 1
                    
                except Exception as e:
                    validation_result["errors"].append(f"第{i+1}行: 数据验证错误 - {str(e)}")
                    validation_result["invalid_rows"] += 1
            
            return {
                "status": "success",
                "validation": validation_result
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"数据验证失败: {str(e)}"
            }
 


class RepairAgent:
    """修复智能体 - 用于修复失败的案件提取和关系图生成"""

    def __init__(self, model_manager):
        self.model_manager = model_manager
        self.model_client = model_manager.get_client()

    async def repair_mermaid(self, case_content: str, original_mermaid: str, error_message: str,
                           user_requirements: str, max_length: int = 10000) -> Dict[str, Any]:
        """修复失败的Mermaid关系图生成"""
        try:
            # 限制内容长度以避免token超限
            case_content = case_content[:5000] if case_content else ""
            user_requirements = user_requirements[:2000] if user_requirements else ""
            original_mermaid = original_mermaid[:3000] if original_mermaid else ""
            error_message = error_message[:500] if error_message else ""

            system_message = f"""你是一位专业的关系图修复专家，专门修复失败的Mermaid关系图生成。

【修复任务】
根据案件内容、原始失败的Mermaid代码、错误信息和用户需求，重新生成正确的Mermaid关系图代码。

【用户需求】
{user_requirements}

【修复原则】
1. 分析原始代码的错误原因
2. 确保Mermaid语法正确
3. 保持图谱的逻辑性和可读性
4. 使用双引号包围中文节点内容以支持换行
5. 避免特殊字符导致的渲染问题

【输出格式】
请严格按照以下JSON格式返回：
{{
    "analysis": "对原始错误的分析和修复说明",
    "csv_data": "提取的结构化数据（CSV格式）",
    "mermaid_code": "修复后的完整Mermaid代码"
}}"""

            user_message = f"""【案件内容】
{case_content}

【原始失败的Mermaid代码】
{original_mermaid}

【错误信息】
{error_message}

请分析错误原因并生成修复后的Mermaid关系图代码。"""

            # 创建修复智能体
            repair_agent = AssistantAgent(
                name="mermaid_repair_agent",
                model_client=self.model_client,
                system_message=system_message
            )

            # 调用模型
            response = await repair_agent.on_messages(
                [TextMessage(content=user_message, source="user")],
                CancellationToken()
            )

            # 提取响应内容
            if response and response.chat_message:
                model_response = response.chat_message.content
            else:
                raise Exception("模型未返回有效响应")

            # 解析JSON响应
            try:
                result = json.loads(model_response)
            except json.JSONDecodeError:
                # 尝试提取JSON
                json_match = re.search(r'\{.*\}', model_response, re.DOTALL)
                if json_match:
                    try:
                        result = json.loads(json_match.group(0))
                    except json.JSONDecodeError:
                        logging.warning(f"修复智能体JSON解析失败，原始响应: {model_response}")
                        result = {
                            "analysis": "修复智能体响应格式错误",
                            "csv_data": "",
                            "mermaid_code": ""
                        }
                else:
                    logging.warning(f"修复智能体未返回有效JSON，原始响应: {model_response}")
                    result = {
                        "analysis": "修复智能体未返回有效JSON",
                        "csv_data": "",
                        "mermaid_code": ""
                    }

            return {
                "status": "success",
                "result": result
            }

        except Exception as e:
            logging.error(f"关系图修复失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }


class RelationshipVisualizationAgent:
    """人物关系图可视化智能体 - 支持修复功能和并发优化"""

    def __init__(self, session_manager, model_manager=None, max_repair_attempts: int = 2):
        self.session_manager = session_manager
        self.model_manager = model_manager
        self.max_repair_attempts = max_repair_attempts
        self.repair_agent = RepairAgent(model_manager) if model_manager else None

        # 并发优化相关
        self._docker_lock = asyncio.Lock()  # Docker调用锁，避免并发冲突
        self._generation_stats = {
            "total_generated": 0,
            "success_count": 0,
            "failure_count": 0,
            "total_time": 0.0
        }
    
    #def clean_mermaid_code(self, mermaid_code):
    #    """清理Mermaid代码中的特殊字符"""
    #    if mermaid_code:
    #        # 替换节点名称中的括号
    #        mermaid_code = re.sub(r'$$(.*?)$$', r'[\1]', mermaid_code)
    #        # 替换其他特殊字符
    #        mermaid_code = mermaid_code.replace("（", "(").replace("）", ")")
    #        # 移除可能导致问题的特殊字符，但保留mermaid语法需要的字符
    #        mermaid_code = re.sub(r'[^\w\u4e00-\u9fff\s$$$$(){}\"\',:;.\-_\n\r\t>|]', '', mermaid_code)
    #    return mermaid_code
    
    def clean_json_string(self, json_string: str) -> str:
        """
        清理JSON字符串中的控制字符，确保能正确解析

        Args:
            json_string (str): 原始的JSON字符串

        Returns:
            str: 清理后的JSON字符串
        """
        if not json_string:
            return json_string

        # 移除或替换常见的控制字符
        # 保留合法的JSON转义字符：\n, \r, \t, \", \\, \/
        import re

        # 替换不合法的控制字符
        # ASCII控制字符范围：0x00-0x1F (除了 \t \n \r)
        json_string = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F]', '', json_string)

        # 修复常见的JSON格式问题
        # 替换单引号为双引号（在字符串值外部）
        # 注意：这是一个简化的处理，可能需要更复杂的逻辑

        return json_string

    def clean_mermaid_code(self, mermaid_code):
        """
        清理Mermaid代码中的特殊字符，确保代码能正确渲染

        Args:
            mermaid_code (str): 原始的Mermaid图表代码

        Returns:
            str: 清理后的Mermaid代码
        """
        if mermaid_code:
            # 替换中文括号为英文括号，避免渲染问题
            mermaid_code = mermaid_code.replace("（", "(").replace("）", ")")

            # 移除可能导致Mermaid渲染问题的特殊字符，但保留必要的语法字符
            # 保留：字母、数字、中文字符、空白字符、方括号、圆括号、花括号、引号、标点符号、连字符、下划线、换行符、制表符、箭头符号、井号
            #mermaid_code = re.sub(r'[^\w\u4e00-\u9fff\s$$$$(){}\"\',:;.\-_\n\r\t>|#]', '', mermaid_code)

            # 注意：移除了错误的方括号替换逻辑
            # 原来的错误代码：mermaid_code = re.sub(r'$$$$(.*?)$$$$', r'[\1]', mermaid_code)

        return mermaid_code


    
    def regenerate_mermaid_from_csv(self, csv_data: str) -> str:
        """根据CSV数据重新生成Mermaid关系图代码"""
        try:
            # 解析CSV数据
            lines = csv_data.strip().split('\n')
            if len(lines) < 2:
                return "graph TD\n    A[无数据] --> B[请检查数据]"
            
            # 提取人员和关系信息
            people = []
            organizations = set()
            
            for line in lines[1:]:  # 跳过表头
                if line.strip():
                    fields = [field.strip() for field in line.split(',')]
                    if len(fields) >= 12:
                        name = fields[3] if fields[3] else f"未知{len(people)+1}"
                        superior = fields[9] if len(fields) > 9 and fields[9] and fields[9] != name else None
                        organization = fields[10] if len(fields) > 10 and fields[10] else None
                        role = fields[12] if len(fields) > 12 and fields[12] else "未知角色"
                        
                        # 清理名称中的特殊字符
                        name = re.sub(r'[^\w\u4e00-\u9fff]', '', name) if name else f"Person{len(people)+1}"
                        superior = re.sub(r'[^\w\u4e00-\u9fff]', '', superior) if superior else None
                        organization = re.sub(r'[^\w\u4e00-\u9fff]', '', organization) if organization else None
                        role = re.sub(r'[^\w\u4e00-\u9fff]', '', role) if role else "未知角色"
                        
                        people.append({
                            'name': name,
                            'superior': superior,
                            'organization': organization,
                            'role': role
                        })
                        
                        if organization:
                            organizations.add(organization)
            
            # 生成Mermaid代码
            mermaid_lines = ["graph TD"]
            
            # 添加组织节点
            org_nodes = {}
            for i, org in enumerate(organizations):
                org_id = f"ORG{i}"
                org_nodes[org] = org_id
                mermaid_lines.append(f"    {org_id}[\"{org}\"]")
                # 设置组织节点样式
                mermaid_lines.append(f"    classDef org{i} fill:#e1f5fe,stroke:#0277bd,stroke-width:2px")
                mermaid_lines.append(f"    class {org_id} org{i}")
            
            # 添加人员节点
            person_nodes = {}
            for i, person in enumerate(people):
                person_id = f"P{i}"
                person_nodes[person['name']] = person_id
                node_label = f"{person['name']}<br/>{person['role']}"
                mermaid_lines.append(f"    {person_id}[\"{node_label}\"]")
                
                # 设置人员节点样式
                mermaid_lines.append(f"    classDef person{i} fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px")
                mermaid_lines.append(f"    class {person_id} person{i}")
            
            # 添加关系
            for person in people:
                person_id = person_nodes.get(person['name'])
                
                # 添加上级关系
                if person['superior'] and person['superior'] in person_nodes:
                    superior_id = person_nodes[person['superior']]
                    mermaid_lines.append(f"    {superior_id} -->|指挥| {person_id}")
                
                # 添加组织关系
                if person['organization'] and person['organization'] in org_nodes:
                    org_id = org_nodes[person['organization']]
                    mermaid_lines.append(f"    {org_id} -->|管理| {person_id}")
            
            return '\n'.join(mermaid_lines)
            
        except Exception as e:
            logging.warning(f"从CSV生成Mermaid失败: {e}")
            return "graph TD\n    A[数据解析错误] --> B[请检查CSV格式]"
    
    def generate_mermaid_image_with_docker(self, mermaid_code: str, output_path: str) -> bool:
        """使用 Docker 生成 Mermaid 关系图"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 获取session目录作为工作目录
            session_id = Path(output_path).parts[-3]  # 从路径中提取session_id
            session_dir = self.session_manager.get_session_dir(session_id)
            work_dir = session_dir / "data"
            work_dir.mkdir(parents=True, exist_ok=True)

            # 获取绝对路径
            work_dir_abs = work_dir.resolve()

            # 从输出路径中提取案件编号作为文件名
            output_filename = Path(output_path).stem  # 获取不带扩展名的文件名

            # 在session/data目录下创建mermaid文件，使用案件编号命名
            mmd_file = work_dir_abs / f"{output_filename}.mmd"
            png_file = work_dir_abs / f"{output_filename}.png"
            
            
            #print('----------mmd_file:mermaid_code-------')
            #print(mermaid_code)
            
            # 写入 Mermaid 代码文件
            with open(mmd_file, 'w', encoding='utf-8') as f:
                f.write(mermaid_code)
            
            # 设置文件权限
            os.chmod(mmd_file, 0o644)
            
            # 验证文件是否存在并且有内容
            if not mmd_file.exists():
                logging.error("Mermaid文件创建失败")
                return False
            
            with open(mmd_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    logging.error("Mermaid文件内容为空")
                    return False
            
            logging.info(f"Mermaid文件创建成功: {mmd_file}, 内容长度: {len(content)}")
            
            # 使用 Docker SDK 生成图像
            try:
                client = docker.from_env()
                
                # 检查Docker镜像是否存在，如果不存在则拉取
                try:
                    client.images.get('minlag/mermaid-cli:latest')
                except docker.errors.ImageNotFound:
                    logging.info("拉取 mermaid-cli 镜像...")
                    client.images.pull('minlag/mermaid-cli:latest')
                
                # 运行容器生成图像 - 使用绝对路径和动态文件名
                container_result = client.containers.run(
                    'minlag/mermaid-cli:latest',
                    command=[
                        'mmdc',
                        '-i', f'/data/{output_filename}.mmd',
                        '-o', f'/data/{output_filename}.png',
                        '--theme', 'default',
                        '--scale', '2',
                        '--backgroundColor', 'white'
                    ],
                    volumes={
                        str(work_dir_abs): {'bind': '/data', 'mode': 'rw'}  # 使用绝对路径
                    },
                    working_dir='/data',
                    user='root',  # 确保有足够权限
                    remove=True,
                    detach=False,
                    stdout=True,
                    stderr=True
                )
                
                logging.info(f"Docker容器执行完成，输出: {container_result}")
                
                # 等待一下确保文件生成完成
                time.sleep(2)
                
                # 检查生成的文件是否存在
                if png_file.exists() and png_file.stat().st_size > 0:
                    # 复制到目标位置
                    shutil.copy2(png_file, output_path)
                    logging.info(f"Mermaid图解生成成功: {output_path}")
                    
                    # 清理临时文件
                    #try:
                    #    mmd_file.unlink()
                    #    png_file.unlink()
                    #except:
                    #    pass
                    
                    return True
                else:
                    logging.error(f"Docker容器没有生成PNG文件或文件为空")
                    logging.error(f"工作目录内容: {list(work_dir_abs.iterdir())}")
                    return False
                        
            except docker.errors.ContainerError as e:
                logging.error(f"Docker容器执行错误: {e}")
                if e.stderr:
                    stderr_output = e.stderr.decode() if isinstance(e.stderr, bytes) else str(e.stderr)
                    logging.error(f"容器错误输出: {stderr_output}")
                return False
            except docker.errors.DockerException as e:
                logging.error(f"Docker操作失败: {str(e)}")
                return False
                
        except Exception as e:
            logging.error(f"生成Mermaid图像异常: {str(e)}")
            return False
    
    def generate_mermaid_image_with_docker_alternative(self, mermaid_code: str, output_path: str) -> bool:
        """使用 Docker 的替代方案 - 使用命令行方式"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 获取session目录作为工作目录
            session_id = Path(output_path).parts[-3]
            session_dir = self.session_manager.get_session_dir(session_id)
            work_dir = session_dir / "data"
            work_dir.mkdir(parents=True, exist_ok=True)
            
            # 获取绝对路径
            work_dir_abs = work_dir.resolve()

            # 从输出路径中提取案件编号作为文件名
            output_filename = Path(output_path).stem  # 获取不带扩展名的文件名

            # 在session/data目录下创建mermaid文件，使用案件编号命名
            mmd_file = work_dir_abs / f"{output_filename}.mmd"
            png_file = work_dir_abs / f"{output_filename}.png"
            
            # 写入 Mermaid 代码文件
            with open(mmd_file, 'w', encoding='utf-8') as f:
                f.write(mermaid_code)
            
            logging.info(f"Mermaid文件创建成功: {mmd_file}")
            
            # 使用subprocess运行docker命令 - 使用绝对路径
            import subprocess
            
            cmd = [
                'docker', 'run', '--rm',
                '-v', f'{work_dir_abs}:/data',  # 使用绝对路径
                '-w', '/data',
                'minlag/mermaid-cli:latest',
                'mmdc',
                '-i', f'/data/{output_filename}.mmd',
                '-o', f'/data/{output_filename}.png',
                '--theme', 'default',
                '--scale', '2',
                '--backgroundColor', 'white'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # 检查生成的文件是否存在
                if png_file.exists() and png_file.stat().st_size > 0:
                    # 复制到目标位置
                    shutil.copy2(png_file, output_path)
                    logging.info(f"Mermaid图解生成成功: {output_path}")
                    
                    # 清理临时文件
                    #try:
                    #    mmd_file.unlink()
                    #    png_file.unlink()
                    #except:
                    #    pass
                    
                    return True
                else:
                    logging.error(f"Docker命令执行成功但未生成文件")
                    return False
            else:
                logging.error(f"Docker命令执行失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logging.error("Docker命令执行超时")
            return False
        except Exception as e:
            logging.error(f"Docker命令执行异常: {str(e)}")
            return False
    
    async def render_mermaid_to_image(self, mermaid_code: str, session_id: str, case_name: str = "relationship",
                                    user_requirements: str = None, repair_count: int = 0,
                                    case_content: str = None, csv_data: str = None) -> Dict[str, Any]:
        """将Mermaid代码渲染为图片，支持修复重试"""
        try:
            # 如果提供了CSV数据，重新生成Mermaid代码
            if csv_data:
                mermaid_code = self.regenerate_mermaid_from_csv(csv_data)

            print('----------mermaid_code-------')
            print(mermaid_code)

            # 确保mermaid代码不为空
            if not mermaid_code or not mermaid_code.strip():
                mermaid_code = "graph TD\n    A[默认节点] --> B[请提供有效数据]"

            # 修复Mermaid代码中的常见语法错误
            mermaid_code = self._fix_mermaid_syntax(mermaid_code)

            session_dir = self.session_manager.get_session_dir(session_id)

            # 使用案件编号作为文件名，如果case_name包含案件编号则直接使用，否则使用默认格式
            if case_name and case_name != "relationship":
                base_filename = case_name
            else:
                base_filename = f"relationship_{int(datetime.now().timestamp())}"

            filename = f"{base_filename}.png"
            output_path = session_dir / "outputs" / filename

            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 使用锁保护Docker调用，避免并发冲突
            async with self._docker_lock:
                # 首先尝试使用Docker SDK生成图像
                docker_success = await self._generate_mermaid_image_async(mermaid_code, str(output_path))

                # 如果Docker SDK失败，尝试命令行方式
                if not docker_success:
                    logging.warning(f"案件 {case_name} Docker SDK失败，尝试命令行方式")
                    docker_success = await self._generate_mermaid_image_alternative_async(mermaid_code, str(output_path))

            if docker_success and output_path.exists():
                # 转换为base64
                with open(output_path, 'rb') as f:
                    img_base64 = base64.b64encode(f.read()).decode()

                return {
                    "status": "success",
                    "image_path": str(output_path),
                    "image_base64": img_base64,
                    "filename": filename,
                    "mermaid_code": mermaid_code,
                    "generation_method": "docker",
                    "repair_count": repair_count
                }
            else:
                # Docker失败，尝试修复
                error_message = "Docker生成失败，无法生成关系图"

                if (self.repair_agent and repair_count < self.max_repair_attempts and
                    user_requirements and case_content):

                    logging.info(f"尝试修复关系图，第{repair_count + 1}次")

                    # 调用修复智能体
                    repair_result = await self.repair_agent.repair_mermaid(
                        case_content, mermaid_code, error_message, user_requirements
                    )

                    if repair_result["status"] == "success":
                        repaired_mermaid = repair_result["result"].get("mermaid_code", "")
                        if repaired_mermaid and repaired_mermaid != mermaid_code:
                            # 递归调用，使用修复后的代码
                            return await self.render_mermaid_to_image(
                                repaired_mermaid, session_id, case_name, user_requirements,
                                repair_count + 1, case_content, csv_data
                            )

                # 修复失败或超过最大修复次数，返回错误
                logging.error("Docker生成失败，无法生成关系图")
                return {
                    "status": "error",
                    "error": error_message,
                    "mermaid_code": mermaid_code,
                    "repair_count": repair_count,
                    "max_repairs_exceeded": repair_count >= self.max_repair_attempts
                }

        except Exception as e:
            logging.error(f"关系图生成失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "repair_count": repair_count
            }

    async def _generate_mermaid_image_async(self, mermaid_code: str, output_path: str) -> bool:
        """异步Docker调用生成Mermaid图片"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.generate_mermaid_image_with_docker,
            mermaid_code,
            output_path
        )

    async def _generate_mermaid_image_alternative_async(self, mermaid_code: str, output_path: str) -> bool:
        """异步Docker命令行方式生成Mermaid图片"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.generate_mermaid_image_with_docker_alternative,
            mermaid_code,
            output_path
        )

    def _fix_mermaid_syntax(self, mermaid_code: str) -> str:
        """修复常见的Mermaid语法错误"""
        if not mermaid_code:
            return mermaid_code

        import re

        # 查找节点定义模式并修复其中的换行符和引号
        node_pattern = r'(\w+)\[([^\]]+)\]'

        def fix_node(match):
            node_id = match.group(1)
            content = match.group(2)

            # 修复节点内容中的换行符
            content = content.replace('\\n', '<br/>')
            content = content.replace('\n', '<br/>')

            # 如果内容没有被双引号包围，添加双引号
            if not (content.startswith('"') and content.endswith('"')):
                content = f'"{content}"'

            return f'{node_id}[{content}]'

        fixed_code = re.sub(node_pattern, fix_node, mermaid_code)

        # 确保每行末尾没有多余的空格
        lines = fixed_code.split('\n')
        fixed_lines = [line.rstrip() for line in lines]
        fixed_code = '\n'.join(fixed_lines)

        return fixed_code

    def get_generation_stats(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        stats = self._generation_stats.copy()
        if stats["total_generated"] > 0:
            stats["success_rate"] = stats["success_count"] / stats["total_generated"]
            stats["average_time"] = stats["total_time"] / stats["total_generated"]
        else:
            stats["success_rate"] = 0.0
            stats["average_time"] = 0.0
        return stats

    def reset_generation_stats(self):
        """重置生成统计信息"""
        self._generation_stats = {
            "total_generated": 0,
            "success_count": 0,
            "failure_count": 0,
            "total_time": 0.0
        }
    
    def _render_with_matplotlib_fallback(self, mermaid_code: str, session_id: str, case_name: str) -> Dict[str, Any]:
        """使用matplotlib作为备用方案生成关系图"""
        try:
            session_dir = self.session_manager.get_session_dir(session_id)
            
            filename = f"{case_name}_relationship_{int(datetime.now().timestamp())}_fallback.png"
            output_path = session_dir / "outputs" / filename
            
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建图形
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 解析Mermaid代码中的节点和关系
            nodes, edges = self._parse_mermaid(mermaid_code)
            
            # 简单的布局算法
            positions = self._calculate_positions(nodes)
            
            # 绘制节点
            for node, (x, y) in positions.items():
                ax.scatter(x, y, s=1000, c='lightblue', alpha=0.7, edgecolors='navy', linewidth=2)
                ax.text(x, y, node, ha='center', va='center', fontsize=10, weight='bold')
            
            # 绘制边
            for edge in edges:
                if edge['from'] in positions and edge['to'] in positions:
                    x1, y1 = positions[edge['from']]
                    x2, y2 = positions[edge['to']]
                    
                    # 计算箭头方向和位置，避免与节点重叠
                    dx, dy = x2 - x1, y2 - y1
                    length = (dx**2 + dy**2)**0.5
                    if length > 0:
                        # 缩短箭头，避免与节点重叠
                        offset = 0.3
                        start_x = x1 + dx * offset / length
                        start_y = y1 + dy * offset / length
                        end_x = x2 - dx * offset / length
                        end_y = y2 - dy * offset / length
                        
                        ax.annotate('', xy=(end_x, end_y), xytext=(start_x, start_y),
                                  arrowprops=dict(arrowstyle='->', lw=2, color='darkred', alpha=0.7))
                        
                        # 添加边标签
                        if edge.get('label'):
                            mid_x, mid_y = (start_x + end_x) / 2, (start_y + end_y) / 2
                            ax.text(mid_x, mid_y, edge['label'], ha='center', va='center', 
                                   fontsize=8, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8))
            
            # 设置图形属性
            margin = 0.5
            if positions:
                x_coords = [pos[0] for pos in positions.values()]
                y_coords = [pos[1] for pos in positions.values()]
                ax.set_xlim(min(x_coords) - margin, max(x_coords) + margin)
                ax.set_ylim(min(y_coords) - margin, max(y_coords) + margin)
            else:
                ax.set_xlim(-2, 2)
                ax.set_ylim(-2, 2)
                
            ax.set_aspect('equal')
            ax.axis('off')
            ax.set_title(f'{case_name}案件人物关系图', fontsize=16, weight='bold', pad=20)
            
            # 保存图片
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            # 转换为base64
            with open(output_path, 'rb') as f:
                img_base64 = base64.b64encode(f.read()).decode()
            
            return {
                "status": "success",
                "image_path": str(output_path),
                "image_base64": img_base64,
                "filename": filename,
                "mermaid_code": mermaid_code,
                "generation_method": "matplotlib_fallback"
            }
            
        except Exception as e:
            logging.error(f"备用方案生成失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _parse_mermaid(self, mermaid_code: str) -> tuple:
        """解析Mermaid代码"""
        nodes = set()
        edges = []
        
        lines = mermaid_code.split('\n')
        for line in lines:
            line = line.strip()
            if '-->' in line or '|' in line:
                # 解析边 (支持带标签的边)
                if '|' in line and '-->' in line:
                    # 带标签的边: A -->|label| B
                    match = re.search(r'(\w+)\s*-->\s*\|([^|]*)\|\s*(\w+)', line)
                    if match:
                        from_node, label, to_node = match.groups()
                        nodes.add(from_node)
                        nodes.add(to_node)
                        edges.append({
                            'from': from_node,
                            'to': to_node,
                            'label': label.strip()
                        })
                elif '-->' in line:
                    # 普通边: A --> B
                    parts = line.split('-->')
                    if len(parts) == 2:
                        from_node = re.sub(r'[^\w\u4e00-\u9fff]', '', parts[0].strip())
                        to_node = re.sub(r'[^\w\u4e00-\u9fff]', '', parts[1].strip())
                        
                        if from_node and to_node:
                            nodes.add(from_node)
                            nodes.add(to_node)
                            edges.append({
                                'from': from_node,
                                'to': to_node,
                                'label': ''
                            })
            elif '[' in line and ']' in line:
                # 解析节点定义: A["节点名称"]
                match = re.search(r'(\w+)\s*$$([^$$]*)$$', line)
                if match:
                    node_id, node_label = match.groups()
                    # 提取节点标签中的实际文本
                    clean_label = re.sub(r'["\']', '', node_label).strip()
                    if clean_label:
                        nodes.add(node_id)
        
        # 如果没有解析到节点，添加默认节点
        if not nodes:
            nodes = {'主犯', '从犯1', '从犯2', '组织'}
            edges = [
                {'from': '主犯', 'to': '从犯1', 'label': '指挥'},
                {'from': '主犯', 'to': '从犯2', 'label': '指挥'},
                {'from': '组织', 'to': '主犯', 'label': '隶属'}
            ]
        
        return list(nodes), edges
    
    def _calculate_positions(self, nodes: List[str]) -> Dict[str, tuple]:
        """计算节点位置"""
        import math
        
        positions = {}
        n = len(nodes)
        
        if n == 1:
            positions[nodes[0]] = (0, 0)
        elif n == 2:
            positions[nodes[0]] = (-1, 0)
            positions[nodes[1]] = (1, 0)
        elif n <= 6:
            # 圆形布局
            for i, node in enumerate(nodes):
                angle = 2 * math.pi * i / n
                x = 1.5 * math.cos(angle)
                y = 1.5 * math.sin(angle)
                positions[node] = (x, y)
        else:
            # 多层圆形布局
            inner_count = min(6, n // 2)
            outer_count = n - inner_count
            
            # 内圈
            for i in range(inner_count):
                angle = 2 * math.pi * i / inner_count
                x = 0.8 * math.cos(angle)
                y = 0.8 * math.sin(angle)
                positions[nodes[i]] = (x, y)
            
            # 外圈
            for i in range(outer_count):
                angle = 2 * math.pi * i / outer_count
                x = 2 * math.cos(angle)
                y = 2 * math.sin(angle)
                positions[nodes[inner_count + i]] = (x, y)
        
        return positions





class ReportGeneratorAgent:
    """报告生成智能体"""
    
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
    
    def generate_html_report(self, case_data: Dict[str, Any], session_id: str, relationship_image: str = None) -> Dict[str, Any]:
        """生成HTML报告"""
        try:
            session_dir = self.session_manager.get_session_dir(session_id)
            
            case_name = case_data.get("case_name", "未知案件")
            analysis = case_data.get("analysis", "")
            csv_data = case_data.get("csv_data", "")  # 使用最新的CSV数据（可能是编辑后的）
            
            # 格式化分析内容，提高可读性
            formatted_analysis = self._format_analysis_content(analysis)
            
            # 解析CSV数据为表格（排除batch_id和case_name列）
            table_html = self._csv_to_html_table(csv_data, exclude_columns=['batch_id', 'case_name'])
            
            # 使用 try-except 处理多种时间格式
            def format_extraction_time(time_str):
                """
                处理多种时间格式，返回格式化后的时间字符串
                """
                if not time_str or time_str == 'N/A':
                    return 'N/A'
                
                # 尝试不同的时间格式
                formats = [
                    '%Y-%m-%dT%H:%M:%S.%f',  # ISO格式带微秒
                    '%Y-%m-%dT%H:%M:%S',     # ISO格式不带微秒
                    '%Y年%m月%d日 %H:%M:%S',   # 中文格式
                    '%Y-%m-%d %H:%M:%S',     # 标准格式
                ]
                
                for fmt in formats:
                    try:
                        dt = datetime.strptime(time_str, fmt)
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
                
                # 如果所有格式都失败，返回原始字符串
                return time_str
            
            # 生成HTML内容
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{case_name}案件分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }}
        .analysis-section {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            white-space: pre-wrap;
            line-height: 1.8;
            font-size: 14px;
        }}
        .analysis-section h3 {{
            color: #2c3e50;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
        }}
        .analysis-section p {{
            margin: 10px 0;
            text-indent: 2em;
        }}
        .analysis-section .step {{
            margin: 15px 0;
            padding: 10px;
            background-color: #e8f4f8;
            border-left: 3px solid #3498db;
            border-radius: 3px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }}
        th {{
            background-color: #3498db;
            color: white;
            font-weight: bold;
            text-align: center;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .relationship-image {{
            text-align: center;
            margin: 20px 0;
        }}
        .relationship-image img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .timestamp {{
            text-align: right;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 30px;
        }}
        .file-info {{
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }}
        .data-edit-notice {{
            background-color: #fff3cd;
            border: 1px solid #ffd60a;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            color: #856404;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{case_name}案件分析报告</h1>
        
        <h2>📁 文件信息</h2>
        <div class="file-info">
            <p><strong>原始文件名:</strong> {case_data.get('file_info', {}).get('original_name', 'N/A')}</p>
            <p><strong>批次号:</strong> {case_data.get('batch_id', 'N/A')}</p>
            <p><strong>分析时间:</strong> {format_extraction_time(case_data.get('extraction_time', 'N/A'))}</p>
            {f'<div class="data-edit-notice">⚠️ 注意：表格数据已被用户编辑修改</div>' if case_data.get('data_edited', False) else ''}
        </div>
        
        {f'<h2>📊 分析过程</h2><div class="analysis-section">{formatted_analysis}</div>' if analysis else ''}
        
        <h2>👥 案件人员信息</h2>
        {table_html}
        
        {f'<h2>🔗 案件人员关系图</h2><div class="relationship-image"><img src="data:image/png;base64,{relationship_image}" alt="人员关系图"></div>' if relationship_image else ''}
        
        <div class="timestamp">
            报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>
    </div>
</body>
</html>
"""
            
            # 保存HTML文件
            filename = f"{case_name}_report_{int(datetime.now().timestamp())}.html"
            output_path = session_dir / "reports" / filename
            
            # 确保目录存在
            output_path.parent.mkdir(exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return {
                "status": "success",
                "html_content": html_content,
                "file_path": str(output_path),
                "filename": filename
            }
            
        except Exception as e:
            logging.error(f"报告生成失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _format_analysis_content(self, analysis_text: str) -> str:
        """格式化分析内容，提高可读性"""
        if not analysis_text:
            return analysis_text
        
        # 处理常见的格式问题
        formatted_text = analysis_text.strip()
        
        # 处理标题格式 (如：第一步：、第二步：等)
        import re
        
        # 处理标题格式 (如：第一步：、第二步：等)
        formatted_text = re.sub(r'(第[一二三四五六七十]+步[：:])', r'<h3>\1</h3>', formatted_text)
        formatted_text = re.sub(r'(步骤[0-9]+[：:])', r'<h3>\1</h3>', formatted_text)
        
        # 处理数字编号步骤
        formatted_text = re.sub(r'(\n|^)([0-9]+[\.\、])', r'\1<div class="step">\2', formatted_text)
        
        # 在句号后添加段落分隔
        formatted_text = re.sub(r'([。！？])([^<\n])', r'\1</p><p>\2', formatted_text)
        
        # 处理冒号后的内容
        formatted_text = re.sub(r'([：:])([^<\n])', r'\1<br>\2', formatted_text)
        
        # 处理特殊标记
        formatted_text = re.sub(r'【([^】]+)】', r'<strong>【\1】</strong>', formatted_text)
        
        # 添加段落标签
        if not formatted_text.startswith('<'):
            formatted_text = f'<p>{formatted_text}</p>'
        
        # 关闭未关闭的div标签
        if '<div class="step">' in formatted_text:
            formatted_text = re.sub(r'(<div class="step">[^<]*?)(?=<div class="step">|$)', r'\1</div>', formatted_text)
            if not formatted_text.endswith('</div>'):
                formatted_text += '</div>'
        
        # 清理多余的空段落
        formatted_text = re.sub(r'<p>\s*</p>', '', formatted_text)
        formatted_text = re.sub(r'<p>\s*<br>\s*</p>', '', formatted_text)
        
        return formatted_text
    
    def _csv_to_html_table(self, csv_data: str, exclude_columns: List[str] = None) -> str:
        """将CSV数据转换为HTML表格"""
        if not csv_data.strip():
            return "<p>无数据</p>"
        
        exclude_columns = exclude_columns or []
        
        lines = csv_data.strip().split('\n')
        if len(lines) < 2:
            return "<p>数据格式错误</p>"
        
        # 表头
        headers = [h.strip() for h in lines[0].split(',')]
        
        # 找到要排除的列的索引
        exclude_indices = []
        filtered_headers = []
        for i, header in enumerate(headers):
            if header.lower() not in [col.lower() for col in exclude_columns]:
                filtered_headers.append(header)
            else:
                exclude_indices.append(i)
        
        html = "<table>\n<thead>\n<tr>\n"
        for header in filtered_headers:
            html += f"<th>{header}</th>\n"
        html += "</tr>\n</thead>\n<tbody>\n"
        
        # 数据行
        for line in lines[1:]:
            if line.strip():
                cells = [c.strip() for c in line.split(',')]
                # 过滤掉排除的列
                filtered_cells = []
                for i, cell in enumerate(cells):
                    if i not in exclude_indices:
                        filtered_cells.append(cell)
                
                html += "<tr>\n"
                for cell in filtered_cells:
                    # 处理长文本换行
                    if len(cell) > 30:
                        cell = cell[:27] + "..."
                    html += f"<td>{cell}</td>\n"
                html += "</tr>\n"
        
        html += "</tbody>\n</table>"
        return html


class ConversationManager:
    """对话管理器"""
    
    def __init__(self, session_manager: SessionManager):
        self.conversations = {}
        self.session_manager = session_manager
    
    def get_conversation(self, session_id: str) -> Dict[str, Any]:
        if session_id not in self.conversations:
            # 尝试从文件加载
            saved_data = self.session_manager.load_session_data(session_id)
            self.conversations[session_id] = saved_data if saved_data else {
                "messages": [],
                "case_data": None,
                "files": [],
                "context": {}
            }
        return self.conversations[session_id]
    
    def add_message(self, session_id: str, message_type: str, content: str, metadata: Dict[str, Any] = None):
        conversation = self.get_conversation(session_id)
        message = {
            "type": message_type,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }
        conversation["messages"].append(message)
        
        # 保存到文件
        self.session_manager.save_session_data(session_id, conversation)
    
    def set_case_data(self, session_id: str, case_data: Any):
        conversation = self.get_conversation(session_id)
        conversation["case_data"] = case_data
        
        # 保存到文件
        self.session_manager.save_session_data(session_id, conversation)



class CaseAnalysisOrchestrator:
    """案件分析编排器"""
    
    def __init__(self):
        self.session_manager = SessionManager()
        self.model_manager = ModelManager()
        self.db_manager = DatabaseManager()
        self.file_processor = FileProcessor()
        self.case_extractor = CaseExtractionAgent(self.model_manager, self.session_manager)
        self.db_inserter = DatabaseInsertAgent(self.db_manager, self.session_manager)
        self.relationship_visualizer = RelationshipVisualizationAgent(self.session_manager, self.model_manager)
        self.report_generator = ReportGeneratorAgent(self.session_manager)
        self.conversation_manager = ConversationManager(self.session_manager)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def save_uploaded_file(self, session_id: str, file_content: bytes, original_filename: str) -> str:
        """保存上传的文件，保持原始文件名"""
        return self.session_manager.save_uploaded_file(session_id, file_content, original_filename)
    
    async def process_case_file(self, file_path: str, session_id: str, user_requirements: str = None) -> Dict[str, Any]:
        """处理案件文件的完整工作流"""
        try:
            # 读取文件
            file_content, file_info = self.file_processor.read_file(file_path)
            
            # 生成批次ID
            batch_id = f"batch_{int(datetime.now().timestamp())}"
            
            # 提取案件信息
            extraction_result = await self.case_extractor.extract_case_info(
                file_content, file_info, batch_id, session_id
            )
            
            if extraction_result["status"] != "success":
                return extraction_result
            
            # 不再生成关系图图片，直接保存Mermaid代码
            mermaid_code = extraction_result.get("mermaid_code", "")
            if mermaid_code:
                # 直接保存Mermaid代码，不生成图片
                extraction_result["mermaid_code"] = mermaid_code
            
            # 保存案件数据到对话管理器
            self.conversation_manager.set_case_data(session_id, extraction_result)
            
            return extraction_result
            
        except Exception as e:
            self.logger.error(f"处理案件文件失败: {e}")
            return {
                "status": "error",
                "message": f"处理案件文件失败: {str(e)}"
            }
    
    def update_case_data(self, session_id: str, updated_csv_data: str) -> Dict[str, Any]:
        """更新案件数据（用户编辑后）"""
        try:
            # 获取当前会话数据
            conversation = self.conversation_manager.get_conversation(session_id)
            case_data = conversation.get("case_data")
            
            if not case_data:
                return {
                    "status": "error",
                    "message": "没有可更新的案件数据"
                }
            
            # 更新CSV数据
            case_data["csv_data"] = updated_csv_data
            case_data["data_edited"] = True
            case_data["last_edit_time"] = datetime.now().isoformat()
            
            # 保存更新后的数据
            self.conversation_manager.set_case_data(session_id, case_data)
            
            # 保存编辑后的CSV到文件
            session_dir = self.session_manager.get_session_dir(session_id)
            case_name = case_data.get("case_name", "case")
            csv_file_path = session_dir / "data" / f"{case_name}_edited_data.csv"
            with open(csv_file_path, 'w', encoding='utf-8') as f:
                f.write(updated_csv_data)
            
            return {
                "status": "success",
                "message": "案件数据已更新",
                "case_data": case_data
            }
            
        except Exception as e:
            self.logger.error(f"更新案件数据失败: {e}")
            return {
                "status": "error",
                "message": f"更新案件数据失败: {str(e)}"
            }
    
    def regenerate_relationship_image(self, session_id: str) -> Dict[str, Any]:
        """根据最新的CSV数据重新生成关系图"""
        try:
            # 获取最新的案件数据
            conversation = self.conversation_manager.get_conversation(session_id)
            case_data = conversation.get("case_data")
            
            if not case_data:
                return {
                    "status": "error",
                    "message": "没有可用的案件数据"
                }
            
            # 获取最新的CSV数据
            csv_data = case_data.get("csv_data", "")
            case_name = case_data.get("case_name", "relationship")
            
            # 不再重新生成关系图图片，直接使用现有的Mermaid代码
            mermaid_code = case_data.get("mermaid_code", "")

            if mermaid_code:
                # 保存更新后的数据
                self.conversation_manager.set_case_data(session_id, case_data)

                return {
                    "status": "success",
                    "message": "关系图数据已更新",
                    "mermaid_code": mermaid_code
                }
            else:
                return {
                    "status": "error",
                    "message": "没有找到Mermaid代码"
                }
                
        except Exception as e:
            self.logger.error(f"重新生成关系图失败: {e}")
            return {
                "status": "error",
                "message": f"重新生成关系图失败: {str(e)}"
            }
    
    async def insert_to_database(self, session_id: str, csv_data: str = None) -> Dict[str, Any]:
        """插入数据到数据库，使用最新的编辑后数据"""
        try:
            # 如果没有提供csv_data，从会话数据中获取最新数据
            if csv_data is None:
                conversation = self.conversation_manager.get_conversation(session_id)
                case_data = conversation.get("case_data")
                if not case_data:
                    return {
                        "session_id": session_id,
                        "status": "error",
                        "message": "没有可用的案件数据"
                    }
                csv_data = case_data.get("csv_data", "")
            
            # 数据验证
            validation_result = self.db_inserter.validate_data_before_insert(csv_data)
            
            if validation_result["status"] != "success":
                return {
                    "session_id": session_id,
                    "status": "error",
                    "message": "数据验证失败",
                    "error": validation_result.get("message", "")
                }
            
            # 检查验证结果
            validation = validation_result["validation"]
            if validation["invalid_rows"] > 0:
                return {
                    "session_id": session_id,
                    "status": "warning",
                    "message": f"数据验证发现问题: {validation['invalid_rows']} 行无效数据",
                    "validation": validation
                }
            
            # 执行插入
            insert_result = self.db_inserter.execute_batch_insert(csv_data, session_id)
            
            # 如果插入成功，更新会话数据中的导入状态
            if insert_result["status"] == "success":
                conversation = self.conversation_manager.get_conversation(session_id)
                if conversation.get("case_data"):
                    conversation["case_data"]["imported_to_db"] = True
                    self.conversation_manager.set_case_data(session_id, conversation["case_data"])
            
            return {
                "session_id": session_id,
                "status": insert_result["status"],
                "message": insert_result["message"],
                "insert_result": insert_result,
                "validation": validation
            }
            
        except Exception as e:
            return {
                "session_id": session_id,
                "status": "error",
                "message": f"数据库操作失败: {str(e)}"
            }
    
    def generate_report(self, session_id: str) -> Dict[str, Any]:
        """生成分析报告，使用最新的编辑后数据"""
        try:
            # 获取最新的案件数据
            conversation = self.conversation_manager.get_conversation(session_id)
            case_data = conversation.get("case_data")
            
            if not case_data:
                return {
                    "status": "error",
                    "message": "没有可用的案件数据"
                }
            
            # 获取最新的关系图
            relationship_image = case_data.get("relationship_image")
            
            # 生成HTML报告
            report_result = self.report_generator.generate_html_report(
                case_data, session_id, relationship_image
            )
            
            return {
                "status": report_result["status"],
                "message": "报告生成成功" if report_result["status"] == "success" else report_result.get("error", ""),
                "report_result": report_result
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"生成报告失败: {str(e)}"
            }
 
# 工具函数
def generate_session_id() -> str:
    """生成会话ID"""
    return uuid.uuid4().hex[:16]  # 生成16位随机字符串 
