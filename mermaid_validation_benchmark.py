#!/usr/bin/env python3
"""
Mermaid验证方法性能对比测试
比较Live Editor API和Docker CLI两种验证方法的性能
"""

import time
import asyncio
import logging
from typing import List, Dict, Any
from case_extraction_agent import CaseExtractionAgent
from config import config


class MermaidValidationBenchmark:
    """Mermaid验证性能基准测试"""
    
    def __init__(self):
        self.agent = CaseExtractionAgent()
        self.logger = logging.getLogger(__name__)
        
        # 测试用的Mermaid代码样本
        self.test_samples = [
            # 简单图表
            """
graph TD
    A[开始] --> B[处理]
    B --> C[结束]
            """,
            
            # 复杂人物关系图
            """
graph TD
    A["张某某<br/>组织者<br/>35岁<br/>已判刑"] -->|指挥| B["李某某<br/>运输员<br/>28岁<br/>在逃"]
    A -->|雇佣| C["王某某<br/>司机<br/>30岁<br/>逮捕"]
    B -->|联系| D["赵某某<br/>接货人<br/>25岁<br/>取保"]
    C -->|运输| E["货物<br/>走私商品<br/>价值100万"]
    
    style A fill:#FFB6C1,stroke:#333
    style B fill:#87CEEB,stroke:#333
    style C fill:#87CEEB,stroke:#333
    style D fill:#98FB98,stroke:#333
    style E fill:#FFD700,stroke:#333
            """,
            
            # 语法错误的代码
            """
graph TD
    A[开始 --> B[处理]
    B --> C[结束
            """,
            
            # 空代码
            "",
            
            # 复杂流程图
            """
flowchart TB
    subgraph "走私组织"
        A["主犯张某<br/>组织者<br/>负责整体规划"]
        B["从犯李某<br/>联络员<br/>负责上下游联系"]
        C["从犯王某<br/>运输员<br/>负责货物运输"]
    end
    
    subgraph "上游供应"
        D["供应商赵某<br/>提供货源"]
        E["仓库管理员钱某<br/>负责存储"]
    end
    
    subgraph "下游销售"
        F["销售员孙某<br/>负责销售"]
        G["客户群体<br/>最终买家"]
    end
    
    D -->|供货| E
    E -->|出库| A
    A -->|指挥| B
    A -->|指挥| C
    B -->|联络| D
    B -->|联络| F
    C -->|运输| F
    F -->|销售| G
    
    style A fill:#ff6b6b,stroke:#333,stroke-width:3px
    style B fill:#4ecdc4,stroke:#333,stroke-width:2px
    style C fill:#4ecdc4,stroke:#333,stroke-width:2px
    style D fill:#ffe66d,stroke:#333,stroke-width:2px
    style E fill:#ffe66d,stroke:#333,stroke-width:2px
    style F fill:#a8e6cf,stroke:#333,stroke-width:2px
    style G fill:#dcedc1,stroke:#333,stroke-width:2px
            """
        ]
    
    async def benchmark_validation_method(self, method: str, samples: List[str], 
                                        iterations: int = 5) -> Dict[str, Any]:
        """
        对指定验证方法进行基准测试
        
        Args:
            method: 验证方法 ('live_editor', 'docker', 'hybrid')
            samples: 测试样本
            iterations: 每个样本的测试次数
            
        Returns:
            测试结果
        """
        # 临时修改配置
        original_method = self.agent.system_config['mermaid_validation_method']
        self.agent.system_config['mermaid_validation_method'] = method
        
        results = {
            "method": method,
            "total_tests": len(samples) * iterations,
            "successful_validations": 0,
            "failed_validations": 0,
            "total_time": 0.0,
            "average_time": 0.0,
            "min_time": float('inf'),
            "max_time": 0.0,
            "sample_results": []
        }
        
        try:
            for i, sample in enumerate(samples):
                sample_results = {
                    "sample_index": i,
                    "sample_length": len(sample),
                    "times": [],
                    "success_count": 0,
                    "error_count": 0,
                    "errors": []
                }
                
                for iteration in range(iterations):
                    start_time = time.time()
                    
                    try:
                        result = self.agent.validate_mermaid_code(sample)
                        end_time = time.time()
                        
                        execution_time = end_time - start_time
                        sample_results["times"].append(execution_time)
                        
                        if result.get("valid", False):
                            sample_results["success_count"] += 1
                            results["successful_validations"] += 1
                        else:
                            sample_results["error_count"] += 1
                            results["failed_validations"] += 1
                            sample_results["errors"].append(result.get("error", "未知错误"))
                        
                        results["total_time"] += execution_time
                        results["min_time"] = min(results["min_time"], execution_time)
                        results["max_time"] = max(results["max_time"], execution_time)
                        
                    except Exception as e:
                        end_time = time.time()
                        execution_time = end_time - start_time
                        
                        sample_results["times"].append(execution_time)
                        sample_results["error_count"] += 1
                        results["failed_validations"] += 1
                        sample_results["errors"].append(str(e))
                        
                        results["total_time"] += execution_time
                
                # 计算样本统计
                if sample_results["times"]:
                    sample_results["avg_time"] = sum(sample_results["times"]) / len(sample_results["times"])
                    sample_results["min_time"] = min(sample_results["times"])
                    sample_results["max_time"] = max(sample_results["times"])
                
                results["sample_results"].append(sample_results)
            
            # 计算总体统计
            if results["total_tests"] > 0:
                results["average_time"] = results["total_time"] / results["total_tests"]
                results["success_rate"] = results["successful_validations"] / results["total_tests"]
            
            if results["min_time"] == float('inf'):
                results["min_time"] = 0.0
            
        finally:
            # 恢复原始配置
            self.agent.system_config['mermaid_validation_method'] = original_method
        
        return results
    
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """运行全面的基准测试"""
        self.logger.info("开始Mermaid验证方法性能基准测试")
        
        methods = ['live_editor', 'docker', 'hybrid']
        all_results = {}
        
        for method in methods:
            self.logger.info(f"测试验证方法: {method}")
            
            try:
                result = await self.benchmark_validation_method(
                    method=method,
                    samples=self.test_samples,
                    iterations=3
                )
                all_results[method] = result
                
                self.logger.info(f"{method} 测试完成 - 平均时间: {result['average_time']:.3f}s, "
                               f"成功率: {result['success_rate']:.2%}")
                
            except Exception as e:
                self.logger.error(f"{method} 测试失败: {e}")
                all_results[method] = {
                    "method": method,
                    "error": str(e),
                    "status": "failed"
                }
        
        # 生成对比报告
        comparison = self._generate_comparison_report(all_results)
        all_results["comparison"] = comparison
        
        return all_results
    
    def _generate_comparison_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成对比报告"""
        comparison = {
            "fastest_method": None,
            "most_reliable_method": None,
            "recommended_method": None,
            "performance_ranking": [],
            "reliability_ranking": []
        }
        
        valid_results = {k: v for k, v in results.items() 
                        if isinstance(v, dict) and "average_time" in v}
        
        if not valid_results:
            return comparison
        
        # 性能排名（按平均时间）
        performance_sorted = sorted(valid_results.items(), 
                                  key=lambda x: x[1]["average_time"])
        comparison["performance_ranking"] = [
            {
                "method": method,
                "average_time": data["average_time"],
                "rank": i + 1
            }
            for i, (method, data) in enumerate(performance_sorted)
        ]
        comparison["fastest_method"] = performance_sorted[0][0]
        
        # 可靠性排名（按成功率）
        reliability_sorted = sorted(valid_results.items(), 
                                  key=lambda x: x[1].get("success_rate", 0), 
                                  reverse=True)
        comparison["reliability_ranking"] = [
            {
                "method": method,
                "success_rate": data.get("success_rate", 0),
                "rank": i + 1
            }
            for i, (method, data) in enumerate(reliability_sorted)
        ]
        comparison["most_reliable_method"] = reliability_sorted[0][0]
        
        # 推荐方法（综合考虑性能和可靠性）
        method_scores = {}
        for method, data in valid_results.items():
            # 性能分数（时间越短分数越高）
            min_time = min(d["average_time"] for d in valid_results.values())
            max_time = max(d["average_time"] for d in valid_results.values())
            if max_time > min_time:
                performance_score = 1 - (data["average_time"] - min_time) / (max_time - min_time)
            else:
                performance_score = 1.0
            
            # 可靠性分数
            reliability_score = data.get("success_rate", 0)
            
            # 综合分数（可靠性权重更高）
            total_score = performance_score * 0.3 + reliability_score * 0.7
            method_scores[method] = total_score
        
        comparison["recommended_method"] = max(method_scores.items(), 
                                             key=lambda x: x[1])[0]
        
        return comparison
    
    def print_results(self, results: Dict[str, Any]):
        """打印测试结果"""
        print("\n" + "="*80)
        print("Mermaid验证方法性能基准测试结果")
        print("="*80)
        
        for method, data in results.items():
            if method == "comparison":
                continue
                
            if isinstance(data, dict) and "average_time" in data:
                print(f"\n【{method.upper()}】")
                print(f"  总测试数: {data['total_tests']}")
                print(f"  成功验证: {data['successful_validations']}")
                print(f"  失败验证: {data['failed_validations']}")
                print(f"  成功率: {data.get('success_rate', 0):.2%}")
                print(f"  平均时间: {data['average_time']:.3f}s")
                print(f"  最快时间: {data['min_time']:.3f}s")
                print(f"  最慢时间: {data['max_time']:.3f}s")
            else:
                print(f"\n【{method.upper()}】")
                print(f"  状态: 测试失败")
                print(f"  错误: {data.get('error', '未知错误')}")
        
        # 打印对比结果
        if "comparison" in results:
            comp = results["comparison"]
            print(f"\n【对比结果】")
            print(f"  最快方法: {comp.get('fastest_method', 'N/A')}")
            print(f"  最可靠方法: {comp.get('most_reliable_method', 'N/A')}")
            print(f"  推荐方法: {comp.get('recommended_method', 'N/A')}")
            
            print(f"\n【性能排名】")
            for rank_data in comp.get("performance_ranking", []):
                print(f"  {rank_data['rank']}. {rank_data['method']}: {rank_data['average_time']:.3f}s")
            
            print(f"\n【可靠性排名】")
            for rank_data in comp.get("reliability_ranking", []):
                print(f"  {rank_data['rank']}. {rank_data['method']}: {rank_data['success_rate']:.2%}")


async def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    benchmark = MermaidValidationBenchmark()
    results = await benchmark.run_comprehensive_benchmark()
    benchmark.print_results(results)


if __name__ == "__main__":
    asyncio.run(main())
