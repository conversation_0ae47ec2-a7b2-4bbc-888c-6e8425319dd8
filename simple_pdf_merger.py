#!/usr/bin/env python3
"""
简化版PDF合并工具
专门用于合并案件PDF文件
"""

import os
import glob
from pathlib import Path

try:
    from PyPDF2 import PdfReader, PdfWriter
except ImportError:
    try:
        import PyPDF2
        from PyPDF2 import PdfFileReader as PdfReader, PdfFileWriter as PdfWriter
    except ImportError:
        print("❌ 需要安装PyPDF2库")
        print("请运行: pip install PyPDF2")
        exit(1)

def merge_case_pdfs(case_id, input_dir=".", output_dir=None):
    """
    合并指定案件的PDF文件
    
    Args:
        case_id: 案件编号，如 "A4401171601002021036001"
        input_dir: 输入目录
        output_dir: 输出目录（默认为输入目录）
    
    Returns:
        bool: 是否成功
    """
    if output_dir is None:
        output_dir = input_dir
    
    # 查找匹配的PDF文件
    pattern = os.path.join(input_dir, f"{case_id}_*.pdf")
    files = glob.glob(pattern)
    
    if not files:
        print(f"❌ 没有找到案件 {case_id} 的PDF文件")
        return False
    
    # 按文件名排序
    files.sort()
    
    print(f"🔍 找到案件 {case_id} 的 {len(files)} 个PDF文件:")
    for file in files:
        print(f"  - {os.path.basename(file)}")
    
    # 输出文件路径
    output_file = os.path.join(output_dir, f"{case_id}.pdf")
    
    try:
        writer = PdfWriter()
        total_pages = 0
        
        print(f"\n📄 开始合并...")
        
        for file_path in files:
            print(f"  处理: {os.path.basename(file_path)}")
            
            try:
                reader = PdfReader(file_path)
                page_count = len(reader.pages)
                total_pages += page_count
                
                # 添加所有页面
                for page in reader.pages:
                    writer.add_page(page)
                
                print(f"    ✅ 已添加 {page_count} 页")
                
            except Exception as e:
                print(f"    ❌ 处理失败: {e}")
                return False
        
        # 写入合并后的PDF
        with open(output_file, 'wb') as output:
            writer.write(output)
        
        print(f"\n✅ 合并完成!")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 总页数: {total_pages}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合并失败: {e}")
        return False

def merge_all_cases_in_directory(input_dir=".", output_dir=None):
    """
    合并目录中所有案件的PDF文件
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录（默认为输入目录）
    
    Returns:
        dict: 合并结果统计
    """
    if output_dir is None:
        output_dir = input_dir
    
    # 查找所有带编号的PDF文件
    pattern = os.path.join(input_dir, "*_*.pdf")
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 没有找到需要合并的PDF文件")
        return {"success": 0, "failed": 0}
    
    # 提取案件编号
    case_ids = set()
    for file in files:
        basename = os.path.basename(file)
        if '_' in basename:
            case_id = basename.split('_')[0]
            case_ids.add(case_id)
    
    print(f"🔍 找到 {len(case_ids)} 个案件需要合并PDF:")
    for case_id in sorted(case_ids):
        print(f"  - {case_id}")
    
    results = {"success": 0, "failed": 0}
    
    for case_id in sorted(case_ids):
        print(f"\n{'='*60}")
        print(f"📁 处理案件: {case_id}")
        print("="*60)
        
        if merge_case_pdfs(case_id, input_dir, output_dir):
            results["success"] += 1
        else:
            results["failed"] += 1
    
    return results

def main():
    """主函数 - 示例用法"""
    
    print("🔧 案件PDF合并工具")
    print("="*50)
    
    # 示例1: 合并特定案件
    case_id = "A4401171601002021036001"
    print(f"示例1: 合并案件 {case_id}")
    merge_case_pdfs(case_id)
    
    print(f"\n{'='*50}")
    
    # 示例2: 合并当前目录所有案件
    print("示例2: 合并当前目录所有案件")
    results = merge_all_cases_in_directory()
    
    print(f"\n📊 合并结果:")
    print(f"  ✅ 成功: {results['success']} 个")
    print(f"  ❌ 失败: {results['failed']} 个")

# 便捷函数
def quick_merge(case_id, directory="."):
    """
    快速合并指定案件的PDF
    
    Args:
        case_id: 案件编号
        directory: 目录路径
    
    Example:
        quick_merge("A4401171601002021036001")
        quick_merge("A4401171601002021036001", "/path/to/pdfs")
    """
    return merge_case_pdfs(case_id, directory)

def batch_merge(directory="."):
    """
    批量合并目录中所有案件的PDF
    
    Args:
        directory: 目录路径
    
    Example:
        batch_merge()
        batch_merge("/path/to/pdfs")
    """
    return merge_all_cases_in_directory(directory)

if __name__ == "__main__":
    # 检查命令行参数
    import sys
    
    if len(sys.argv) == 1:
        print("🔧 案件PDF合并工具")
        print("="*50)
        print("使用方法:")
        print()
        print("1. 在Python代码中使用:")
        print("   from simple_pdf_merger import quick_merge, batch_merge")
        print("   quick_merge('A4401171601002021036001')  # 合并特定案件")
        print("   batch_merge()  # 合并当前目录所有案件")
        print()
        print("2. 命令行使用:")
        print("   python simple_pdf_merger.py A4401171601002021036001  # 合并特定案件")
        print("   python simple_pdf_merger.py --all  # 合并所有案件")
        print()
        print("文件命名规则:")
        print("  输入: A4401171601002021036001_01.pdf, A4401171601002021036001_02.pdf, ...")
        print("  输出: A4401171601002021036001.pdf")
        
    elif len(sys.argv) == 2:
        if sys.argv[1] == "--all":
            # 合并所有案件
            results = batch_merge()
            print(f"\n🎉 批量合并完成!")
            print(f"✅ 成功: {results['success']} 个")
            print(f"❌ 失败: {results['failed']} 个")
        else:
            # 合并特定案件
            case_id = sys.argv[1]
            if quick_merge(case_id):
                print(f"\n🎉 案件 {case_id} 合并完成!")
            else:
                print(f"\n❌ 案件 {case_id} 合并失败!")
    else:
        print("❌ 参数错误")
        print("使用方法: python simple_pdf_merger.py [案件编号|--all]")
