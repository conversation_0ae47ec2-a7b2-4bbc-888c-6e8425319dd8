#!/usr/bin/env python3
"""
调试并行处理时间问题
精确分析任务启动和完成时间
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


async def simulate_parallel_processing():
    """模拟并行处理来验证逻辑"""
    
    print("=" * 80)
    print("🔍 并行处理逻辑调试")
    print("=" * 80)
    
    # 模拟3个分片批
    batch_slices = [
        {'ppid': 1, 'ajbh_list': ['case1', 'case2']},
        {'ppid': 2, 'ajbh_list': ['case3', 'case4']}, 
        {'ppid': 3, 'ajbh_list': ['case5']}
    ]
    
    # 结果存储
    completed_ocr = {}
    completed_extraction = {}
    
    # 创建事件来协调任务
    ocr_events = {slice_info['ppid']: asyncio.Event() for slice_info in batch_slices}
    
    # 创建队列来管理OCR任务的顺序
    ocr_queue = asyncio.Queue()
    for slice_info in batch_slices:
        await ocr_queue.put(slice_info)
    
    # 信号量控制并发数
    extraction_semaphore = asyncio.Semaphore(3)
    
    def log_with_time(message):
        """带精确时间戳的日志"""
        now = datetime.now()
        print(f"{now.strftime('%H:%M:%S')}.{now.microsecond//1000:03d} - {message}")
    
    async def mock_ocr_worker():
        """模拟OCR工作器"""
        while True:
            try:
                slice_info = await asyncio.wait_for(ocr_queue.get(), timeout=1.0)
                ppid = slice_info['ppid']
                ajbh_list = slice_info['ajbh_list']
                
                log_with_time(f"🔄 OCR工作器开始处理分片批 {ppid}, 案件数: {len(ajbh_list)}")
                
                # 模拟OCR处理时间
                await asyncio.sleep(0.5)  # 模拟500ms的OCR处理时间
                
                # 存储OCR结果
                ocr_result = {"status": "success", "success_ajbh": ajbh_list}
                completed_ocr[ppid] = {
                    'slice_info': slice_info,
                    'ocr_result': ocr_result,
                    'completed_time': asyncio.get_event_loop().time()
                }
                
                log_with_time(f"✅ OCR分片批 {ppid} 完成，状态: success")
                
                # 立即通知对应的要素提取任务可以开始
                ocr_events[ppid].set()
                log_with_time(f"📢 通知要素提取分片批 {ppid} 可以开始")
                
                # 标记任务完成
                ocr_queue.task_done()
                
            except asyncio.TimeoutError:
                log_with_time("🏁 OCR工作器完成所有任务")
                break
            except Exception as e:
                log_with_time(f"❌ OCR工作器异常: {e}")
                break
    
    async def mock_extraction_worker(slice_info):
        """模拟要素提取工作器"""
        ppid = slice_info['ppid']
        
        # 等待对应的OCR分片批完成
        log_with_time(f"⏳ 要素提取工作器 {ppid} 等待OCR完成...")
        await ocr_events[ppid].wait()
        
        # OCR完成后立即开始要素提取
        log_with_time(f"🚀 要素提取分片批 {ppid} 开始（OCR已完成）")
        
        async with extraction_semaphore:
            log_with_time(f"📝 要素提取分片批 {ppid} 获得信号量，开始处理")
            
            # 模拟要素提取处理时间
            await asyncio.sleep(1.0)  # 模拟1秒的要素提取时间
            
            # 存储要素提取结果
            extraction_result = {"successful_count": len(slice_info['ajbh_list']), "failed_count": 0}
            completed_extraction[ppid] = extraction_result
            
            log_with_time(f"✅ 要素提取分片批 {ppid} 完成")
            
            return ppid, extraction_result
    
    # 创建任务
    log_with_time("🚀 创建并行任务...")
    
    # 1. 创建OCR工作器任务
    ocr_task = asyncio.create_task(mock_ocr_worker())
    log_with_time("📋 OCR工作器任务已创建")
    
    # 2. 创建要素提取工作器任务
    extraction_tasks = []
    for slice_info in batch_slices:
        task = asyncio.create_task(mock_extraction_worker(slice_info))
        extraction_tasks.append(task)
        log_with_time(f"📋 要素提取工作器 {slice_info['ppid']} 任务已创建")
    
    log_with_time("🎯 所有任务已创建，开始并行执行...")
    
    # 3. 并行执行所有任务
    all_tasks = [ocr_task] + extraction_tasks
    await asyncio.gather(*all_tasks, return_exceptions=True)
    
    log_with_time("🏁 所有任务完成")
    
    # 分析结果
    print(f"\n📊 并行处理结果分析:")
    print(f"  OCR完成分片批: {len(completed_ocr)}")
    print(f"  要素提取完成分片批: {len(completed_extraction)}")
    
    # 分析时间戳
    print(f"\n⏱️  时间戳分析:")
    print(f"  查看上面的日志，验证以下并行模式:")
    print(f"  ✅ 正确模式: OCR片批1完成 → 立即(几毫秒内)启动OCR片批2 + 要素提取片批1")
    print(f"  ❌ 错误模式: OCR片批1完成 → 等待 → OCR片批2完成 → 要素提取片批1开始")


async def test_event_timing():
    """测试事件通知的时间"""
    
    print(f"\n" + "=" * 80)
    print("🔍 事件通知时间测试")
    print("=" * 80)
    
    def log_with_time(message):
        now = datetime.now()
        print(f"{now.strftime('%H:%M:%S')}.{now.microsecond//1000:03d} - {message}")
    
    # 创建事件
    event1 = asyncio.Event()
    event2 = asyncio.Event()
    
    async def setter():
        """设置事件的任务"""
        log_with_time("📤 开始设置事件...")
        
        await asyncio.sleep(0.1)  # 模拟100ms处理时间
        log_with_time("📢 设置事件1")
        event1.set()
        
        await asyncio.sleep(0.1)  # 模拟100ms处理时间
        log_with_time("📢 设置事件2")
        event2.set()
    
    async def waiter1():
        """等待事件1的任务"""
        log_with_time("⏳ 等待事件1...")
        await event1.wait()
        log_with_time("🚀 事件1收到，立即开始处理")
        await asyncio.sleep(0.2)  # 模拟200ms处理时间
        log_with_time("✅ 任务1完成")
    
    async def waiter2():
        """等待事件2的任务"""
        log_with_time("⏳ 等待事件2...")
        await event2.wait()
        log_with_time("🚀 事件2收到，立即开始处理")
        await asyncio.sleep(0.2)  # 模拟200ms处理时间
        log_with_time("✅ 任务2完成")
    
    # 并行执行
    log_with_time("🎯 开始事件通知测试...")
    
    tasks = [
        asyncio.create_task(setter()),
        asyncio.create_task(waiter1()),
        asyncio.create_task(waiter2())
    ]
    
    await asyncio.gather(*tasks)
    
    log_with_time("🏁 事件通知测试完成")
    
    print(f"\n💡 预期的时间戳模式:")
    print(f"  设置事件1 → 立即(几毫秒内) → 事件1收到，立即开始处理")
    print(f"  设置事件2 → 立即(几毫秒内) → 事件2收到，立即开始处理")


async def main():
    """主函数"""
    print("并行处理逻辑调试工具")
    print(f"运行时间: {datetime.now()}")
    
    # 1. 模拟并行处理
    await simulate_parallel_processing()
    
    # 2. 测试事件通知时间
    await test_event_timing()
    
    print(f"\n" + "=" * 80)
    print("🎯 调试总结")
    print("=" * 80)
    
    print(f"如果模拟测试显示正确的并行模式，但实际系统仍然串行，")
    print(f"可能的原因:")
    print(f"  1. 要素提取工作器启动时机问题")
    print(f"  2. asyncio.gather() 的执行顺序")
    print(f"  3. 信号量等待导致的延迟")
    print(f"  4. 任务创建和启动的时间差")
    
    print(f"\n💡 解决方案:")
    print(f"  1. 确保要素提取工作器在系统启动时就开始等待")
    print(f"  2. 使用更大的extraction_semaphore值")
    print(f"  3. 优化事件通知机制")
    print(f"  4. 添加更详细的时间戳日志")


if __name__ == "__main__":
    asyncio.run(main())
