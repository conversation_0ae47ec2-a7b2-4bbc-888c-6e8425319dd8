{% extends "base.html" %}

{% block title %}统计分析 - 案件关系图查看器{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        transition: transform 0.3s;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .chart-container {
        position: relative;
        height: 400px;
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    
    .batch-card {
        transition: all 0.3s;
        border-left: 4px solid #007bff;
    }
    
    .batch-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateX(5px);
    }
    
    .progress-custom {
        height: 8px;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4"><i class="bi bi-bar-chart"></i> 统计分析</h2>

        <!-- 状态统计卡片 -->
        <div class="row mb-4">
            {% for stat in status_stats %}
            <div class="col-md-2 col-sm-4 col-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="card-title">{{ stat.count }}</h3>
                        <p class="card-text">{{ stat.status_text }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 图表区域 -->
        <div class="row mb-4">
            <!-- 状态分布饼图 -->
            <div class="col-md-6 mb-4">
                <div class="chart-container">
                    <h5 class="text-center mb-3">状态分布</h5>
                    <canvas id="statusChart"></canvas>
                </div>
            </div>

            <!-- 每日处理趋势图 -->
            <div class="col-md-6 mb-4">
                <div class="chart-container">
                    <h5 class="text-center mb-3">每日处理趋势</h5>
                    <canvas id="dailyChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 批次统计 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-layers"></i> 批次处理统计</h5>
            </div>
            <div class="card-body">
                {% if batch_stats %}
                <div class="row">
                    {% for batch in batch_stats %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card batch-card">
                            <div class="card-body">
                                <h6 class="card-title text-truncate" title="{{ batch.batchid }}">
                                    {{ batch.batchid }}
                                </h6>
                                <div class="row text-center mb-2">
                                    <div class="col-3">
                                        <small class="text-muted">总数</small>
                                        <div class="fw-bold">{{ batch.count }}</div>
                                    </div>
                                    <div class="col-3">
                                        <small class="text-muted">完成</small>
                                        <div class="fw-bold text-success">{{ batch.completed }}</div>
                                    </div>
                                    <div class="col-3">
                                        <small class="text-muted">失败</small>
                                        <div class="fw-bold text-danger">{{ batch.failed }}</div>
                                    </div>
                                    <div class="col-3">
                                        <small class="text-muted">错误</small>
                                        <div class="fw-bold text-warning">{{ batch.error_count }}</div>
                                    </div>
                                </div>

                                <!-- 成功率进度条 -->
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <small>成功率</small>
                                        <small>{{ "%.1f"|format(batch.success_rate) }}%</small>
                                    </div>
                                    <div class="progress progress-custom">
                                        <div class="progress-bar bg-success"
                                             style="width: {{ batch.success_rate }}%"></div>
                                    </div>
                                </div>

                                <!-- 错误率进度条 -->
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <small>错误率</small>
                                        <small>{{ "%.1f"|format(batch.error_rate) }}%</small>
                                    </div>
                                    <div class="progress progress-custom">
                                        <div class="progress-bar bg-warning"
                                             style="width: {{ batch.error_rate }}%"></div>
                                    </div>
                                </div>

                                <div class="text-muted">
                                    <small>
                                        <i class="bi bi-clock"></i>
                                        {{ batch.start_time_formatted }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无批次数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 状态分布饼图
const statusData = {
    labels: [{% for stat in status_stats %}'{{ stat.status_text }}'{% if not loop.last %},{% endif %}{% endfor %}],
    datasets: [{
        data: [{% for stat in status_stats %}{{ stat.count }}{% if not loop.last %},{% endif %}{% endfor %}],
        backgroundColor: [
            '#e3f2fd',
            '#f3e5f5', 
            '#e8f5e8',
            '#e8f5e8',
            '#ffebee'
        ],
        borderColor: [
            '#1976d2',
            '#7b1fa2',
            '#388e3c', 
            '#2e7d32',
            '#d32f2f'
        ],
        borderWidth: 2
    }]
};

const statusChart = new Chart(document.getElementById('statusChart'), {
    type: 'doughnut',
    data: statusData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// 每日处理趋势图
const dailyData = {
    labels: [{% for stat in daily_stats %}'{{ stat.date }}'{% if not loop.last %},{% endif %}{% endfor %}],
    datasets: [{
        label: '处理数量',
        data: [{% for stat in daily_stats %}{{ stat.count }}{% if not loop.last %},{% endif %}{% endfor %}],
        borderColor: '#667eea',
        backgroundColor: 'rgba(102, 126, 234, 0.1)',
        tension: 0.4,
        fill: true
    }]
};

const dailyChart = new Chart(document.getElementById('dailyChart'), {
    type: 'line',
    data: dailyData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// 自动刷新数据（每30秒）
setInterval(() => {
    location.reload();
}, 30000);
</script>
{% endblock %}
