#!/usr/bin/env python3
"""
Excel转PDF工具测试脚本
创建测试数据并验证转换功能
"""

import pandas as pd
import os
import sys
from datetime import datetime

def create_test_excel():
    """创建测试Excel文件"""
    
    print("📊 创建测试Excel文件...")
    
    # 测试数据
    test_data = [
        {
            '案件编号': 'CASE001',
            '数据版本号': 'V1.0',
            '正文内容': '这是第一个案件的正文内容。包含了案件的基本情况和相关描述。\n这里是第二段内容，用于测试换行处理。',
            '到案情况': '嫌疑人张某已于2025年1月15日主动到案，配合调查。',
            '依法侦查查明': '经过详细侦查，查明嫌疑人在2024年12月期间实施了相关违法行为。',
            '犯罪证据': '现有证据包括：\n1. 监控录像\n2. 证人证言\n3. 物证材料',
            '综上所述': '综合以上调查情况和证据材料，认定事实清楚，证据确凿。',
            '其他说明': '案件处理过程中，嫌疑人态度良好，积极配合调查工作。'
        },
        {
            '案件编号': 'CASE002',
            '数据版本号': 'V1.1',
            '正文内容': '第二个案件涉及经济纠纷，情况较为复杂。需要进一步调查取证。',
            '到案情况': '当事人李某尚未到案，正在联系中。',
            '依法侦查查明': '初步调查显示存在合同纠纷，涉及金额较大。',
            '犯罪证据': '目前收集的证据有限，需要补充调查。',
            '综上所述': '案件仍在调查阶段，需要进一步收集证据。',
            '其他说明': '建议加快调查进度，尽快联系相关当事人。'
        },
        {
            '案件编号': 'CASE003',
            '数据版本号': 'V2.0',
            '正文内容': '第三个案件为交通事故案件，事实相对清楚。',
            '到案情况': '双方当事人均已到案，正在协商处理。',
            '依法侦查查明': '事故发生在2025年1月20日上午，责任划分明确。',
            '犯罪证据': '交通事故认定书、现场照片、医疗证明等证据齐全。',
            '综上所述': '事故责任清楚，建议按照相关法规处理。',
            '其他说明': '双方当事人对责任认定无异议。'
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    test_excel_path = 'test_data.xlsx'
    df.to_excel(test_excel_path, index=False)
    
    print(f"✅ 测试Excel文件已创建: {test_excel_path}")
    print(f"   包含 {len(test_data)} 条测试记录")
    
    return test_excel_path

def test_conversion():
    """测试转换功能"""
    
    print(f"\n🧪 开始测试Excel转PDF功能")
    print("="*50)
    
    # 创建测试Excel文件
    excel_path = create_test_excel()
    
    # 设置输出目录
    output_dir = './test_pdf_output'
    
    try:
        # 导入转换器
        from main_excel_to_pdf import ExcelToPDFConverter
        
        print(f"\n🔄 开始转换...")
        print(f"   Excel文件: {excel_path}")
        print(f"   输出目录: {output_dir}")
        
        # 创建转换器
        converter = ExcelToPDFConverter(excel_path, output_dir)
        
        # 执行转换
        success = converter.convert_all()
        
        if success:
            print(f"\n🎉 转换成功！")
            
            # 检查生成的PDF文件
            if os.path.exists(output_dir):
                pdf_files = [f for f in os.listdir(output_dir) if f.endswith('.pdf')]
                print(f"✅ 生成的PDF文件:")
                for pdf_file in pdf_files:
                    file_path = os.path.join(output_dir, pdf_file)
                    file_size = os.path.getsize(file_path)
                    print(f"   - {pdf_file} ({file_size} bytes)")
                
                print(f"\n📁 PDF文件保存在: {os.path.abspath(output_dir)}")
            else:
                print(f"⚠️  输出目录不存在")
        else:
            print(f"\n❌ 转换失败")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print(f"请确保已安装必要的依赖: pip install pandas openpyxl reportlab")
        return False
    except Exception as e:
        print(f"❌ 转换过程出错: {e}")
        return False
    
    return success

def test_config():
    """测试配置功能"""
    
    print(f"\n🔧 测试配置功能")
    print("="*30)
    
    try:
        from excel_to_pdf_config import get_config, validate_config
        
        # 验证配置
        errors = validate_config()
        if errors:
            print(f"❌ 配置验证失败:")
            for error in errors:
                print(f"   - {error}")
            return False
        else:
            print(f"✅ 配置验证通过")
        
        # 显示配置
        config = get_config()
        print(f"\n📋 当前配置:")
        print(f"   PDF文件名字段: {config['excel']['pdf_name_fields']}")
        print(f"   内容字段数量: {len(config['excel']['content_fields'])}")
        print(f"   默认输出目录: {config['path']['default_output_dir']}")
        
        return True
        
    except ImportError:
        print(f"⚠️  配置文件不存在，将使用默认配置")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    
    print(f"\n🧹 清理测试文件...")
    
    # 删除测试Excel文件
    test_files = ['test_data.xlsx']
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ✅ 已删除: {file_path}")
            except Exception as e:
                print(f"   ❌ 删除失败 {file_path}: {e}")

def main():
    """主函数"""
    
    print("🧪 Excel转PDF工具测试")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    tests = [
        ("配置功能", test_config),
        ("转换功能", test_conversion)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试项目: {test_name}")
        print("="*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！Excel转PDF工具可以正常使用")
        print(f"\n🚀 使用方法:")
        print(f"   python main_excel_to_pdf.py your_excel_file.xlsx output_directory")
    else:
        print(f"\n⚠️  部分测试失败，请检查依赖和配置")
        print(f"\n📦 安装依赖:")
        print(f"   pip install pandas openpyxl reportlab")
    
    # 询问是否清理测试文件
    if input(f"\n是否清理测试文件? (y/N): ").strip().lower() == 'y':
        cleanup_test_files()
    
    return passed == total

if __name__ == "__main__":
    main()
