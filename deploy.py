#!/usr/bin/env python3
"""
系统部署脚本
自动化部署和环境检查
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


class SystemDeployer:
    """系统部署器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.required_dirs = [
            "logs",
            "/bigai/ai/MonkeyOCR"
        ]
        
    def print_step(self, step, message):
        """打印步骤信息"""
        print(f"\n{'='*60}")
        print(f"步骤 {step}: {message}")
        print('='*60)
    
    def check_python_version(self):
        """检查Python版本"""
        self.print_step(1, "检查Python版本")
        
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print(f"❌ Python版本过低: {version.major}.{version.minor}")
            print("需要Python 3.8或更高版本")
            return False
        
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def install_dependencies(self):
        """安装依赖包"""
        self.print_step(2, "安装Python依赖包")
        
        requirements_file = self.project_root / "requirements.txt"
        
        if not requirements_file.exists():
            print("❌ requirements.txt文件不存在")
            return False
        
        try:
            # 升级pip
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            print("✅ pip已升级到最新版本")
            
            # 安装依赖
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
                         check=True, capture_output=True)
            print("✅ 依赖包安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖包安装失败: {e}")
            return False
    
    def create_directories(self):
        """创建必要的目录"""
        self.print_step(3, "创建系统目录")
        
        success = True
        
        for dir_path in self.required_dirs:
            try:
                path = Path(dir_path)
                path.mkdir(parents=True, exist_ok=True)
                
                # 设置权限
                if os.name != 'nt':  # 非Windows系统
                    os.chmod(path, 0o755)
                
                print(f"✅ 目录创建成功: {dir_path}")
                
            except Exception as e:
                print(f"❌ 目录创建失败: {dir_path} - {e}")
                success = False
        
        return success
    
    def check_docker_environment(self):
        """检查Docker环境"""
        self.print_step(4, "检查Docker环境")
        
        try:
            # 检查Docker是否安装
            result = subprocess.run(["docker", "--version"], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ Docker版本: {result.stdout.strip()}")
            
            # 检查Docker是否运行
            subprocess.run(["docker", "info"], 
                         capture_output=True, check=True)
            print("✅ Docker服务正在运行")
            
            # 检查MonkeyOCR容器
            try:
                result = subprocess.run(["docker", "ps", "-a", "--filter", "name=monkeyocr"], 
                                      capture_output=True, text=True, check=True)
                if "monkeyocr" in result.stdout:
                    print("✅ MonkeyOCR容器存在")
                else:
                    print("⚠️  MonkeyOCR容器不存在，请手动创建")
            except:
                print("⚠️  无法检查MonkeyOCR容器状态")
            
            # 检查Mermaid CLI镜像
            try:
                result = subprocess.run(["docker", "images", "--filter", "reference=minlag/mermaid-cli"], 
                                      capture_output=True, text=True, check=True)
                if "minlag/mermaid-cli" in result.stdout:
                    print("✅ Mermaid CLI镜像存在")
                else:
                    print("⚠️  Mermaid CLI镜像不存在，正在拉取...")
                    subprocess.run(["docker", "pull", "minlag/mermaid-cli"], check=True)
                    print("✅ Mermaid CLI镜像拉取完成")
            except:
                print("⚠️  无法拉取Mermaid CLI镜像")
            
            return True
            
        except subprocess.CalledProcessError:
            print("❌ Docker未安装或未运行")
            return False
        except FileNotFoundError:
            print("❌ Docker命令未找到，请安装Docker")
            return False
    
    def setup_database(self):
        """设置数据库"""
        self.print_step(5, "检查数据库配置")
        
        try:
            # 导入配置检查数据库连接
            sys.path.insert(0, str(self.project_root))
            from config import config
            from data_fetcher import DataFetcher
            
            # 测试数据库连接
            fetcher = DataFetcher()
            connection = fetcher.get_connection()
            connection.close()
            
            print("✅ 数据库连接正常")
            
            # 检查数据库表
            schema_file = self.project_root / "database_schema.sql"
            if schema_file.exists():
                print(f"✅ 数据库脚本存在: {schema_file}")
                print("请手动执行数据库脚本创建必要的表结构")
            else:
                print("⚠️  数据库脚本不存在")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据库配置检查失败: {e}")
            print("请检查config.py中的数据库配置")
            return False
    
    def create_service_scripts(self):
        """创建服务脚本"""
        self.print_step(6, "创建服务脚本")
        
        # 创建启动脚本
        start_script = self.project_root / "start_service.sh"
        start_content = f"""#!/bin/bash
# 案件处理系统启动脚本

cd {self.project_root}

echo "启动案件处理系统..."
python3 start.py
"""
        
        try:
            with open(start_script, 'w', encoding='utf-8') as f:
                f.write(start_content)
            
            # 设置执行权限
            if os.name != 'nt':
                os.chmod(start_script, 0o755)
            
            print(f"✅ 启动脚本创建成功: {start_script}")
            
            # 创建定时任务脚本
            cron_script = self.project_root / "setup_cron.sh"
            cron_content = f"""#!/bin/bash
# 设置定时任务

# 每小时执行一次
echo "0 * * * * cd {self.project_root} && python3 main_controller.py 1 >> logs/cron_hourly.log 2>&1" | crontab -

echo "定时任务设置完成"
echo "查看当前定时任务:"
crontab -l
"""
            
            with open(cron_script, 'w', encoding='utf-8') as f:
                f.write(cron_content)
            
            if os.name != 'nt':
                os.chmod(cron_script, 0o755)
            
            print(f"✅ 定时任务脚本创建成功: {cron_script}")
            
            return True
            
        except Exception as e:
            print(f"❌ 服务脚本创建失败: {e}")
            return False
    
    def run_system_test(self):
        """运行系统测试"""
        self.print_step(7, "运行系统测试")
        
        try:
            # 运行测试脚本
            result = subprocess.run([sys.executable, "test_system.py"], 
                                  cwd=self.project_root, 
                                  capture_output=True, text=True)
            
            print("测试输出:")
            print(result.stdout)
            
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            if result.returncode == 0:
                print("✅ 系统测试通过")
                return True
            else:
                print("❌ 系统测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 系统测试异常: {e}")
            return False
    
    def deploy(self):
        """执行部署"""
        print("开始部署案件处理系统...")
        
        steps = [
            ("检查Python版本", self.check_python_version),
            ("安装依赖包", self.install_dependencies),
            ("创建目录", self.create_directories),
            ("检查Docker环境", self.check_docker_environment),
            ("检查数据库", self.setup_database),
            ("创建服务脚本", self.create_service_scripts),
            ("运行系统测试", self.run_system_test),
        ]
        
        success_count = 0
        total_count = len(steps)
        
        for step_name, step_func in steps:
            try:
                if step_func():
                    success_count += 1
                else:
                    print(f"⚠️  步骤失败: {step_name}")
            except Exception as e:
                print(f"❌ 步骤异常: {step_name} - {e}")
        
        # 部署结果
        print("\n" + "="*60)
        print("部署结果汇总")
        print("="*60)
        print(f"总步骤数: {total_count}")
        print(f"成功步骤: {success_count}")
        print(f"失败步骤: {total_count - success_count}")
        
        if success_count == total_count:
            print("\n🎉 部署完成！系统已准备就绪。")
            print("\n使用方法:")
            print("1. 直接运行: python3 start.py")
            print("2. 命令行模式: python3 main_controller.py [参数]")
            print("3. 系统测试: python3 test_system.py")
            
            if os.name != 'nt':
                print("4. 启动服务: ./start_service.sh")
                print("5. 设置定时任务: ./setup_cron.sh")
        else:
            print(f"\n⚠️  部署未完全成功，有 {total_count - success_count} 个步骤失败。")
            print("请检查错误信息并手动修复相关问题。")
        
        return success_count == total_count


def main():
    """主函数"""
    deployer = SystemDeployer()
    success = deployer.deploy()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
