-- 修复缺失字段的SQL语句
-- 用于解决 "Unknown column 'llsj' in 'field list'" 错误

-- 检查并添加案件关系表的缺失字段
-- 如果字段已存在，MySQL会报错但不会影响其他操作

-- 为 ds_case_relation_graph 表添加缺失字段
ALTER TABLE ds_case_relation_graph 
ADD COLUMN IF NOT EXISTS llsj DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间';

ALTER TABLE ds_case_relation_graph 
ADD COLUMN IF NOT EXISTS is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除';

ALTER TABLE ds_case_relation_graph 
ADD COLUMN IF NOT EXISTS gxsj_update DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 为 ds_case_details 表添加缺失字段
ALTER TABLE ds_case_details 
ADD COLUMN IF NOT EXISTS llsj DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间';

ALTER TABLE ds_case_details 
ADD COLUMN IF NOT EXISTS is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除';

ALTER TABLE ds_case_details 
ADD COLUMN IF NOT EXISTS gxsj DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 如果上面的 IF NOT EXISTS 语法不支持，使用下面的兼容版本：

-- 案件关系表字段添加（兼容版本）
-- ALTER TABLE ds_case_relation_graph ADD COLUMN llsj DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间';
-- ALTER TABLE ds_case_relation_graph ADD COLUMN is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除';
-- ALTER TABLE ds_case_relation_graph ADD COLUMN gxsj_update DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 案件详细信息表字段添加（兼容版本）
-- ALTER TABLE ds_case_details ADD COLUMN llsj DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间';
-- ALTER TABLE ds_case_details ADD COLUMN is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除';
-- ALTER TABLE ds_case_details ADD COLUMN gxsj DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 验证字段是否添加成功
SELECT 'ds_case_relation_graph 表结构:' as info;
DESCRIBE ds_case_relation_graph;

SELECT 'ds_case_details 表结构:' as info;
DESCRIBE ds_case_details;

-- 检查是否有数据
SELECT COUNT(*) as relation_count FROM ds_case_relation_graph;
SELECT COUNT(*) as details_count FROM ds_case_details;
