#!/usr/bin/env python3
"""
诊断数据库中的数据问题
检查lastcode和code字段的状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from web_case_viewer import get_db_connection
    import pymysql
    
    def diagnose_database():
        """诊断数据库数据状态"""
        
        print("🔍 数据库数据诊断")
        print("="*80)
        
        try:
            with get_db_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 获取所有案件的代码字段状态
                    cursor.execute("""
                        SELECT ajbh, ajmc,
                               CASE 
                                   WHEN code IS NULL THEN 'NULL'
                                   WHEN code = '' THEN 'EMPTY'
                                   WHEN TRIM(code) = '' THEN 'WHITESPACE'
                                   ELSE 'HAS_DATA'
                               END as code_status,
                               CASE 
                                   WHEN lastcode IS NULL THEN 'NULL'
                                   WHEN lastcode = '' THEN 'EMPTY'
                                   WHEN TRIM(lastcode) = '' THEN 'WHITESPACE'
                                   ELSE 'HAS_DATA'
                               END as lastcode_status,
                               LENGTH(COALESCE(code, '')) as code_length,
                               LENGTH(COALESCE(lastcode, '')) as lastcode_length,
                               status,
                               updatetype
                        FROM ds_case_relation 
                        ORDER BY updatetime DESC
                    """)
                    
                    cases = cursor.fetchall()
                    
                    if not cases:
                        print("❌ 数据库中没有案件数据")
                        return
                    
                    print(f"📊 找到 {len(cases)} 个案件")
                    print("-" * 120)
                    print(f"{'案件编号':<20} {'案件名称':<25} {'code状态':<12} {'lastcode状态':<12} {'code长度':<8} {'lastcode长度':<12} {'状态':<6} {'类型'}")
                    print("-" * 120)
                    
                    # 统计数据
                    stats = {
                        'total': len(cases),
                        'code_null': 0,
                        'code_empty': 0,
                        'code_whitespace': 0,
                        'code_has_data': 0,
                        'lastcode_null': 0,
                        'lastcode_empty': 0,
                        'lastcode_whitespace': 0,
                        'lastcode_has_data': 0,
                        'both_empty': 0,
                        'usable_cases': 0
                    }
                    
                    problematic_cases = []
                    usable_cases = []
                    
                    for case in cases:
                        ajbh = case['ajbh']
                        ajmc = (case['ajmc'] or 'N/A')[:23]
                        code_status = case['code_status']
                        lastcode_status = case['lastcode_status']
                        code_length = case['code_length']
                        lastcode_length = case['lastcode_length']
                        status = case['status'] or 'N/A'
                        updatetype = case['updatetype'] or 'N/A'
                        
                        print(f"{ajbh:<20} {ajmc:<25} {code_status:<12} {lastcode_status:<12} {code_length:<8} {lastcode_length:<12} {status:<6} {updatetype}")
                        
                        # 统计
                        stats[f'code_{code_status.lower()}'] += 1
                        stats[f'lastcode_{lastcode_status.lower()}'] += 1
                        
                        # 检查是否可用
                        has_usable_code = (
                            lastcode_status == 'HAS_DATA' or 
                            code_status == 'HAS_DATA'
                        )
                        
                        if has_usable_code:
                            stats['usable_cases'] += 1
                            usable_cases.append(ajbh)
                        else:
                            stats['both_empty'] += 1
                            problematic_cases.append({
                                'ajbh': ajbh,
                                'ajmc': ajmc,
                                'code_status': code_status,
                                'lastcode_status': lastcode_status
                            })
                    
                    print("-" * 120)
                    print(f"\n📈 统计结果:")
                    print(f"  总案件数: {stats['total']}")
                    print(f"  可用案件数: {stats['usable_cases']}")
                    print(f"  问题案件数: {stats['both_empty']}")
                    
                    print(f"\n📋 code字段状态:")
                    print(f"  NULL: {stats['code_null']}")
                    print(f"  空字符串: {stats['code_empty']}")
                    print(f"  仅空白字符: {stats['code_whitespace']}")
                    print(f"  有数据: {stats['code_has_data']}")
                    
                    print(f"\n📋 lastcode字段状态:")
                    print(f"  NULL: {stats['lastcode_null']}")
                    print(f"  空字符串: {stats['lastcode_empty']}")
                    print(f"  仅空白字符: {stats['lastcode_whitespace']}")
                    print(f"  有数据: {stats['lastcode_has_data']}")
                    
                    if problematic_cases:
                        print(f"\n❌ 问题案件列表 (两个字段都为空):")
                        for case in problematic_cases[:10]:  # 只显示前10个
                            print(f"  - {case['ajbh']}: {case['ajmc']} (code: {case['code_status']}, lastcode: {case['lastcode_status']})")
                        if len(problematic_cases) > 10:
                            print(f"  ... 还有 {len(problematic_cases) - 10} 个问题案件")
                    
                    if usable_cases:
                        print(f"\n✅ 可用案件列表 (前10个):")
                        for ajbh in usable_cases[:10]:
                            print(f"  - {ajbh}")
                            print(f"    测试URL: http://localhost:5000/mermaid/{ajbh}")
                        if len(usable_cases) > 10:
                            print(f"  ... 还有 {len(usable_cases) - 10} 个可用案件")
                    
                    return stats, problematic_cases, usable_cases
                    
        except Exception as e:
            print(f"❌ 诊断失败: {e}")
            return None, None, None
    
    def check_specific_case(ajbh):
        """检查特定案件的详细信息"""
        
        print(f"\n🔍 检查案件 {ajbh} 的详细信息")
        print("-" * 60)
        
        try:
            with get_db_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute("""
                        SELECT ajbh, ajmc, code, lastcode, status, updatetype,
                               LENGTH(COALESCE(code, '')) as code_length,
                               LENGTH(COALESCE(lastcode, '')) as lastcode_length
                        FROM ds_case_relation 
                        WHERE ajbh = %s
                    """, (ajbh,))
                    
                    case = cursor.fetchone()
                    
                    if not case:
                        print(f"❌ 案件 {ajbh} 不存在")
                        return
                    
                    print(f"案件编号: {case['ajbh']}")
                    print(f"案件名称: {case['ajmc']}")
                    print(f"状态: {case['status']}")
                    print(f"修改类型: {case['updatetype']}")
                    print(f"code字段长度: {case['code_length']}")
                    print(f"lastcode字段长度: {case['lastcode_length']}")
                    
                    if case['code']:
                        print(f"code字段预览: {case['code'][:100]}...")
                    else:
                        print(f"code字段: NULL 或空")
                    
                    if case['lastcode']:
                        print(f"lastcode字段预览: {case['lastcode'][:100]}...")
                    else:
                        print(f"lastcode字段: NULL 或空")
                    
                    # 检查是否可用
                    has_code = case['code'] and case['code'].strip()
                    has_lastcode = case['lastcode'] and case['lastcode'].strip()
                    
                    print(f"\n可用性检查:")
                    print(f"  code可用: {'✅' if has_code else '❌'}")
                    print(f"  lastcode可用: {'✅' if has_lastcode else '❌'}")
                    print(f"  整体可用: {'✅' if (has_code or has_lastcode) else '❌'}")
                    
        except Exception as e:
            print(f"❌ 检查失败: {e}")
    
    def main():
        """主函数"""
        
        print("🧪 数据库数据诊断工具")
        print("检查lastcode和code字段对关系图功能的影响")
        
        # 诊断整体数据状态
        stats, problematic_cases, usable_cases = diagnose_database()
        
        if stats:
            # 检查几个具体案件
            if usable_cases:
                print(f"\n{'='*80}")
                print("详细检查可用案件")
                print("="*80)
                
                for ajbh in usable_cases[:3]:  # 检查前3个可用案件
                    check_specific_case(ajbh)
            
            print(f"\n{'='*80}")
            print("诊断结论")
            print("="*80)
            
            if stats['usable_cases'] > 0:
                print(f"✅ 发现 {stats['usable_cases']} 个可用案件")
                print("这些案件应该能正常打开Mermaid Live Editor")
            else:
                print("❌ 没有发现可用案件")
                print("所有案件的code和lastcode字段都为空")
            
            if stats['both_empty'] > 0:
                print(f"⚠️  发现 {stats['both_empty']} 个问题案件")
                print("这些案件无法打开Mermaid Live Editor")
            
            print(f"\n💡 建议:")
            print("1. 优先测试可用案件的关系图功能")
            print("2. 对于问题案件，检查AI处理是否完成")
            print("3. 考虑重新运行AI处理生成关系图代码")

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在html_main目录下运行此脚本")
