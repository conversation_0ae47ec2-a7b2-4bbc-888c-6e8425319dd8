#!/bin/bash
# 后台启动案件处理系统脚本

# 设置工作目录
cd /data/ai/AJagent-main

# 创建日志目录
mkdir -p logs

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "🚀 启动案件处理系统后台服务..."
echo "工作目录: $(pwd)"
echo "日志文件: logs/system_${TIMESTAMP}.log"
echo "进程ID文件: logs/system.pid"

# 使用nohup后台运行，并记录进程ID
nohup python3 start.py > logs/system_${TIMESTAMP}.log 2>&1 &

# 保存进程ID
echo $! > logs/system.pid

echo "✅ 系统已在后台启动"
echo "进程ID: $(cat logs/system.pid)"
echo ""
echo "📋 管理命令:"
echo "查看日志: tail -f logs/system_${TIMESTAMP}.log"
echo "查看进程: ps aux | grep start.py"
echo "停止服务: ./stop_background.sh"
echo "重启服务: ./restart_background.sh"
echo ""
echo "🔍 快速检查:"
echo "ps aux | grep $(cat logs/system.pid)"
