# 表名配置化说明

## 📋 功能概述

将系统中硬编码的数据库表名统一配置化管理，提高系统的可维护性和灵活性。

## 🎯 配置化的表名

### 主要表名
| 配置键 | 默认表名 | 说明 |
|--------|----------|------|
| `source_table` | `ds_case_instrument_his_ai` | 案件文书历史表（AI版本） |
| `case_relation_table` | `ds_case_relation` | 案件关系图表 |
| `case_details_table` | `ds_case_details` | 案件详细信息表 |

### 备份表名
| 配置键 | 默认表名 | 说明 |
|--------|----------|------|
| `case_relation_backup` | `ds_case_relation_backup` | 案件关系表备份 |
| `case_details_backup` | `ds_case_details_backup` | 案件详细信息表备份 |

### 视图名称
| 配置键 | 默认表名 | 说明 |
|--------|----------|------|
| `case_relation_active_view` | `ds_case_relation_active` | 案件关系活动视图 |
| `case_details_active_view` | `ds_case_details_active` | 案件详细信息活动视图 |

## 🔧 配置文件 (config.py)

### 表名配置结构
```python
self.table_config = {
    # 源数据表
    'source_table': 'ds_case_instrument_his_ai',  # 案件文书历史表（AI版本）
    
    # 主要业务表
    'case_relation_table': 'ds_case_relation',     # 案件关系图表
    'case_details_table': 'ds_case_details',       # 案件详细信息表
    
    # 备份表（如果需要）
    'case_relation_backup': 'ds_case_relation_backup',
    'case_details_backup': 'ds_case_details_backup',
    
    # 视图名称
    'case_relation_active_view': 'ds_case_relation_active',
    'case_details_active_view': 'ds_case_details_active'
}
```

### 获取表名的方法
```python
# 方法1: 获取所有表名配置
table_config = config.get_table_config()
case_relation_table = table_config['case_relation_table']

# 方法2: 获取单个表名
case_relation_table = config.get_table_name('case_relation_table')
```

## 📁 修改的文件

### 1. config.py
- ✅ 添加 `table_config` 配置字典
- ✅ 添加 `get_table_config()` 方法
- ✅ 添加 `get_table_name(table_key)` 方法

### 2. data_fetcher.py
- ✅ 在 `__init__` 中添加表名配置
- ✅ 替换所有硬编码的表名为配置化表名
- ✅ 修改SQL语句使用f-string格式

### 3. html_main/web_case_viewer.py
- ✅ 导入config模块
- ✅ 添加 `get_table_name()` 辅助函数
- ✅ 替换所有SQL语句中的硬编码表名

### 4. html_main/diagnose_database.py
- ✅ 导入config模块
- ✅ 添加表名配置变量
- ✅ 替换SQL语句中的硬编码表名

### 5. ocr_processor.py
- ✅ 在 `__init__` 中添加表名配置
- ✅ 替换UPDATE语句中的硬编码表名

### 6. case_extraction_agent.py
- ✅ 在 `__init__` 中添加表名配置
- ✅ 替换所有SQL语句中的硬编码表名

## 🔄 使用示例

### 修改前（硬编码）
```python
# 硬编码表名
sql = """
SELECT ajbh, ajmc FROM ds_case_relation 
WHERE status = '0'
"""

# 插入语句
insert_sql = """
INSERT INTO `ds_case_details` 
(batchid, ajbh, ajmc) VALUES (%s, %s, %s)
"""
```

### 修改后（配置化）
```python
# 配置化表名
sql = f"""
SELECT ajbh, ajmc FROM {self.case_relation_table} 
WHERE status = '0'
"""

# 插入语句
insert_sql = f"""
INSERT INTO `{self.case_details_table}` 
(batchid, ajbh, ajmc) VALUES (%s, %s, %s)
"""
```

## 🎯 优势

### 1. 灵活性
- ✅ 可以轻松修改表名而不需要修改代码
- ✅ 支持不同环境使用不同的表名
- ✅ 便于数据库迁移和重构

### 2. 可维护性
- ✅ 统一管理所有表名
- ✅ 减少硬编码，降低维护成本
- ✅ 避免表名修改时的遗漏

### 3. 可扩展性
- ✅ 易于添加新的表名配置
- ✅ 支持备份表和视图的配置
- ✅ 为未来功能扩展提供基础

## 🔧 修改表名的方法

### 1. 修改单个表名
编辑 `config.py` 文件：
```python
self.table_config = {
    'case_relation_table': 'new_case_relation_table',  # 修改表名
    # ... 其他配置保持不变
}
```

### 2. 添加新表名
```python
self.table_config = {
    # 现有配置...
    'new_table': 'new_table_name',  # 添加新表名
}
```

### 3. 在代码中使用新表名
```python
# 在类的__init__方法中
self.new_table = self.table_config['new_table']

# 在SQL语句中使用
sql = f"SELECT * FROM {self.new_table} WHERE id = %s"
```

## 🧪 测试验证

### 运行测试脚本
```bash
python test_table_config.py
```

### 测试内容
- ✅ 配置导入测试
- ✅ 各模块表名配置测试
- ✅ 文件内容硬编码检查
- ✅ 功能完整性验证

## ⚠️ 注意事项

### 1. SQL文件
- SQL脚本文件（.sql）中的表名仍然是硬编码的
- 这些文件主要用于数据库初始化和维护
- 如需配置化，可以考虑使用模板文件

### 2. 向后兼容
- 默认表名保持不变，确保向后兼容
- 现有数据库无需修改
- 可以逐步迁移到新的表名

### 3. 错误处理
- 如果配置的表名不存在，会产生数据库错误
- 建议在修改表名前先验证表是否存在
- 可以使用 `get_table_name()` 方法的默认值功能

## 🚀 使用建议

### 1. 开发环境
```python
# 开发环境可以使用不同的表名前缀
self.table_config = {
    'case_relation_table': 'dev_ds_case_relation',
    'case_details_table': 'dev_ds_case_details',
    # ...
}
```

### 2. 测试环境
```python
# 测试环境使用test前缀
self.table_config = {
    'case_relation_table': 'test_ds_case_relation',
    'case_details_table': 'test_ds_case_details',
    # ...
}
```

### 3. 生产环境
```python
# 生产环境保持原有表名
self.table_config = {
    'case_relation_table': 'ds_case_relation',
    'case_details_table': 'ds_case_details',
    # ...
}
```

## 🎉 总结

表名配置化功能已成功实现：

- ✅ **统一管理**: 所有表名在config.py中统一配置
- ✅ **代码清理**: 移除了所有硬编码的表名
- ✅ **向后兼容**: 默认配置保持原有表名
- ✅ **易于维护**: 修改表名只需修改配置文件
- ✅ **功能完整**: 所有相关模块都已更新

现在你可以通过修改 `config.py` 中的 `table_config` 来轻松管理所有数据库表名！
