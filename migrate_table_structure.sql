-- 数据库表结构迁移脚本
-- 将ds_case_relation表从旧结构迁移到新结构
-- 删除字段: tfsj, xgsj
-- 新增字段: tbgxsj, llsj  
-- 重命名字段: updatetype -> codesource

USE `djzs_db`;

-- 1. 备份现有数据（可选）
CREATE TABLE IF NOT EXISTS `ds_case_relation_backup` AS 
SELECT * FROM `ds_case_relation`;

-- 2. 检查当前表结构
DESCRIBE `ds_case_relation`;

-- 3. 删除旧字段（如果存在）
-- 删除填发时间字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'djzs_db' 
     AND TABLE_NAME = 'ds_case_relation' 
     AND COLUMN_NAME = 'tfsj') > 0,
    'ALTER TABLE `ds_case_relation` DROP COLUMN `tfsj`',
    'SELECT "Column tfsj does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除修改时间字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'djzs_db' 
     AND TABLE_NAME = 'ds_case_relation' 
     AND COLUMN_NAME = 'xgsj') > 0,
    'ALTER TABLE `ds_case_relation` DROP COLUMN `xgsj`',
    'SELECT "Column xgsj does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加新字段（如果不存在）
-- 添加录入时间字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'djzs_db' 
     AND TABLE_NAME = 'ds_case_relation' 
     AND COLUMN_NAME = 'llsj') = 0,
    'ALTER TABLE `ds_case_relation` ADD COLUMN `llsj` datetime DEFAULT NULL COMMENT "录入时间" AFTER `flwsxzdz`',
    'SELECT "Column llsj already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加同步更新时间字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'djzs_db' 
     AND TABLE_NAME = 'ds_case_relation' 
     AND COLUMN_NAME = 'tbgxsj') = 0,
    'ALTER TABLE `ds_case_relation` ADD COLUMN `tbgxsj` timestamp NULL DEFAULT NULL COMMENT "同步更新时间" AFTER `tbrksj`',
    'SELECT "Column tbgxsj already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 重命名字段 updatetype -> codesource
-- 检查是否存在updatetype字段，如果存在则重命名
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'djzs_db' 
     AND TABLE_NAME = 'ds_case_relation' 
     AND COLUMN_NAME = 'updatetype') > 0,
    'ALTER TABLE `ds_case_relation` CHANGE COLUMN `updatetype` `codesource` varchar(10) DEFAULT 0 COMMENT "图代码来源【0 AI生成，1人工修改】"',
    'SELECT "Column updatetype does not exist, checking codesource" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果codesource字段不存在，则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'djzs_db' 
     AND TABLE_NAME = 'ds_case_relation' 
     AND COLUMN_NAME = 'codesource') = 0,
    'ALTER TABLE `ds_case_relation` ADD COLUMN `codesource` varchar(10) DEFAULT 0 COMMENT "图代码来源【0 AI生成，1人工修改】" AFTER `updater`',
    'SELECT "Column codesource already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 确保status字段有正确的默认值
ALTER TABLE `ds_case_relation` 
MODIFY COLUMN `status` varchar(10) DEFAULT '0' COMMENT 'ai处理状态，0:开始，1:下载完成，2:ocr完成，3:ai完成，4:报错';

-- 7. 确保isdelete字段有正确的默认值
ALTER TABLE `ds_case_relation` 
MODIFY COLUMN `isdelete` varchar(10) DEFAULT '0' COMMENT '是否删除， 0 否  1是';

-- 8. 验证表结构
DESCRIBE `ds_case_relation`;

-- 9. 显示字段变更情况
SELECT 
    'Field Changes Summary' as summary,
    'Removed: tfsj, xgsj' as removed_fields,
    'Added: llsj, tbgxsj' as added_fields,
    'Renamed: updatetype -> codesource' as renamed_fields;

-- 10. 检查数据
SELECT COUNT(*) as total_records FROM `ds_case_relation`;

-- 11. 显示最新的几条记录验证结构
SELECT batchid, ajbh, ajmc, counts, llsj, tbgxsj, codesource, status, isdelete
FROM `ds_case_relation`
ORDER BY updatetime DESC
LIMIT 5;

-- 12. 创建视图简化查询（可选）
CREATE OR REPLACE VIEW `ds_case_relation_active` AS
SELECT batchid, ajbh, ajmc, counts, xxzjbh, ajlx, flwsxzdz, llsj, tbrksj, tbgxsj,
       ajnr, code, lastcode, updater, codesource, status, starttime, endtime, 
       updatetime, nums, error
FROM `ds_case_relation`
WHERE isdelete = '0';

-- 13. 验证视图
SELECT COUNT(*) as active_view_count FROM `ds_case_relation_active`;

COMMIT;

-- 使用说明：
-- 1. 执行前请确保已备份重要数据
-- 2. 此脚本会自动检查字段是否存在，避免重复操作
-- 3. 执行后请验证应用程序是否正常工作
-- 4. 如有问题可从备份表恢复数据
