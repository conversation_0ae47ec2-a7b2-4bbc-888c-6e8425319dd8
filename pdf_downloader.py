#!/usr/bin/env python3
"""
法律文书下载智能体
批量下载PDF文书，保存到指定路径并以案件编号命名
"""

import os
import requests
import logging
import asyncio
import aiohttp
import aiofiles
import pymysql
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path
from config import config


class PDFDownloader:
    """PDF下载器"""
    
    def __init__(self):
        self.db_config = config.get_db_config()
        self.table_config = config.get_table_config()
        self.system_config = config.get_system_config()
        self.logger = logging.getLogger(__name__)
        
        # 下载配置
        self.download_timeout = self.system_config['download_timeout']
        self.ocr_base_path = self.system_config['ocr_base_path']

        # 表名配置 
        self.case_relation_table = self.table_config['case_relation_table'] 
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def create_download_directory(self, batch_id: str) -> str:
        """
        创建下载目录
        
        Args:
            batch_id: 批次号
            
        Returns:
            下载目录路径
        """
        download_dir = f"{self.ocr_base_path}/{batch_id}/input"
        os.makedirs(download_dir, exist_ok=True)
        
        # 设置目录权限
        try:
            os.chmod(download_dir, 0o777)
        except:
            pass
        
        self.logger.info(f"创建下载目录: {download_dir}")
        return download_dir
    
    async def download_single_pdf(self, session: aiohttp.ClientSession,
                                 case_info: Dict[str, Any],
                                 download_dir: str) -> Dict[str, Any]:
        """
        下载单个PDF文件
        文件名格式：ajbh_xxzjbh.pdf

        Args:
            session: aiohttp会话
            case_info: 案件信息
            download_dir: 下载目录

        Returns:
            下载结果
        """
        ajbh = case_info['ajbh']
        xxzjbh = case_info['xxzjbh']
        download_url = case_info['flwsxzdz']
        file_path = os.path.join(download_dir, f"{ajbh}_{xxzjbh}.pdf")
        
        try:
            self.logger.info(f"开始下载: {ajbh}_{xxzjbh} - {download_url}")

            # 检查文件是否已存在
            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                self.logger.info(f"文件已存在，跳过下载: {file_path}")
                return {
                    "status": "success",
                    "ajbh": ajbh,
                    "xxzjbh": xxzjbh,
                    "file_path": file_path,
                    "message": "文件已存在"
                }
            
            # 下载文件
            async with session.get(download_url, timeout=self.download_timeout) as response:
                if response.status == 200:
                    # 异步写入文件
                    async with aiofiles.open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)
                    
                    # 验证文件
                    if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                        file_size = os.path.getsize(file_path)
                        self.logger.info(f"下载成功: {ajbh}_{xxzjbh} - {file_size} bytes")

                        return {
                            "status": "success",
                            "ajbh": ajbh,
                            "xxzjbh": xxzjbh,
                            "file_path": file_path,
                            "file_size": file_size,
                            "message": "下载成功"
                        }
                    else:
                        raise Exception("下载的文件为空或不存在")
                else:
                    raise Exception(f"HTTP错误: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"下载失败: {ajbh}_{xxzjbh} - {str(e)}")

            # 清理失败的文件
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass

            return {
                "status": "error",
                "ajbh": ajbh,
                "xxzjbh": xxzjbh,
                "error": str(e),
                "message": "下载失败"
            }
    
    async def batch_download_pdfs(self, batch_id: str, cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量下载PDF文件
        现在每个案件可能有多个PDF文件（每个xxzjbh对应一个文件）

        Args:
            batch_id: 批次号
            cases: 案件列表（从ds_case_relation表获取，包含合并的xxzjbh和flwsxzdz）

        Returns:
            下载结果统计
        """
        try:
            if not cases:
                return {
                    "status": "error",
                    "message": "没有案件需要下载"
                }

            # 创建下载目录
            download_dir = self.create_download_directory(batch_id)

            # 解析案件数据，创建单个下载任务
            download_tasks = []
            for case_info in cases:
                ajbh = case_info['ajbh']
                xxzjbh_list = case_info['xxzjbh'].split(',') if case_info['xxzjbh'] else []
                flwsxzdz_list = case_info['flwsxzdz'].split(',') if case_info['flwsxzdz'] else []

                # 确保xxzjbh和flwsxzdz数量一致
                if len(xxzjbh_list) != len(flwsxzdz_list):
                    self.logger.warning(f"案件 {ajbh} 的xxzjbh和flwsxzdz数量不匹配")
                    continue

                # 为每个xxzjbh创建下载任务
                for xxzjbh, flwsxzdz in zip(xxzjbh_list, flwsxzdz_list):
                    download_task_info = {
                        'ajbh': ajbh,
                        'xxzjbh': xxzjbh.strip(),
                        'flwsxzdz': flwsxzdz.strip(),
                        'ajmc': case_info.get('ajmc', ''),
                        'ajlx': case_info.get('ajlx', '')
                    }
                    download_tasks.append(download_task_info)

            self.logger.info(f"准备下载 {len(download_tasks)} 个PDF文件")

            # 配置HTTP会话
            timeout = aiohttp.ClientTimeout(total=self.download_timeout)
            connector = aiohttp.TCPConnector(limit=self.system_config['max_concurrent'])

            async with aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            ) as session:

                # 创建下载任务
                tasks = []
                for task_info in download_tasks:
                    task = self.download_single_pdf(session, task_info, download_dir)
                    tasks.append(task)

                # 并发执行下载
                results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            successful_downloads = []
            failed_downloads = []

            for result in results:
                if isinstance(result, Exception):
                    failed_downloads.append({
                        "ajbh": "unknown",
                        "xxzjbh": "unknown",
                        "error": str(result)
                    })
                elif result.get("status") == "success":
                    successful_downloads.append(result)
                else:
                    failed_downloads.append(result)

            # 按案件编号统计
            successful_ajbhs = set()
            failed_ajbhs = set()

            for result in successful_downloads:
                successful_ajbhs.add(result.get("ajbh"))

            for result in failed_downloads:
                failed_ajbhs.add(result.get("ajbh"))

            self.logger.info(f"批量下载完成 - 成功: {len(successful_downloads)} 个文件, 失败: {len(failed_downloads)} 个文件")
            self.logger.info(f"涉及案件 - 成功: {len(successful_ajbhs)} 个, 失败: {len(failed_ajbhs)} 个")

            return {
                "status": "success",
                "batch_id": batch_id,
                "download_dir": download_dir,
                "total_cases": len(cases),
                "total_files": len(download_tasks),
                "successful_files": len(successful_downloads),
                "failed_files": len(failed_downloads),
                "successful_cases": len(successful_ajbhs),
                "failed_cases": len(failed_ajbhs),
                "successful_downloads": successful_downloads,
                "failed_downloads": failed_downloads,
                "summary": f"成功下载 {len(successful_downloads)}/{len(download_tasks)} 个文件，涉及 {len(successful_ajbhs)} 个案件"
            }
            
        except Exception as e:
            self.logger.error(f"批量下载失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "batch_id": batch_id
            }
    
    def update_download_status(self, batch_id: str, successful_ajbhs: List[str], 
                              failed_ajbhs: List[str]) -> Dict[str, Any]:
        """
        更新数据库中的下载状态
        
        Args:
            batch_id: 批次号
            successful_ajbhs: 成功下载的案件编号列表
            failed_ajbhs: 失败的案件编号列表
            
        Returns:
            更新结果
        """
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    
                    # 更新成功下载的记录
                    if successful_ajbhs:
                        success_placeholders = ','.join(['%s'] * len(successful_ajbhs))
                        success_sql = f"""
                        UPDATE `{self.case_relation_table}` 
                        SET status = '1', updatetime = NOW()
                        WHERE batchid = %s AND ajbh IN ({success_placeholders})
                        """
                        cursor.execute(success_sql, [batch_id] + successful_ajbhs)
                    
                    # 更新失败的记录
                    if failed_ajbhs:
                        failed_placeholders = ','.join(['%s'] * len(failed_ajbhs))
                        failed_sql = f"""
                        UPDATE `{self.case_relation_table}` 
                        SET status = '4', error = 'PDF下载失败', updatetime = NOW()
                        WHERE batchid = %s AND ajbh IN ({failed_placeholders})
                        """
                        cursor.execute(failed_sql, [batch_id] + failed_ajbhs)
                    
                    connection.commit()
            
            self.logger.info(f"更新下载状态完成 - 批次: {batch_id}")
            
            return {
                "status": "success",
                "message": "下载状态更新完成"
            }
            
        except Exception as e:
            self.logger.error(f"更新下载状态失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }


async def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    downloader = PDFDownloader()
    
    # 测试数据
    test_cases = [
        {
            'ajbh': 'TEST001',
            'flwsxzdz': 'http://example.com/test.pdf'
        }
    ]
    
    batch_id = "2025073009461901"
    
    # 测试下载
    result = await downloader.batch_download_pdfs(batch_id, test_cases)
    print(f"下载结果: {result}")


if __name__ == "__main__":
    asyncio.run(main())
