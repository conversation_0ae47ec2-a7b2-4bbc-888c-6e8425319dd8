# 分片批处理系统实现总结

## 🎯 问题解决

**原始问题**: 大批量案件（600+）OCR处理超时，导致整批案件状态全部标记为失败（status=4）

**解决方案**: 实现分片批处理系统，将大批次分割成小片批，每个片批独立处理，避免超时问题

## 📋 实现的功能

### ✅ 已完成的核心功能

1. **配置文件增强** (`config.py`)
   - 添加 `batch_slice_size` 配置项，默认值10
   - 支持动态调整分片批大小

2. **PDF分片批分配器** (`pdf_batch_slicer.py`)
   - 自动将PDF文件分配到各个片批目录
   - 创建标准的分片批目录结构
   - 支持从数据库获取案件信息进行分配

3. **OCR处理器增强** (`ocr_processor.py`)
   - 支持分片批模式的目录结构
   - 新增 `process_batch_slice()` 方法处理单个片批
   - 新增 `update_batch_slice_status()` 方法按片批更新状态
   - 支持按案件编号精确更新数据库状态

4. **主控制器增强** (`main_controller.py`)
   - 新增 `process_batch_with_slicing()` 方法
   - 实现增量处理流程：片批OCR完成后立即进行要素提取
   - 支持分片批顺序处理和并行要素提取

5. **测试和验证脚本**
   - `test_slicing_logic.py`: 逻辑验证
   - `verify_batch_slicing_config.py`: 配置验证
   - `run_batch_slicing_example.py`: 使用示例

## 🏗️ 系统架构

### 原始流程
```
数据获取 → PDF下载 → PDF合并 → OCR识别 → 要素提取
```

### 新的分片批流程
```
数据获取 → PDF下载 → PDF合并 → PDF分片批分配 → 
├─ OCR识别(片批1) → 要素提取(片批1)
├─ OCR识别(片批2) → 要素提取(片批2)  
└─ OCR识别(片批3) → 要素提取(片批3)
```

## 📁 目录结构变化

### 原始结构
```
/bigai/ai/AJagent-main/data/
└── {batch_id}/
    ├── input/          # PDF输入目录
    └── output/         # OCR输出目录
```

### 分片批结构
```
/bigai/ai/AJagent-main/data/
└── {batch_id}/
    ├── input/          # 原始输入目录（分配后清空）
    ├── 1/              # 片批1
    │   ├── input/      # 片批1 PDF输入
    │   └── output/     # 片批1 OCR输出
    ├── 2/              # 片批2
    │   ├── input/      # 片批2 PDF输入
    │   └── output/     # 片批2 OCR输出
    └── 3/              # 片批3
        ├── input/      # 片批3 PDF输入
        └── output/     # 片批3 OCR输出
```

## 🔧 核心代码修改

### 1. 配置文件 (`config.py`)
```python
# 新增分片批处理配置
'batch_slice_size': 10,  # 分片批大小，默认10个PDF为一个片批
```

### 2. OCR处理器 (`ocr_processor.py`)
```python
# 支持分片批的目录设置
def setup_ocr_directories(self, batch_id: str, ppid: int = None) -> Dict[str, str]:
    if ppid is not None:
        input_dir = f"{self.ocr_base_path}/{batch_id}/{ppid}/input"
        output_dir = f"{self.ocr_base_path}/{batch_id}/{ppid}/output"
    else:
        # 原始模式（向后兼容）
        input_dir = f"{self.ocr_base_path}/{batch_id}/input"
        output_dir = f"{self.ocr_base_path}/{batch_id}/output"

# 支持分片批的OCR处理
def run_ocr_processing(self, batch_id: str, ppid: int = None) -> Dict[str, Any]:
    if ppid is not None:
        docker_cmd = [
            "docker", "exec", self.docker_container,
            "python", "parse.py", 
            f"./data/{batch_id}/{ppid}/input",
            "-o", f"./data/{batch_id}/{ppid}/output"
        ]

# 分片批状态更新
def update_batch_slice_status(self, batch_id: str, success_ajbh: List[str], error_ajbh: List[str]):
    # 按案件编号精确更新状态
```

### 3. 主控制器 (`main_controller.py`)
```python
# 新的分片批处理主流程
async def process_batch_with_slicing(self, batch_id: str, cases: List[Dict[str, Any]]):
    # 1-2. PDF下载和合并（保持不变）
    # 3. 创建分片批
    batch_slices = self.pdf_batch_slicer.create_batch_slices(batch_id)
    
    # 4. 按顺序处理每个分片批
    for slice_info in batch_slices:
        # 4.1 OCR识别
        ocr_result = self.ocr_processor.process_batch_slice(batch_id, ppid, ajbh_list)
        
        # 4.2 立即进行要素提取（仅处理OCR成功的案件）
        if ocr_result["status"] == "success":
            # 并发处理要素提取
```

## 🚀 使用方法

### 1. 直接使用（推荐）
现有的调用方式无需修改，系统会自动使用分片批处理：

```bash
# 时间范围任务
python main.py --mode time_range --stime '2025-08-01 00:00:00' --etime '2025-08-11 23:59:59'

# 案件编号任务  
python main.py --mode ajbh --ajbh 'CASE123456'
```

### 2. 编程调用
```python
from main_controller import MainController

controller = MainController()

# 使用分片批处理
result = await controller.process_batch_with_slicing(batch_id, cases)
```

### 3. 配置调整
在 `config.py` 中调整分片批大小：
```python
'batch_slice_size': 20,  # 根据系统性能调整
```

## 📊 性能优势

### 原始方式 vs 分片批处理

| 场景 | 原始方式 | 分片批处理 |
|------|----------|------------|
| 600个案件 | 1次OCR调用，容易超时 | 60次OCR调用，每次10个 |
| 超时风险 | 高（整批失败） | 低（单片批失败） |
| 错误恢复 | 困难（需重新处理全部） | 简单（只需重新处理失败片批） |
| 并行度 | 低（串行处理） | 高（OCR串行，要素提取并行） |
| 资源利用 | 低（等待OCR完成） | 高（流水线处理） |

## 🔍 监控和故障排除

### 1. 日志监控
```bash
# 查看主控制器日志
tail -f logs/main_controller_*.log

# 查看分片批处理进度
grep "分片批" logs/main_controller_*.log
```

### 2. 数据库状态查询
```sql
-- 查看批次处理状态
SELECT batchid, status, COUNT(*) as count 
FROM ds_case_relation 
GROUP BY batchid, status 
ORDER BY batchid DESC;

-- 查看特定批次的详细状态
SELECT ajbh, status, updatetime 
FROM ds_case_relation 
WHERE batchid = 'your_batch_id' 
ORDER BY ajbh;
```

### 3. 手动恢复失败片批
```python
from ocr_processor import OCRProcessor

processor = OCRProcessor()

# 重新处理特定片批
batch_id = "your_batch_id"
ppid = 2  # 失败的片批ID
ajbh_list = ["CASE001", "CASE002", "CASE003"]  # 该片批的案件编号

result = processor.process_batch_slice(batch_id, ppid, ajbh_list)
print(result)
```

## ⚠️ 注意事项

1. **向后兼容**: 系统完全向后兼容，原有代码无需修改
2. **配置调整**: 根据服务器性能调整 `batch_slice_size`
3. **磁盘空间**: 分片批会创建更多目录，注意磁盘空间
4. **错误处理**: 单片批失败不影响其他片批，提高了系统稳定性

## 🎉 预期效果

1. **解决超时问题**: 600个案件分成60个片批，每个片批10个案件，大大降低超时风险
2. **提高处理效率**: OCR串行处理，要素提取并行处理，提高整体效率
3. **增强错误恢复**: 支持片批级别的错误恢复，减少重新处理的工作量
4. **改善监控**: 更细粒度的处理状态，便于监控和调试

---

**实施建议**: 
1. 先在测试环境验证功能
2. 从小批量开始测试（50-100个案件）
3. 逐步增加到大批量（600+个案件）
4. 根据实际性能调整 `batch_slice_size` 配置
